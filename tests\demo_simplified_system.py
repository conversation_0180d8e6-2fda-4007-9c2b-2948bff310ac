#!/usr/bin/env python3
# coding: utf-8
"""
简化后的天气数据定时任务调度器演示
展示基于uptime状态管理的智能调度系统
"""

import asyncio
import sys
from pathlib import Path
import logging
from datetime import datetime

# 添加src目录到Python路径
src_path = Path(__file__).parent / "src"
sys.path.insert(0, str(src_path))

# 导入时间工具
from time_utils import get_local_now

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

logger = logging.getLogger(__name__)


def demo_system_overview():
    """演示系统概览"""
    logger.info("=== 天气数据定时任务调度器系统概览 ===")
    
    logger.info("🎯 核心特性:")
    logger.info("  ✅ 基于uptime的智能状态管理")
    logger.info("  ✅ 防重复处理机制")
    logger.info("  ✅ 5个独立任务状态跟踪")
    logger.info("  ✅ 精确的调度时间控制")
    logger.info("  ✅ API重试和错误处理")
    
    logger.info("\n📅 调度时间:")
    logger.info("  🕐 1小时任务: 每小时第4分钟 (01:04, 02:04, 03:04...)")
    logger.info("  🕕 6分钟任务: 4,10,16,22,28,34,40,46,52,58分钟")
    
    logger.info("\n📊 任务类型:")
    logger.info("  🌡️  1小时任务: PRE(降水), TEM(温度), WEATHER(天气), VIS(能见度)")
    logger.info("  🌧️  6分钟任务: gz_mpfv3(降水数据)")
    
    logger.info("\n🧠 智能处理:")
    logger.info("  📝 内存中记录每个任务的最后处理uptime")
    logger.info("  🔄 相同uptime数据自动跳过")
    logger.info("  🆕 新uptime数据自动处理")
    logger.info("  🚀 首次运行直接执行")


def demo_scheduling_logic():
    """演示调度逻辑"""
    logger.info("=== 演示调度逻辑 ===")
    
    try:
        from scheduler import WeatherTaskScheduler
        
        scheduler = WeatherTaskScheduler()
        current_time = get_local_now()
        
        # 计算下次调度时间
        next_hourly = scheduler._get_next_hourly_schedule(current_time)
        next_six_minute = scheduler._get_next_six_minute_schedule(current_time)
        
        logger.info(f"⏰ 当前时间: {current_time.strftime('%Y-%m-%d %H:%M:%S')}")
        logger.info(f"🕐 下次1小时任务: {next_hourly.strftime('%Y-%m-%d %H:%M:%S')}")
        logger.info(f"🕕 下次6分钟任务: {next_six_minute.strftime('%Y-%m-%d %H:%M:%S')}")
        
        # 计算等待时间
        wait_hourly = (next_hourly - current_time).total_seconds()
        wait_six_minute = (next_six_minute - current_time).total_seconds()
        
        logger.info(f"⏳ 距离1小时任务: {wait_hourly:.0f}秒 ({wait_hourly/60:.1f}分钟)")
        logger.info(f"⏳ 距离6分钟任务: {wait_six_minute:.0f}秒 ({wait_six_minute/60:.1f}分钟)")
        
        return True
        
    except Exception as e:
        logger.error(f"演示调度逻辑失败: {e}")
        return False


def demo_uptime_processing():
    """演示uptime处理逻辑"""
    logger.info("=== 演示uptime处理逻辑 ===")
    
    try:
        from task_state_manager import task_state_manager, should_process_task, mark_task_completed
        from scheduler import WeatherTaskScheduler
        
        # 重置状态
        task_state_manager.reset_all_states()
        scheduler = WeatherTaskScheduler()
        
        # 模拟API返回的uptime数据
        test_scenarios = [
            {
                "name": "首次运行",
                "uptime": "202507131304",
                "description": "系统首次启动，内存为空"
            },
            {
                "name": "重复数据",
                "uptime": "202507131304", 
                "description": "相同uptime，应该跳过"
            },
            {
                "name": "新数据",
                "uptime": "202507131310",
                "description": "新的uptime，应该处理"
            },
            {
                "name": "旧数据",
                "uptime": "202507131305",
                "description": "比已处理数据更旧，应该跳过"
            }
        ]
        
        task_type = "1h"
        data_type = "PRE"
        
        logger.info(f"🎯 测试任务: {task_type}_{data_type}")
        
        for i, scenario in enumerate(test_scenarios, 1):
            logger.info(f"\n--- 场景 {i}: {scenario['name']} ---")
            logger.info(f"📝 描述: {scenario['description']}")
            
            raw_uptime = scenario['uptime']
            formatted_uptime = scheduler._format_uptime(raw_uptime)
            logger.info(f"📅 API uptime: {raw_uptime} -> {formatted_uptime}")
            
            should_process = should_process_task(task_type, data_type, formatted_uptime)
            logger.info(f"🤔 是否处理: {'✅ 是' if should_process else '❌ 否'}")
            
            if should_process:
                logger.info("🚀 开始处理数据...")
                mark_task_completed(task_type, data_type, formatted_uptime)
                logger.info("✅ 处理完成")
            else:
                logger.info("⏭️  跳过处理")
        
        # 显示最终状态
        logger.info(f"\n📊 {task_type}_{data_type} 最终状态:")
        state = task_state_manager.get_task_state(task_type, data_type)
        if state:
            logger.info(f"  📝 最后uptime: {state.last_uptime}")
            logger.info(f"  🔢 执行次数: {state.total_executions}")
        
        return True
        
    except Exception as e:
        logger.error(f"演示uptime处理失败: {e}")
        return False


def demo_api_integration():
    """演示API集成"""
    logger.info("=== 演示API集成 ===")
    
    try:
        from uptime_checker import UptimeChecker
        
        # 创建检查器（不实际调用API）
        checker = UptimeChecker("gz_mpfv3")
        
        logger.info("🔧 API配置:")
        logger.info(f"  📡 基础URL: {checker.api_base_url}")
        logger.info(f"  🔑 API密钥: {checker.api_key[:10]}...")
        logger.info(f"  🔄 重试间隔: {checker.retry_interval_seconds}秒")
        logger.info(f"  ⏰ 最大重试时长: {checker.max_retry_duration_minutes}分钟")
        
        logger.info("\n📋 API调用流程:")
        logger.info("  1️⃣ 调用API获取数据信息")
        logger.info("  2️⃣ 解析返回的uptime字段")
        logger.info("  3️⃣ 检查是否已处理过该uptime")
        logger.info("  4️⃣ 如果未处理，开始下载和处理")
        logger.info("  5️⃣ 处理完成后更新状态记录")
        
        logger.info("\n🔄 重试机制:")
        logger.info("  ❌ API调用失败 → 等待30秒 → 重试")
        logger.info("  ⏰ 最多重试60分钟")
        logger.info("  ✅ 成功获取数据 → 继续处理")
        
        return True
        
    except Exception as e:
        logger.error(f"演示API集成失败: {e}")
        return False


def demo_system_benefits():
    """演示系统优势"""
    logger.info("=== 系统优势 ===")
    
    logger.info("🎯 相比传统定时任务的优势:")
    
    logger.info("\n💡 智能化:")
    logger.info("  ✅ 基于数据实际更新时间，而非固定时间间隔")
    logger.info("  ✅ 自动跳过已处理数据，避免重复工作")
    logger.info("  ✅ 首次运行自动检测，无需手动配置")
    
    logger.info("\n🛡️ 可靠性:")
    logger.info("  ✅ API调用失败自动重试")
    logger.info("  ✅ 任务超时自动终止")
    logger.info("  ✅ 状态持久化在内存中")
    
    logger.info("\n⚡ 效率:")
    logger.info("  ✅ 避免重复下载相同数据")
    logger.info("  ✅ 精确的调度时间控制")
    logger.info("  ✅ 资源池统一管理")
    
    logger.info("\n🔧 可维护性:")
    logger.info("  ✅ 统一配置管理")
    logger.info("  ✅ 详细的状态监控")
    logger.info("  ✅ RESTful API接口")
    
    logger.info("\n📊 监控能力:")
    logger.info("  ✅ 实时任务状态查看")
    logger.info("  ✅ 执行统计和历史记录")
    logger.info("  ✅ Web界面监控")


async def main():
    """主演示函数"""
    logger.info("🎭 天气数据定时任务调度器 - 简化系统演示")
    logger.info("=" * 60)
    
    demos = [
        ("系统概览", demo_system_overview),
        ("调度逻辑", demo_scheduling_logic),
        ("uptime处理逻辑", demo_uptime_processing),
        ("API集成", demo_api_integration),
        ("系统优势", demo_system_benefits),
    ]
    
    results = []
    for demo_name, demo_func in demos:
        try:
            logger.info(f"\n🎬 {demo_name}")
            logger.info("-" * 40)
            result = demo_func()
            results.append((demo_name, result))
            
        except Exception as e:
            logger.error(f"❌ {demo_name} 演示异常: {e}")
            results.append((demo_name, False))
    
    # 汇总结果
    logger.info("\n" + "=" * 60)
    logger.info("📊 演示结果:")
    
    all_passed = True
    for demo_name, passed in results:
        if passed is not None:
            status = "✅ 成功" if passed else "❌ 失败"
            logger.info(f"  {demo_name}: {status}")
            if not passed:
                all_passed = False
        else:
            logger.info(f"  {demo_name}: ℹ️  信息展示")
    
    logger.info("=" * 60)
    if all_passed:
        logger.info("🎉 系统演示完成！")
        logger.info("")
        logger.info("🚀 启动完整系统:")
        logger.info("   uv run python run_weather_scheduler.py")
        logger.info("")
        logger.info("🌐 监控界面:")
        logger.info("   http://localhost:8000")
        logger.info("   http://localhost:8000/task-states")
        logger.info("")
        logger.info("💡 关键改进:")
        logger.info("   ✅ 移除了不必要的delay_minutes配置")
        logger.info("   ✅ 移除了max_uptime_minutes检查")
        logger.info("   ✅ 基于uptime状态的智能处理")
        logger.info("   ✅ 调度时间已包含4分钟延迟")
        logger.info("   ✅ 简化的配置和更清晰的逻辑")
    else:
        logger.error("💥 部分演示失败！")


if __name__ == "__main__":
    asyncio.run(main())
