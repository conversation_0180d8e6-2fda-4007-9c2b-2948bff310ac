#!/usr/bin/env python3
# coding: utf-8
"""
测试uptime字段修复
验证API响应解析现在使用'uptime'字段而不是'time'字段
"""

import logging
import sys
from pathlib import Path

# 添加src目录到Python路径
src_path = Path(__file__).parent / "src"
sys.path.insert(0, str(src_path))

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


def test_api_response_parsing():
    """测试API响应解析是否正确使用uptime字段"""
    logger.info("=== 测试API响应解析修复 ===")
    
    # 模拟用户提供的API响应格式
    mock_api_response = {
        "result": {
            "time": "2025071308",
            "url": "http://filed.weatherdt.com/grid-file-download/down?filename=br2MffttE5E2f3Ef3MfAEtt7AtbfzAC9.tar.gz",
            "md5": "d5c4e010cebf856786565927cc75009d",
            "uptime": "202507131904"
        },
        "status": "success",
        "errorMsg": ""
    }
    
    # 测试weather_download.py中的解析
    logger.info("测试weather_download.py中的API响应解析...")
    try:
        from weather_download import WeatherDownloader
        
        downloader = WeatherDownloader("gz_mpfv3")
        
        # 模拟download_from_api方法中的解析逻辑
        result = mock_api_response["result"]
        file_time = result.get('uptime', '')  # 应该使用uptime而不是time
        
        logger.info(f"✓ 提取的uptime: {file_time}")
        logger.info(f"✓ 正确值应该是: {mock_api_response['result']['uptime']}")
        
        if file_time == mock_api_response['result']['uptime']:
            logger.info("✅ weather_download.py 正确使用了uptime字段")
        else:
            logger.error("❌ weather_download.py 仍在使用错误的字段")
            return False
            
    except Exception as e:
        logger.error(f"测试weather_download.py失败: {e}")
        return False
    
    # 测试uptime_checker.py中的解析
    logger.info("测试uptime_checker.py中的API响应解析...")
    try:
        # 模拟get_api_data方法中的解析逻辑
        api_result = mock_api_response["result"]
        file_time = api_result.get('uptime', '')  # 应该使用uptime而不是time
        
        logger.info(f"✓ 提取的uptime: {file_time}")
        
        if file_time == mock_api_response['result']['uptime']:
            logger.info("✅ uptime_checker.py 正确使用了uptime字段")
        else:
            logger.error("❌ uptime_checker.py 仍在使用错误的字段")
            return False
            
    except Exception as e:
        logger.error(f"测试uptime_checker.py失败: {e}")
        return False
    
    # 测试scheduler.py中的解析
    logger.info("测试scheduler.py中的API响应解析...")
    try:
        # 模拟scheduler中的解析逻辑
        api_result = mock_api_response["result"]
        current_uptime = api_result.get('uptime', '')  # 应该使用uptime而不是time
        
        logger.info(f"✓ 提取的uptime: {current_uptime}")
        
        if current_uptime == mock_api_response['result']['uptime']:
            logger.info("✅ scheduler.py 正确使用了uptime字段")
        else:
            logger.error("❌ scheduler.py 仍在使用错误的字段")
            return False
            
    except Exception as e:
        logger.error(f"测试scheduler.py失败: {e}")
        return False
    
    logger.info("🎉 所有API响应解析都已正确修复！")
    return True


def test_old_vs_new_behavior():
    """演示修复前后的行为差异"""
    logger.info("=== 演示修复前后的行为差异 ===")
    
    mock_api_response = {
        "result": {
            "time": "2025071308",        # 错误的时间值
            "url": "http://example.com/file.tar.gz",
            "md5": "d5c4e010cebf856786565927cc75009d",
            "uptime": "202507131904"     # 正确的uptime值
        },
        "status": "success",
        "errorMsg": ""
    }
    
    result = mock_api_response["result"]
    
    # 旧的错误行为
    old_time = result.get('time', '')
    logger.info(f"❌ 修复前（错误）: 使用'time'字段 = {old_time}")
    
    # 新的正确行为
    new_uptime = result.get('uptime', '')
    logger.info(f"✅ 修复后（正确）: 使用'uptime'字段 = {new_uptime}")
    
    logger.info(f"📊 差异: {old_time} -> {new_uptime}")
    logger.info("🔧 这个修复解决了一直尝试新数据的问题")
    
    return True


if __name__ == "__main__":
    logger.info("开始测试uptime字段修复...")
    
    success = True
    
    # 运行测试
    if not test_api_response_parsing():
        success = False
    
    if not test_old_vs_new_behavior():
        success = False
    
    if success:
        logger.info("🎉 所有测试通过！uptime字段修复成功。")
        logger.info("💡 现在系统会正确使用API响应中的'uptime'字段，而不是'time'字段。")
        logger.info("🚀 这应该解决了一直尝试新数据的问题。")
    else:
        logger.error("❌ 测试失败！请检查修复是否正确应用。")
        sys.exit(1)
