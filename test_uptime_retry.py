#!/usr/bin/env python3
# coding: utf-8
"""
测试新的uptime重试逻辑
验证如果uptime时间不是新的，系统会每隔30秒重新获取API
"""

import asyncio
import logging
import sys
from pathlib import Path

# 添加src目录到Python路径
src_path = Path(__file__).parent / "src"
sys.path.insert(0, str(src_path))

from uptime_checker import check_and_get_fresh_data, UptimeChecker
from task_state_manager import task_state_manager, mark_task_started, mark_task_completed, should_process_task
from config import SCHEDULER_RETRY_UNTIL_FRESH

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


async def test_uptime_retry_logic():
    """测试uptime重试逻辑"""
    logger.info("=== 测试uptime重试逻辑 ===")
    
    # 显示当前配置
    logger.info(f"retry_until_fresh配置: {SCHEDULER_RETRY_UNTIL_FRESH}")
    
    try:
        # 1. 重置任务状态
        task_state_manager.reset_all_states()
        logger.info("✓ 已重置所有任务状态")
        
        # 2. 模拟首次运行（应该立即返回）
        logger.info("\n--- 测试场景1: 首次运行 ---")
        api_result = await check_and_get_fresh_data("gz_mpfv3", "6m", None)
        if api_result:
            uptime = api_result.get('uptime', '')
            logger.info(f"✓ 首次运行成功获取数据，uptime: {uptime}")

            # 测试新逻辑：先标记开始，再标记完成
            logger.info("测试新逻辑：先标记任务开始处理...")
            mark_task_started("6m", "gz_mpfv3", uptime)
            logger.info(f"✓ 已标记任务开始: {uptime}")

            # 模拟处理过程
            logger.info("模拟数据处理过程...")
            await asyncio.sleep(1)  # 模拟处理时间

            # 标记任务完成
            mark_task_completed("6m", "gz_mpfv3", uptime)
            logger.info(f"✓ 已标记任务完成: {uptime}")
        else:
            logger.error("✗ 首次运行失败")
            return False
        
        # 3. 测试重复uptime（应该重试）
        logger.info("\n--- 测试场景2: 重复uptime（会重试直到新数据） ---")
        logger.info("注意：这个测试会持续重试直到API返回新的uptime或超时")
        logger.info("如果API一直返回相同的uptime，测试会在60分钟后超时")
        
        # 获取当前任务状态
        task_state = task_state_manager.get_task_state("6m", "gz_mpfv3")
        last_uptime = task_state.last_uptime if task_state else None
        
        logger.info(f"当前已处理的uptime: {last_uptime}")
        logger.info("开始重试逻辑测试（最多等待5分钟）...")
        
        # 为了测试，我们临时修改最大重试时间为5分钟
        original_max_retry = None
        try:
            checker = UptimeChecker("gz_mpfv3")
            original_max_retry = checker.max_retry_duration_minutes
            checker.max_retry_duration_minutes = 5  # 5分钟用于测试
            
            api_result = await check_and_get_fresh_data("gz_mpfv3", "6m", last_uptime)
            
            if api_result:
                new_uptime = api_result.get('uptime', '')
                logger.info(f"✓ 获取到新数据，uptime: {new_uptime}")
                if new_uptime != last_uptime:
                    logger.info("✓ 确认获取到了新的uptime")
                else:
                    logger.warning("⚠ 获取到的uptime与之前相同，可能API数据未更新")
            else:
                logger.warning("⚠ 重试测试超时（5分钟），这是正常的，说明API一直返回相同的uptime")
                
        finally:
            # 恢复原始配置
            if original_max_retry:
                checker.max_retry_duration_minutes = original_max_retry
        
        # 4. 测试新逻辑：检查uptime状态变化
        logger.info("\n--- 测试场景3: 验证uptime状态变化 ---")

        # 获取当前状态
        task_state = task_state_manager.get_task_state("6m", "gz_mpfv3")
        current_uptime = task_state.last_uptime if task_state else None
        logger.info(f"当前任务状态中的uptime: {current_uptime}")

        # 测试should_process_task函数
        test_uptime = "202507131820"  # 一个未来的uptime
        should_process = should_process_task("6m", "gz_mpfv3", test_uptime)
        logger.info(f"是否应该处理uptime {test_uptime}: {should_process}")

        if should_process:
            logger.info("✓ 新uptime检测正常")

            # 模拟新的处理逻辑
            logger.info("模拟新的处理逻辑：立即标记开始...")
            mark_task_started("6m", "gz_mpfv3", test_uptime)

            # 检查状态是否立即更新
            updated_state = task_state_manager.get_task_state("6m", "gz_mpfv3")
            if updated_state and updated_state.last_uptime == test_uptime:
                logger.info("✓ uptime状态立即更新成功")

                # 再次检查是否应该处理相同的uptime
                should_process_again = should_process_task("6m", "gz_mpfv3", test_uptime)
                if not should_process_again:
                    logger.info("✓ 相同uptime不会被重复处理")
                else:
                    logger.warning("⚠ 相同uptime仍然会被处理，这可能是问题")
            else:
                logger.error("✗ uptime状态更新失败")
        else:
            logger.warning("⚠ 新uptime检测异常")

        # 5. 测试禁用重试功能
        logger.info("\n--- 测试场景4: 禁用重试功能 ---")
        
        # 临时修改配置
        import config
        original_retry_until_fresh = config.SCHEDULER_RETRY_UNTIL_FRESH
        config.SCHEDULER_RETRY_UNTIL_FRESH = False
        
        try:
            api_result = await check_and_get_fresh_data("gz_mpfv3", "6m", last_uptime)
            if api_result:
                logger.info("✓ 禁用重试功能时，立即返回API结果")
            else:
                logger.warning("⚠ 禁用重试功能时，API调用失败")
        finally:
            # 恢复原始配置
            config.SCHEDULER_RETRY_UNTIL_FRESH = original_retry_until_fresh
        
        logger.info("\n=== 测试完成 ===")
        return True
        
    except Exception as e:
        logger.error(f"测试过程中发生错误: {e}")
        return False


async def test_api_basic_functionality():
    """测试API基本功能"""
    logger.info("=== 测试API基本功能 ===")
    
    try:
        # 测试不同数据类型的API调用
        data_types = [
            "gz_mpfv3",
            "gz_didiforecast1hPRE",
            "gz_didiforecast1hTEM",
            "gz_didiforecast1hWEATHER",
            "gz_didiforecast1hVIS"
        ]
        
        for data_type in data_types:
            logger.info(f"\n--- 测试数据类型: {data_type} ---")
            
            checker = UptimeChecker(data_type)
            api_result = await checker.get_api_data()
            
            if api_result:
                uptime = api_result.get('uptime', '')
                url = api_result.get('url', '')
                md5 = api_result.get('md5', '')
                
                logger.info(f"✓ API调用成功")
                logger.info(f"  uptime: {uptime}")
                logger.info(f"  URL: {url[:50]}..." if url else "  URL: 无")
                logger.info(f"  MD5: {md5}")
            else:
                logger.warning(f"⚠ API调用失败: {data_type}")
        
        return True
        
    except Exception as e:
        logger.error(f"API测试过程中发生错误: {e}")
        return False


async def main():
    """主函数"""
    logger.info("开始测试uptime重试逻辑")
    
    # 测试API基本功能
    api_test_result = await test_api_basic_functionality()
    
    if api_test_result:
        # 测试重试逻辑
        retry_test_result = await test_uptime_retry_logic()
        
        if retry_test_result:
            logger.info("🎉 所有测试通过！")
        else:
            logger.error("❌ 重试逻辑测试失败")
    else:
        logger.error("❌ API基本功能测试失败，跳过重试逻辑测试")


if __name__ == "__main__":
    asyncio.run(main())
