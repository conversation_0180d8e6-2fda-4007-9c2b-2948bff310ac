# Weather Script 项目

## 项目概述

这是一个天气数据处理和分析项目，包含天气数据下载、处理、存储和分析功能。项目使用现代Python技术栈，支持异步处理、地理空间数据分析和多种数据库操作。

## 功能特性

- 🌦️ **天气数据下载**: 支持多种天气数据类型的自动下载和处理
- 🗃️ **数据库集成**: 支持PostgreSQL和MySQL数据库操作
- 🌍 **地理空间分析**: 集成GIS功能，支持地理空间数据处理
- ⚡ **异步处理**: 使用异步编程提高数据处理效率
- 🔧 **配置管理**: 灵活的配置系统，支持YAML配置文件
- 📊 **数据分析**: 支持NetCDF文件处理和多维数组分析

## 项目结构

```
weather_script/
├── build_lookup.py              # 构建查找表
├── config.py                    # 统一配置管理
├── config.yml                   # 配置文件
├── create_forecast_tables.py    # 创建预报数据表
├── weather_cell_1h_async_hybrid.py   # 1小时天气数据处理
├── weather_cell_6m_async_hybrid.py   # 6分钟天气数据处理
├── weather_download.py          # 天气数据下载模块
├── pyproject.toml              # 项目配置和依赖
├── test_dependencies.py        # 依赖测试脚本
├── DEPENDENCIES.md             # 依赖说明文档
└── README.md                   # 项目说明文档
```

## 快速开始

### 环境要求

- Python 3.12+
- uv 包管理器

### 安装步骤

1. **克隆项目**（如果适用）或进入项目目录：
   ```bash
   cd weather_script
   ```

2. **安装uv**（如果尚未安装）：
   ```bash
   pip install uv
   ```

3. **安装项目依赖**：
   ```bash
   uv sync
   ```

4. **验证安装**：
   ```bash
   uv run python test_dependencies.py
   ```

### GDAL依赖安装

由于项目使用地理空间数据处理，需要手动安装GDAL：

**推荐方法（使用Conda）**：
```bash
conda install -c conda-forge gdal fiona
```

**其他方法**：
- Windows: 使用OSGeo4W或GDAL官方安装包
- Ubuntu/Debian: `sudo apt-get install gdal-bin libgdal-dev`
- macOS: `brew install gdal`

然后安装Python绑定：
```bash
uv add gdal fiona
```

## 配置

### 数据库配置

编辑 `config.yml` 文件配置数据库连接：

```yaml
database:
  postgres:
    host: "your_postgres_host"
    port: 5432
    database: "your_database"
    username: "your_username"
    password: "your_password"
  
  mysql:
    host: "your_mysql_host"
    port: 3306
    username: "your_username"
    password: "your_password"
    database: "your_database"
```

### 路径配置

配置数据文件路径：

```yaml
paths:
  weather_dir: "./data/weather"
  weather_mpf_dir: "./data/weather/MPF"
  weather_backup_dir: "./data/weather/backup"
  gis_route_shape: "./data/gis_route_shape/gis_route_shape.shp"
```

## 使用方法

### 运行脚本

使用uv运行项目脚本：

```bash
# 运行天气数据下载
uv run python weather_download.py

# 运行1小时天气数据处理
uv run python weather_cell_1h_async_hybrid.py

# 运行6分钟天气数据处理
uv run python weather_cell_6m_async_hybrid.py

# 构建查找表
uv run python build_lookup.py

# 创建预报表
uv run python create_forecast_tables.py
```

### 开发模式

激活开发依赖：
```bash
uv sync --group dev
```

运行代码检查：
```bash
uv run ruff check .
```

运行代码格式化：
```bash
uv run ruff format .
```

## 主要模块说明

### weather_download.py
天气数据下载模块，支持：
- 多种数据类型下载
- 自动解压和文件管理
- 备份机制

### config.py
统一配置管理，提供：
- 数据库连接配置
- 文件路径配置
- 天气数据处理参数

### weather_cell_*_async_hybrid.py
天气数据处理模块，特点：
- 混合架构（多进程+协程）
- 高效的NetCDF数据处理
- 异步数据库操作

### build_lookup.py
构建地理空间查找表，功能：
- GIS数据处理
- 空间索引构建
- 数据库表创建

## 依赖管理

项目使用uv进行依赖管理，主要依赖包括：

- **数据处理**: pandas, numpy, xarray
- **地理空间**: geopandas, shapely, pyproj
- **数据库**: sqlalchemy, asyncpg, pymysql
- **网络**: requests
- **配置**: pyyaml

详细依赖信息请参考 [DEPENDENCIES.md](DEPENDENCIES.md)。

## 测试

运行完整的依赖测试：
```bash
uv run python test_dependencies.py
```

## 故障排除

### 常见问题

1. **GDAL导入错误**
   - 确保GDAL正确安装
   - 检查环境变量设置

2. **数据库连接失败**
   - 检查配置文件中的连接参数
   - 确认数据库服务正在运行

3. **模块导入错误**
   - 运行 `uv sync` 重新安装依赖
   - 检查虚拟环境是否正确激活

### 获取帮助

如果遇到问题：
1. 查看错误日志
2. 运行依赖测试脚本
3. 检查配置文件
4. 确认所有依赖正确安装

## 贡献

欢迎贡献代码！请确保：
1. 代码通过ruff检查
2. 添加适当的测试
3. 更新相关文档

## 许可证

[在此添加许可证信息]

---

**注意**: 本项目需要GDAL库支持，请确保在运行前正确安装GDAL相关依赖。
