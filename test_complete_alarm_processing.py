#!/usr/bin/env python3
# coding: utf-8
"""
完整的预警处理功能测试脚本
包含预警内容解析和数据库存储
"""

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

import asyncio
import json
from datetime import datetime
from src.weather_alarm import WeatherAlarmProcessor

async def test_complete_processing():
    """测试完整的预警处理流程"""
    
    processor = WeatherAlarmProcessor()
    
    try:
        # 初始化处理器
        await processor.initialize()
        
        print("完整预警处理功能测试")
        print("=" * 60)
        
        # 模拟API返回的预警数据
        mock_alarm_data = {
            "alarm": {
                "12345": {  # 假设的weather_id
                    "1001003": [
                        {
                            "001": "云南省",  # 省级名称
                            "002": "普洱市",  # 市级名称
                            "003": "墨江县",  # 县级名称
                            "004": "11B17",   # 预警类别编号
                            "005": "大雾",    # 预警类别名称
                            "006": "03",      # 预警级别编号
                            "007": "黄色",    # 预警级别名称
                            "008": "2025-07-18 07:05",  # 预警发布时间
                            "009": "墨江县气象台2025年7月18日07时05分发布大雾黄色预警信号：未来12小时墨江县大部乡镇将出现浓雾，请做好防护。（预警信息来源：国家预警信息发布中心）",  # 预警发布内容
                            "010": "墨江县大雾黄色预警信号",  # 预警信息
                            "011": "http://www.weather.com.cn/alarm/12345",  # 跳转地址
                            "012": "墨江县发布大雾黄色预警",  # 预警标题
                            "013": "Alert"    # 发布状态
                        },
                        {
                            "001": "云南省",
                            "002": "曲靖市",
                            "003": "会泽县",
                            "004": "11B04",
                            "005": "高温",
                            "006": "03",
                            "007": "黄色",
                            "008": "2025-07-17 16:25",
                            "009": "会泽县气象台2025年7月17日16时25分发布高温黄色预警信号：未来3天娜姑镇、纸厂乡的低矮河谷地区最高气温将在35℃以上，请注意防范。（预警信息来源：国家预警信息发布中心）",
                            "010": "会泽县高温黄色预警信号",
                            "011": "http://www.weather.com.cn/alarm/12346",
                            "012": "会泽县发布高温黄色预警",
                            "013": "Alert"
                        }
                    ]
                },
                "12346": {  # 另一个weather_id
                    "1001003": [
                        {
                            "001": "云南省",
                            "002": "昭通市",
                            "003": "彝良县",
                            "004": "11B04",
                            "005": "高温",
                            "006": "02",
                            "007": "橙色",
                            "008": "2025-07-18 09:00",
                            "009": "彝良县气象台2025年7月18日9时00分继续发布高温橙色预警信号：未来24小时，彝良县洛旺、柳溪、牛街、角奎、发界、海子、洛泽河、两河等乡镇（街道）海拔1200米以下低矮河谷区域日最高气温将升至37℃以上，午后请减少户外活动。（预警信息来源：国家预警信息发布中心）",
                            "010": "彝良县高温橙色预警信号",
                            "011": "http://www.weather.com.cn/alarm/12347",
                            "012": "彝良县发布高温橙色预警",
                            "013": "Alert"
                        }
                    ]
                }
            }
        }
        
        # 模拟weather_id映射
        weather_id_mapping = {
            12345: "530800",  # 墨江县对应的region_code
            12346: "530600"   # 彝良县对应的region_code
        }
        
        print("1. 开始处理模拟预警数据...")
        print(f"   包含 {len(mock_alarm_data['alarm'])} 个weather_id的预警信息")
        
        # 处理预警数据
        await processor.process_alarm_data(mock_alarm_data, weather_id_mapping)
        
        print("2. 预警数据处理完成")
        
        # 验证数据库中的数据
        print("\n3. 验证数据库中的预警记录...")
        await verify_database_records(processor)
        
        print("\n4. 测试预警内容解析功能...")
        await test_content_parsing(processor)
        
        print("\n" + "=" * 60)
        print("完整测试完成")
        
    except Exception as e:
        print(f"测试失败: {e}")
        import traceback
        traceback.print_exc()
    finally:
        await processor.close()

async def verify_database_records(processor):
    """验证数据库中的预警记录"""
    try:
        async with processor.pg_pool.acquire() as conn:
            # 查询最近的预警记录
            rows = await conn.fetch("""
                SELECT weather_id, region_code, county_name, sub_code, sub_name, 
                       alarm_category_name, alarm_level_name, alarm_content
                FROM weather_alarm 
                WHERE create_time >= CURRENT_DATE
                ORDER BY create_time DESC
                LIMIT 10
            """)
            
            print(f"   找到 {len(rows)} 条今日预警记录:")
            for i, row in enumerate(rows, 1):
                print(f"   {i}. weather_id={row['weather_id']}, "
                      f"区域={row['county_name']}, "
                      f"类型={row['alarm_category_name']}, "
                      f"级别={row['alarm_level_name']}")
                if row['sub_code']:
                    print(f"      子区域代码: {row['sub_code']}")
                if row['sub_name']:
                    print(f"      子区域名称: {row['sub_name']}")
                print(f"      内容: {row['alarm_content'][:100]}...")
                print()
                
    except Exception as e:
        print(f"   验证数据库记录失败: {e}")

async def test_content_parsing(processor):
    """测试预警内容解析功能"""
    try:
        # 测试用例
        test_contents = [
            ("墨江县气象台2025年7月18日07时05分发布大雾黄色预警信号：未来12小时墨江县大部乡镇将出现浓雾，请做好防护。", "530800"),
            ("会泽县气象台2025年7月17日16时25分发布高温黄色预警信号：未来3天娜姑镇、纸厂乡的低矮河谷地区最高气温将在35℃以上，请注意防范。", "530300"),
            ("彝良县气象台2025年7月18日9时00分继续发布高温橙色预警信号：未来24小时，彝良县洛旺、柳溪、牛街、角奎、发界、海子、洛泽河、两河等乡镇（街道）海拔1200米以下低矮河谷区域日最高气温将升至37℃以上，午后请减少户外活动。", "530600"),
            ("绥江县气象台2025年7月18日08时34分发布高温橙色预警信号：未来24小时，我县南岸镇、板栗镇、中城镇、新滩镇、会仪镇最高气温将升至37℃以上，午后请减少户外活动。", "530600")
        ]
        
        # 获取区域数据
        geo_codes = [geo_code for _, geo_code in test_contents]
        region_data = processor.content_parser.get_region_data_by_geo_codes(geo_codes)
        
        for i, (content, geo_code) in enumerate(test_contents, 1):
            print(f"   测试 {i}: {content[:50]}...")
            
            # 解析内容
            results = processor.content_parser.parse_alarm_content(content, geo_code, region_data)
            
            print(f"   解析结果: {len(results)} 个匹配")
            for j, result in enumerate(results, 1):
                print(f"     {j}. {result['data_name']} ({result['data_code']})")
            print()
            
    except Exception as e:
        print(f"   内容解析测试失败: {e}")

if __name__ == "__main__":
    asyncio.run(test_complete_processing())
