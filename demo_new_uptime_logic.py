#!/usr/bin/env python3
# coding: utf-8
"""
演示新的uptime处理逻辑
展示关键改进：在下载任务开始时就更新uptime，而不是完成后才更新
"""

import asyncio
import logging
import sys
from pathlib import Path

# 添加src目录到Python路径
src_path = Path(__file__).parent / "src"
sys.path.insert(0, str(src_path))

from task_state_manager import task_state_manager, mark_task_started, mark_task_completed, should_process_task

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


def demo_old_vs_new_logic():
    """演示旧逻辑 vs 新逻辑的区别"""
    logger.info("=== 演示旧逻辑 vs 新逻辑的区别 ===")
    
    # 重置状态
    task_state_manager.reset_all_states()
    
    test_uptime = "202507131820"
    
    logger.info("\n🔴 旧逻辑（有问题的方式）:")
    logger.info("1. 获取API数据，uptime: 202507131820")
    logger.info("2. 检查是否需要处理 -> 是（首次运行）")
    logger.info("3. 开始下载和处理...")
    logger.info("4. 如果处理失败 -> uptime状态没有更新")
    logger.info("5. 下次执行时，还会处理相同的uptime: 202507131820")
    logger.info("   ❌ 问题：同一个uptime可能被重复处理")
    
    logger.info("\n🟢 新逻辑（改进后的方式）:")
    logger.info("1. 获取API数据，uptime: 202507131820")
    
    # 检查是否需要处理
    should_process = should_process_task("6m", "gz_mpfv3", test_uptime)
    logger.info(f"2. 检查是否需要处理 -> {should_process}")
    
    if should_process:
        logger.info("3. 立即标记任务开始处理（更新uptime状态）")
        mark_task_started("6m", "gz_mpfv3", test_uptime)
        
        logger.info("4. 开始下载和处理...")
        logger.info("   ✅ 此时uptime状态已更新，即使处理失败也不会重复处理")
        
        # 验证状态已更新
        should_process_again = should_process_task("6m", "gz_mpfv3", test_uptime)
        logger.info(f"5. 再次检查相同uptime -> {should_process_again}")
        
        if not should_process_again:
            logger.info("   ✅ 确认：相同uptime不会被重复处理")
        
        logger.info("6. 处理完成后标记任务完成")
        mark_task_completed("6m", "gz_mpfv3", test_uptime)


def demo_concurrent_scenario():
    """演示并发场景下的改进"""
    logger.info("\n=== 演示并发场景下的改进 ===")
    
    # 重置状态
    task_state_manager.reset_all_states()
    
    test_uptime = "202507131825"
    
    logger.info("\n🔴 旧逻辑在并发场景下的问题:")
    logger.info("任务A: 获取uptime 202507131825 -> 开始处理")
    logger.info("任务B: 获取uptime 202507131825 -> 也开始处理（因为状态未更新）")
    logger.info("❌ 结果：同一个uptime被两个任务同时处理")
    
    logger.info("\n🟢 新逻辑在并发场景下的表现:")
    
    # 模拟任务A
    logger.info("任务A: 获取uptime 202507131825")
    should_process_a = should_process_task("6m", "gz_mpfv3", test_uptime)
    logger.info(f"任务A: 检查是否需要处理 -> {should_process_a}")
    
    if should_process_a:
        logger.info("任务A: 立即标记开始处理")
        mark_task_started("6m", "gz_mpfv3", test_uptime)
        
        # 模拟任务B（稍后到达）
        logger.info("任务B: 获取uptime 202507131825")
        should_process_b = should_process_task("6m", "gz_mpfv3", test_uptime)
        logger.info(f"任务B: 检查是否需要处理 -> {should_process_b}")
        
        if not should_process_b:
            logger.info("✅ 任务B被正确阻止，避免重复处理")
        
        logger.info("任务A: 完成处理")
        mark_task_completed("6m", "gz_mpfv3", test_uptime)


def demo_retry_logic_improvement():
    """演示重试逻辑的改进"""
    logger.info("\n=== 演示重试逻辑的改进 ===")
    
    # 重置状态
    task_state_manager.reset_all_states()
    
    logger.info("\n📋 新的重试逻辑流程:")
    logger.info("1. 调度器定时执行")
    logger.info("2. 调用API获取数据")
    logger.info("3. 检查uptime是否新鲜")
    logger.info("4. 如果不新鲜 -> 等待30秒后重新调用API")
    logger.info("5. 重复步骤2-4，直到获取新数据或超时")
    logger.info("6. 获取新数据后，立即标记开始处理")
    logger.info("7. 开始下载和处理")
    
    # 模拟场景
    old_uptime = "202507131800"
    new_uptime = "202507131830"
    
    logger.info(f"\n模拟场景:")
    logger.info(f"已处理的uptime: {old_uptime}")
    
    # 先标记一个旧的uptime
    mark_task_completed("6m", "gz_mpfv3", old_uptime)
    
    # 检查旧uptime
    should_process_old = should_process_task("6m", "gz_mpfv3", old_uptime)
    logger.info(f"检查旧uptime {old_uptime} -> {should_process_old} (正确，不应重复处理)")
    
    # 检查新uptime
    should_process_new = should_process_task("6m", "gz_mpfv3", new_uptime)
    logger.info(f"检查新uptime {new_uptime} -> {should_process_new} (正确，应该处理)")
    
    if should_process_new:
        logger.info(f"✅ 新uptime {new_uptime} 会被处理")
        mark_task_started("6m", "gz_mpfv3", new_uptime)
        logger.info(f"✅ 立即更新状态，防止重复处理")


def demo_configuration_options():
    """演示配置选项"""
    logger.info("\n=== 演示配置选项 ===")
    
    logger.info("\n📝 配置文件 (config.yml):")
    logger.info("""
scheduler:
  uptime_check:
    retry_interval_seconds: 30      # 重试间隔（秒）
    max_retry_duration_minutes: 60  # 最大重试时长（分钟）
    retry_until_fresh: true         # 启用新功能
""")
    
    logger.info("\n🎛️ 配置说明:")
    logger.info("• retry_interval_seconds: 控制重试间隔，默认30秒")
    logger.info("• max_retry_duration_minutes: 控制最大重试时间，默认60分钟")
    logger.info("• retry_until_fresh: 控制是否启用新的重试逻辑，默认true")
    
    logger.info("\n🔧 使用方式:")
    logger.info("• 启用功能: retry_until_fresh: true")
    logger.info("• 禁用功能: retry_until_fresh: false")
    logger.info("• 调整重试间隔: retry_interval_seconds: 60")
    logger.info("• 调整超时时间: max_retry_duration_minutes: 120")


def main():
    """主函数"""
    logger.info("🚀 开始演示新的uptime处理逻辑")
    
    try:
        # 演示各种场景
        demo_old_vs_new_logic()
        demo_concurrent_scenario()
        demo_retry_logic_improvement()
        demo_configuration_options()
        
        logger.info("\n🎉 演示完成！")
        logger.info("\n📋 总结:")
        logger.info("✅ 新逻辑在下载开始时就更新uptime状态")
        logger.info("✅ 避免了同一uptime被重复处理")
        logger.info("✅ 解决了并发场景下的竞态条件")
        logger.info("✅ 提供了灵活的配置选项")
        logger.info("✅ 保持了向后兼容性")
        
    except Exception as e:
        logger.error(f"演示过程中发生错误: {e}")


if __name__ == "__main__":
    main()
