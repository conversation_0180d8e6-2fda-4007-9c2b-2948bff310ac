#!/usr/bin/env python3
# coding: utf-8
"""
使用真实MySQL数据测试预警内容解析功能
"""

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

import pymysql
from src.weather_alarm import AlarmContentParser
from src.config import MYSQL_CONFIG

def test_with_real_data():
    """使用真实MySQL数据测试解析功能"""
    
    try:
        # 连接MySQL数据库
        mysql_conn = pymysql.connect(
            host=MYSQL_CONFIG["host"],
            port=MYSQL_CONFIG["port"],
            user=MYSQL_CONFIG["username"],
            password=MYSQL_CONFIG["password"],
            database=MYSQL_CONFIG["database"],
            charset='utf8mb4',
            autocommit=True
        )
        
        print("=" * 80)
        print("使用真实MySQL数据测试预警内容解析功能")
        print("=" * 80)
        
        # 1. 首先查看sys_region_code表的结构和数据
        print("\n1. 检查sys_region_code表结构和数据...")
        with mysql_conn.cursor() as cursor:
            # 查看表结构
            cursor.execute("DESCRIBE sys_region_code")
            columns = cursor.fetchall()
            print("   表结构:")
            for col in columns:
                print(f"     {col[0]} - {col[1]}")
            
            # 查看数据总数
            cursor.execute("SELECT COUNT(*) FROM sys_region_code")
            total_count = cursor.fetchone()[0]
            print(f"   总记录数: {total_count}")
            
            # 查看有sup_code的记录数
            cursor.execute("SELECT COUNT(*) FROM sys_region_code WHERE sup_code IS NOT NULL AND sup_code != ''")
            sup_code_count = cursor.fetchone()[0]
            print(f"   有sup_code的记录数: {sup_code_count}")
            
            # 查看一些示例数据
            cursor.execute("""
                SELECT data_name, data_code, sup_code 
                FROM sys_region_code 
                WHERE sup_code IS NOT NULL AND sup_code != ''
                LIMIT 10
            """)
            sample_data = cursor.fetchall()
            print("   示例数据:")
            for row in sample_data:
                print(f"     {row[0]} ({row[1]}) - 上级: {row[2]}")
        
        # 2. 查找一些具体的上级区域代码进行测试
        print("\n2. 查找可用的上级区域代码...")
        with mysql_conn.cursor() as cursor:
            cursor.execute("""
                SELECT DISTINCT sup_code, COUNT(*) as sub_count
                FROM sys_region_code 
                WHERE sup_code IS NOT NULL AND sup_code != ''
                GROUP BY sup_code
                HAVING sub_count > 5
                ORDER BY sub_count DESC
                LIMIT 5
            """)
            available_codes = cursor.fetchall()
            print("   可用的上级区域代码（子区域数量>5）:")
            for code, count in available_codes:
                print(f"     {code}: {count}个子区域")
        
        if not available_codes:
            print("   ❌ 没有找到可用的上级区域代码，无法进行测试")
            return
        
        # 3. 创建解析器并测试
        print("\n3. 创建解析器并获取区域数据...")
        parser = AlarmContentParser(mysql_conn)
        
        # 使用找到的上级区域代码
        test_geo_codes = [code[0] for code in available_codes[:3]]  # 取前3个
        print(f"   测试的geo_code: {test_geo_codes}")
        
        # 获取区域数据
        region_data = parser.get_region_data_by_geo_codes(test_geo_codes)
        
        for geo_code in test_geo_codes:
            if geo_code in region_data:
                sub_regions = region_data[geo_code]
                print(f"   {geo_code}: {len(sub_regions)}个子区域")
                for i, region in enumerate(sub_regions[:5]):  # 显示前5个
                    print(f"     {i+1}. {region['data_name']} ({region['data_code']})")
                if len(sub_regions) > 5:
                    print(f"     ... 还有{len(sub_regions)-5}个")
        
        # 4. 使用真实的预警内容进行测试
        print("\n4. 使用预警内容进行解析测试...")
        
        # 构造一些测试用例，使用真实的地名
        if region_data:
            first_geo_code = list(region_data.keys())[0]
            first_regions = region_data[first_geo_code]
            
            if len(first_regions) >= 2:
                # 构造包含真实地名的测试内容
                region1 = first_regions[0]['data_name']
                region2 = first_regions[1]['data_name']
                
                test_content = f"测试县气象台2025年7月21日发布预警信号：未来24小时{region1}、{region2}将出现恶劣天气，请注意防范。"
                
                print(f"   测试内容: {test_content}")
                print(f"   使用geo_code: {first_geo_code}")
                
                # 解析内容
                results = parser.parse_alarm_content(test_content, first_geo_code, region_data)
                
                print(f"   解析结果: {len(results)}个匹配")
                for i, result in enumerate(results, 1):
                    print(f"     {i}. {result['data_name']} ({result['data_code']})")
                
                # 验证匹配是否正确
                expected_names = [region1, region2]
                matched_names = [r['data_name'] for r in results]
                
                print(f"   预期匹配: {expected_names}")
                print(f"   实际匹配: {matched_names}")
                
                if set(expected_names).issubset(set(matched_names)):
                    print("   ✅ 匹配成功！")
                else:
                    print("   ❌ 匹配不完整")
        
        # 5. 测试您提供的真实预警内容
        print("\n5. 测试真实预警内容...")
        real_test_cases = [
            {
                "content": "墨江县气象台2025年7月18日07时05分发布大雾黄色预警信号：未来12小时墨江县大部乡镇将出现浓雾，请做好防护。",
                "description": "墨江县大雾预警"
            },
            {
                "content": "会泽县气象台2025年7月17日16时25分发布高温黄色预警信号：未来3天娜姑镇、纸厂乡的低矮河谷地区最高气温将在35℃以上，请注意防范。",
                "description": "会泽县高温预警"
            }
        ]
        
        for i, test_case in enumerate(real_test_cases, 1):
            print(f"\n   真实测试 {i}: {test_case['description']}")
            content = test_case["content"]
            
            # 提取行政区名称
            admin_region = parser.extract_admin_region_name(content)
            print(f"     提取的行政区: {admin_region}")
            
            # 查找对应的geo_code
            with mysql_conn.cursor() as cursor:
                cursor.execute("""
                    SELECT DISTINCT sup_code 
                    FROM sys_region_code 
                    WHERE sup_code IS NOT NULL 
                    AND sup_code != ''
                    LIMIT 1
                """)
                result = cursor.fetchone()
                if result:
                    test_geo_code = result[0]
                    print(f"     使用测试geo_code: {test_geo_code}")
                    
                    # 解析内容
                    results = parser.parse_alarm_content(content, test_geo_code, region_data)
                    print(f"     解析结果: {len(results)}个匹配")
                    for j, result in enumerate(results, 1):
                        print(f"       {j}. {result['data_name']} ({result['data_code']})")
        
        print("\n" + "=" * 80)
        print("真实数据测试完成")
        print("=" * 80)
        
        mysql_conn.close()
        
    except Exception as e:
        print(f"测试失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_with_real_data()
