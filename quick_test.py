#!/usr/bin/env python3
# coding: utf-8

import re

def extract_admin_region_name(alarm_content: str) -> str:
    """提取行政区名称（气象台之前的内容）"""
    match = re.search(r'^(.+?)气象台', alarm_content)
    if match:
        return match.group(1).strip()
    return ""

def extract_content_after_colon(alarm_content: str) -> str:
    """提取第一个冒号后的内容"""
    colon_index = alarm_content.find('：')
    if colon_index == -1:
        colon_index = alarm_content.find(':')
    if colon_index != -1:
        return alarm_content[colon_index + 1:].strip()
    return ""

def extract_time_and_location(content: str) -> str:
    """提取时间信息后的地点内容"""
    # 简化的匹配策略
    time_patterns = [r'未来\d+(?:天|小时)', r'\d+(?:天|小时)', r'(?:天|小时)']
    
    for time_pattern in time_patterns:
        match = re.search(time_pattern, content)
        if match:
            # 获取时间词后的内容
            after_time = content[match.end():].strip()
            
            # 去除开头的标点符号和空格
            after_time = re.sub(r'^[，,。！？\s]+', '', after_time)
            
            # 查找第一个包含地名的部分
            location_patterns = [
                r'([^，,。！？]*?(?:县|市|镇|乡|区|街道|村|等)[^，,。！？]*?)(?:[，,。！？]|$)',
                r'([^，,。！？]+?)(?:[，,。！？]|$)'
            ]
            
            for loc_pattern in location_patterns:
                loc_match = re.search(loc_pattern, after_time)
                if loc_match:
                    location_text = loc_match.group(1).strip()
                    if location_text:
                        return location_text
            break
    
    return ""

def clean_location_prefix(location: str, admin_region: str) -> str:
    """清理地点前缀"""
    prefixes = ['我县', '我区', '我州', '我市']
    for prefix in prefixes:
        if location.startswith(prefix):
            location = location[len(prefix):].strip()
            break
    
    if admin_region and location.startswith(admin_region):
        location = location[len(admin_region):].strip()
    
    return location

def split_locations(location_text: str) -> list:
    """使用顿号分割地点"""
    locations = re.split(r'[、]', location_text)
    return [loc.strip() for loc in locations if loc.strip()]

# 测试用例
test_cases = [
    "墨江县气象台2025年7月18日07时05分发布大雾黄色预警信号：未来12小时墨江县大部乡镇将出现浓雾，请做好防护。",
    "会泽县气象台2025年7月17日16时25分发布高温黄色预警信号：未来3天娜姑镇、纸厂乡的低矮河谷地区最高气温将在35℃以上，请注意防范。",
    "彝良县气象台2025年7月18日9时00分继续发布高温橙色预警信号：未来24小时，彝良县洛旺、柳溪、牛街、角奎、发界、海子、洛泽河、两河等乡镇（街道）海拔1200米以下低矮河谷区域日最高气温将升至37℃以上，午后请减少户外活动。",
    "绥江县气象台2025年7月18日08时34分发布高温橙色预警信号：未来24小时，我县南岸镇、板栗镇、中城镇、新滩镇、会仪镇最高气温将升至37℃以上，午后请减少户外活动。"
]

print("快速解析测试")
print("=" * 50)

for i, content in enumerate(test_cases, 1):
    print(f"\n测试 {i}:")
    print(f"内容: {content[:80]}...")
    
    # 提取行政区
    admin = extract_admin_region_name(content)
    print(f"行政区: {admin}")
    
    # 提取冒号后内容
    after_colon = extract_content_after_colon(content)
    print(f"冒号后: {after_colon[:60]}...")
    
    # 提取地点
    location = extract_time_and_location(after_colon)
    print(f"地点: '{location}'")
    
    # 清理前缀
    cleaned = clean_location_prefix(location, admin)
    print(f"清理后: '{cleaned}'")
    
    # 分割
    locations = split_locations(cleaned)
    print(f"分割: {locations}")

print("\n" + "=" * 50)
print("测试完成")
