#!/bin/bash
# ============================================================================
# Ubuntu 依赖安装脚本
# 用于在Ubuntu环境下安装weather-script项目的所有依赖
# ============================================================================

set -e  # 遇到错误时退出

echo "[INFO] 开始安装 weather-script 项目依赖..."
echo "[INFO] 操作系统: Ubuntu/Debian"
echo "[INFO] 包管理器: uv + apt"
echo

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查是否为root用户
check_sudo() {
    if [ "$EUID" -eq 0 ]; then
        log_warning "检测到root用户，建议使用普通用户运行此脚本"
        read -p "是否继续? (y/N): " -n 1 -r
        echo
        if [[ ! $REPLY =~ ^[Yy]$ ]]; then
            exit 1
        fi
    fi
}

# 检查Ubuntu版本
check_ubuntu_version() {
    log_info "检查Ubuntu版本..."
    if [ -f /etc/os-release ]; then
        . /etc/os-release
        echo "操作系统: $NAME $VERSION"
        if [[ "$ID" != "ubuntu" ]] && [[ "$ID_LIKE" != *"ubuntu"* ]] && [[ "$ID_LIKE" != *"debian"* ]]; then
            log_warning "此脚本主要为Ubuntu/Debian设计，其他发行版可能需要调整"
        fi
    else
        log_warning "无法检测操作系统版本"
    fi
}

# 更新系统包
update_system() {
    log_info "更新系统包列表..."
    sudo apt update
    log_success "系统包列表更新完成"
}

# 安装系统依赖
install_system_dependencies() {
    log_info "安装系统级依赖..."
    
    # GDAL和地理空间库
    log_info "安装GDAL和地理空间库..."
    sudo apt install -y \
        gdal-bin \
        libgdal-dev \
        libgeos-dev \
        libproj-dev \
        libspatialindex-dev \
        libspatialite-dev
    
    # Python开发工具
    log_info "安装Python开发工具..."
    sudo apt install -y \
        python3-dev \
        python3-pip \
        python3-venv \
        build-essential \
        pkg-config
    
    # 数据库客户端库
    log_info "安装数据库客户端库..."
    sudo apt install -y \
        libpq-dev \
        libmysqlclient-dev \
        libsqlite3-dev
    
    # NetCDF库
    log_info "安装NetCDF库..."
    sudo apt install -y \
        libnetcdf-dev \
        libhdf5-dev
    
    # 其他有用的工具
    log_info "安装其他工具..."
    sudo apt install -y \
        curl \
        wget \
        git \
        unzip
    
    log_success "系统级依赖安装完成"
}

# 检查并安装uv
install_uv() {
    log_info "检查uv包管理器..."
    if command -v uv &> /dev/null; then
        log_success "uv已安装: $(uv --version)"
    else
        log_info "安装uv包管理器..."
        curl -LsSf https://astral.sh/uv/install.sh | sh
        
        # 添加到当前会话的PATH
        export PATH="$HOME/.cargo/bin:$PATH"
        
        # 检查安装是否成功
        if command -v uv &> /dev/null; then
            log_success "uv安装成功: $(uv --version)"
            log_info "请将以下行添加到您的 ~/.bashrc 或 ~/.zshrc:"
            echo "export PATH=\"\$HOME/.cargo/bin:\$PATH\""
        else
            log_error "uv安装失败"
            exit 1
        fi
    fi
}

# 检查Python版本
check_python_version() {
    log_info "检查Python版本..."
    python_version=$(python3 --version 2>&1 | cut -d' ' -f2)
    log_info "当前Python版本: $python_version"
    
    # 检查是否为3.12
    if [[ $python_version == 3.12* ]]; then
        log_success "Python版本符合要求"
    else
        log_warning "建议使用Python 3.12，当前版本可能不完全兼容"
        log_info "继续安装..."
    fi
}

# 设置GDAL环境变量
setup_gdal_env() {
    log_info "设置GDAL环境变量..."
    
    # 获取GDAL版本和路径
    gdal_version=$(gdal-config --version 2>/dev/null || echo "unknown")
    gdal_cflags=$(gdal-config --cflags 2>/dev/null || echo "")
    gdal_libs=$(gdal-config --libs 2>/dev/null || echo "")
    
    log_info "GDAL版本: $gdal_version"
    
    # 设置环境变量
    export GDAL_CONFIG=/usr/bin/gdal-config
    export CPLUS_INCLUDE_PATH=/usr/include/gdal
    export C_INCLUDE_PATH=/usr/include/gdal
    
    log_success "GDAL环境变量设置完成"
}

# 创建和同步Python环境
setup_python_env() {
    log_info "设置Python虚拟环境..."
    
    # 创建虚拟环境（如果不存在）
    if [ ! -d ".venv" ]; then
        log_info "创建虚拟环境..."
        uv venv
        log_success "虚拟环境创建完成"
    else
        log_info "虚拟环境已存在"
    fi
    
    # 同步依赖
    log_info "同步项目依赖..."
    log_info "这可能需要几分钟时间，请耐心等待..."
    uv sync
    log_success "项目依赖同步完成"
}

# 安装开发依赖
install_dev_dependencies() {
    echo
    read -p "是否安装开发依赖 (ruff, pytest)? (y/N): " -n 1 -r
    echo
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        log_info "安装开发依赖..."
        uv sync --extra dev
        log_success "开发依赖安装完成"
    fi
}

# 验证安装
verify_installation() {
    log_info "验证关键依赖安装..."
    
    # 测试GDAL
    log_info "测试GDAL导入..."
    if uv run python -c "import gdal; print(f'GDAL版本: {gdal.__version__}')" 2>/dev/null; then
        log_success "GDAL导入成功"
    else
        log_warning "GDAL导入失败，可能需要手动检查"
    fi
    
    # 测试geopandas
    log_info "测试geopandas导入..."
    if uv run python -c "import geopandas; print(f'GeoPandas版本: {geopandas.__version__}')" 2>/dev/null; then
        log_success "GeoPandas导入成功"
    else
        log_warning "GeoPandas导入失败"
    fi
    
    # 测试pandas
    log_info "测试pandas导入..."
    if uv run python -c "import pandas; print(f'Pandas版本: {pandas.__version__}')" 2>/dev/null; then
        log_success "Pandas导入成功"
    else
        log_warning "Pandas导入失败"
    fi
    
    # 测试数据库连接库
    log_info "测试数据库连接库..."
    if uv run python -c "import psycopg2; print('PostgreSQL连接库正常')" 2>/dev/null; then
        log_success "PostgreSQL连接库正常"
    else
        log_warning "PostgreSQL连接库可能有问题"
    fi
}

# 显示安装摘要
show_summary() {
    echo
    echo "============================================================================"
    log_success "依赖安装完成！"
    echo "============================================================================"
    log_info "虚拟环境位置: .venv/"
    log_info "激活虚拟环境: source .venv/bin/activate"
    log_info "运行脚本示例: uv run python src/main.py"
    log_info "查看已安装包: uv pip list"
    echo
    log_info "如果遇到问题，请检查："
    log_info "1. 系统依赖是否正确安装"
    log_info "2. GDAL环境变量是否正确设置"
    log_info "3. Python版本是否兼容"
    log_info "4. 运行测试: uv run python tests/test_gdal_dependencies.py"
    echo
    log_info "环境变量建议添加到 ~/.bashrc:"
    echo "export GDAL_CONFIG=/usr/bin/gdal-config"
    echo "export CPLUS_INCLUDE_PATH=/usr/include/gdal"
    echo "export C_INCLUDE_PATH=/usr/include/gdal"
    echo "export PATH=\"\$HOME/.cargo/bin:\$PATH\""
    echo "============================================================================"
}

# 主函数
main() {
    check_sudo
    check_ubuntu_version
    update_system
    install_system_dependencies
    install_uv
    check_python_version
    setup_gdal_env
    setup_python_env
    install_dev_dependencies
    verify_installation
    show_summary
}

# 运行主函数
main "$@"
