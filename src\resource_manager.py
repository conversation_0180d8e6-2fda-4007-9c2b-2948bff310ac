#!/usr/bin/env python3
# coding: utf-8
"""
资源池管理器
统一管理连接池、线程池、进程池等资源，供所有脚本共用
"""

import asyncio
import asyncpg
import logging
import multiprocessing as mp
from concurrent.futures import ThreadPoolExecutor, ProcessPoolExecutor
from typing import Optional, Dict, Any
import threading
from contextlib import asynccontextmanager
from datetime import datetime

from config import (
    PG_URL, PROCESSING_CONFIG,
    SCHEDULER_DB_MAX_CONNECTIONS, SCHEDULER_DB_MIN_CONNECTIONS,
    SCHEDULER_THREAD_MAX_WORKERS, SCHEDULER_PROCESS_MAX_WORKERS
)

logger = logging.getLogger(__name__)


class ResourceManager:
    """统一资源管理器"""
    
    _instance = None
    _lock = threading.Lock()
    
    def __new__(cls):
        if cls._instance is None:
            with cls._lock:
                if cls._instance is None:
                    cls._instance = super().__new__(cls)
        return cls._instance
    
    def __init__(self):
        if hasattr(self, '_initialized'):
            return
        
        self._initialized = True
        self._db_pool: Optional[asyncpg.Pool] = None
        self._thread_pool: Optional[ThreadPoolExecutor] = None
        self._process_pool: Optional[ProcessPoolExecutor] = None
        self._pool_lock = asyncio.Lock()
        
        # 配置参数
        self.max_db_connections = SCHEDULER_DB_MAX_CONNECTIONS
        self.min_db_connections = SCHEDULER_DB_MIN_CONNECTIONS
        self.max_threads = SCHEDULER_THREAD_MAX_WORKERS or min(32, (mp.cpu_count() or 1) + 4)
        self.max_processes = SCHEDULER_PROCESS_MAX_WORKERS or PROCESSING_CONFIG.get("max_processes") or max(1, mp.cpu_count() - 1)
        
        logger.info(f"资源管理器初始化 - DB连接池: {self.min_db_connections}-{self.max_db_connections}, "
                   f"线程池: {self.max_threads}, 进程池: {self.max_processes}")
    
    async def initialize(self):
        """初始化所有资源池"""
        async with self._pool_lock:
            if self._db_pool is None:
                await self._init_db_pool()
            if self._thread_pool is None:
                self._init_thread_pool()
            if self._process_pool is None:
                self._init_process_pool()
    
    async def _init_db_pool(self):
        """初始化数据库连接池"""
        try:
            self._db_pool = await asyncpg.create_pool(
                PG_URL,
                min_size=self.min_db_connections,
                max_size=self.max_db_connections,
                command_timeout=60,
                server_settings={
                    'application_name': 'weather_scheduler',
                    'jit': 'off'
                }
            )
            logger.info(f"数据库连接池初始化成功，最大连接数: {self.max_db_connections}")
        except Exception as e:
            logger.error(f"数据库连接池初始化失败: {e}")
            raise
    
    def _init_thread_pool(self):
        """初始化线程池"""
        self._thread_pool = ThreadPoolExecutor(
            max_workers=self.max_threads,
            thread_name_prefix="weather_thread"
        )
        logger.info(f"线程池初始化成功，最大线程数: {self.max_threads}")
    
    def _init_process_pool(self):
        """初始化进程池"""
        self._process_pool = ProcessPoolExecutor(
            max_workers=self.max_processes,
            mp_context=mp.get_context('spawn')
        )
        logger.info(f"进程池初始化成功，最大进程数: {self.max_processes}")
    
    @asynccontextmanager
    async def get_db_connection(self):
        """获取数据库连接"""
        if self._db_pool is None:
            await self.initialize()
        
        async with self._db_pool.acquire() as conn:
            yield conn
    
    def get_thread_pool(self) -> ThreadPoolExecutor:
        """获取线程池"""
        if self._thread_pool is None:
            self._init_thread_pool()
        return self._thread_pool
    
    def get_process_pool(self) -> ProcessPoolExecutor:
        """获取进程池"""
        if self._process_pool is None:
            self._init_process_pool()
        return self._process_pool
    
    async def execute_query(self, query: str, *args) -> Any:
        """执行数据库查询"""
        async with self.get_db_connection() as conn:
            return await conn.fetch(query, *args)
    
    async def execute_command(self, command: str, *args) -> str:
        """执行数据库命令"""
        async with self.get_db_connection() as conn:
            return await conn.execute(command, *args)
    
    async def call_procedure(self, procedure_name: str, *args) -> Any:
        """调用存储过程"""
        async with self.get_db_connection() as conn:
            return await conn.fetchval(f"SELECT {procedure_name}($1)", *args)
    
    async def close(self):
        """关闭所有资源池"""
        async with self._pool_lock:
            # 快速关闭数据库连接池
            if self._db_pool:
                try:
                    await asyncio.wait_for(self._db_pool.close(), timeout=1.0)
                    logger.info("数据库连接池已关闭")
                except asyncio.TimeoutError:
                    logger.warning("数据库连接池关闭超时")
                finally:
                    self._db_pool = None

            # 快速关闭线程池
            if self._thread_pool:
                try:
                    # 不等待任务完成，立即关闭
                    self._thread_pool.shutdown(wait=False)
                    logger.info("线程池已关闭")
                except Exception as e:
                    logger.warning(f"线程池关闭异常: {e}")
                finally:
                    self._thread_pool = None

            # 快速关闭进程池
            if self._process_pool:
                try:
                    # 不等待任务完成，立即关闭
                    self._process_pool.shutdown(wait=False)
                    logger.info("进程池已关闭")
                except Exception as e:
                    logger.warning(f"进程池关闭异常: {e}")
                finally:
                    self._process_pool = None
    
    def get_stats(self) -> Dict[str, Any]:
        """获取资源池状态统计"""
        stats = {
            'timestamp': datetime.now().isoformat(),
            'db_pool': None,
            'thread_pool': None,
            'process_pool': None
        }
        
        if self._db_pool:
            stats['db_pool'] = {
                'size': self._db_pool.get_size(),
                'max_size': self._db_pool.get_max_size(),
                'min_size': self._db_pool.get_min_size(),
                'idle_size': self._db_pool.get_idle_size()
            }
        
        if self._thread_pool:
            stats['thread_pool'] = {
                'max_workers': self._thread_pool._max_workers,
                'active_threads': len(self._thread_pool._threads)
            }
        
        if self._process_pool:
            stats['process_pool'] = {
                'max_workers': self._process_pool._max_workers
            }
        
        return stats


# 全局资源管理器实例
resource_manager = ResourceManager()


class AsyncDatabaseManager:
    """异步数据库管理器（兼容现有代码）"""
    
    def __init__(self, db_url: str = None):
        self.db_url = db_url or PG_URL
        self._resource_manager = resource_manager
    
    async def initialize(self):
        """初始化数据库连接"""
        await self._resource_manager.initialize()
    
    async def close(self):
        """关闭连接（实际上不关闭共享池）"""
        pass  # 共享池由ResourceManager管理
    
    async def upsert_weather_data(self, timestamp: str, time_data: Dict) -> int:
        """插入或更新天气数据"""
        if not time_data:
            return 0
        
        # 构建批量upsert SQL
        values = []
        for cell_id, data in time_data.items():
            if isinstance(data, dict):
                # 1小时数据格式
                values.append((cell_id, timestamp, 
                             data.get('PRE'), data.get('TEM'), 
                             data.get('WEATHER'), data.get('VIS')))
            else:
                # 6分钟数据格式
                values.append((cell_id, timestamp, data))
        
        if not values:
            return 0
        
        # 根据数据格式选择表和SQL
        if len(values[0]) == 6:  # 1小时数据
            sql = """
                INSERT INTO weather_cell_1h (cell_id, timestamp, pre, tem, weather, vis)
                VALUES ($1, $2, $3, $4, $5, $6)
                ON CONFLICT (cell_id, timestamp) 
                DO UPDATE SET pre = EXCLUDED.pre, tem = EXCLUDED.tem, 
                             weather = EXCLUDED.weather, vis = EXCLUDED.vis
            """
        else:  # 6分钟数据
            sql = """
                INSERT INTO weather_cell_6m (cell_id, timestamp, rainfall, phase)
                VALUES ($1, $2, $3, $4)
                ON CONFLICT (cell_id, timestamp) 
                DO UPDATE SET rainfall = EXCLUDED.rainfall, phase = EXCLUDED.phase
            """
        
        async with self._resource_manager.get_db_connection() as conn:
            async with conn.transaction():
                await conn.executemany(sql, values)
        
        return len(values)
    
    async def call_stored_procedure(self, procedure_type: str, timestamp: str) -> bool:
        """调用存储过程"""
        try:
            if procedure_type == "polygon":
                proc_name = "process_weather_polygon"
            elif procedure_type == "line":
                proc_name = "process_weather_line"
            else:
                logger.error(f"未知的存储过程类型: {procedure_type}")
                return False
            
            result = await self._resource_manager.call_procedure(proc_name, timestamp)
            return result is not None
        except Exception as e:
            logger.error(f"调用存储过程 {procedure_type} 失败: {e}")
            return False
