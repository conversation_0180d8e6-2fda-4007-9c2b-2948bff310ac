[project]
name = "weather-script"
version = "0.1.0"
description = "Weather data processing and analysis scripts"
requires-python = ">=3.12"

# 核心依赖项：这些是项目运行所必需的包
dependencies = [
    # 地理空间数据处理
    "geopandas>=0.14.0", # 地理空间数据处理
    "shapely>=2.0.0", # 几何对象处理
    # 数据库相关
    "sqlalchemy>=2.0.0", # SQL工具包和ORM
    "geoalchemy2>=0.14.0", # SQLAlchemy的地理空间扩展
    "asyncpg>=0.29.0", # PostgreSQL异步驱动
    "pymysql>=1.1.0", # MySQL驱动
    # 数据处理
    "pandas>=2.0.0", # 数据分析库
    "numpy>=1.24.0", # 数值计算库
    "xarray>=2023.1.0", # 多维数组处理（NetCDF文件）
    # 网络请求和文件处理
    "requests>=2.31.0", # HTTP请求库
    "aiohttp>=3.9.0", # 异步HTTP客户端
    "pyyaml>=6.0", # YAML配置文件解析
    # Web框架和服务器
    "fastapi>=0.104.0", # 现代Web框架
    "uvicorn[standard]>=0.24.0", # ASGI服务器
    # 系统监控
    "psutil>=5.9.0", # 系统和进程监控
    # 地理空间处理依赖 (已安装)
    "gdal>=3.10.2", # 地理空间数据抽象库 (从本地wheel安装)
    "fiona>=1.10.1", # 矢量数据I/O (依赖GDAL)
    "pyproj>=3.7.1", # 投影转换库
    "rasterio>=1.4.3", # 栅格数据处理库
    "psycopg2-binary>=2.9.10",
    "netcdf4>=1.7.2",
]

# 可选依赖项：用于开发、测试等
[project.optional-dependencies]
dev = [
    "ruff>=0.1.0",              # Python代码检查和格式化
    "pytest>=7.0.0",            # 测试框架
    "pytest-asyncio>=0.21.0",   # 异步测试支持
]

# 构建系统配置
[build-system]
requires = ["hatchling"]
build-backend = "hatchling.build"

# Hatch构建配置
[tool.hatch.build.targets.wheel]
packages = ["src"]
exclude = [
    "/.venv",
    "/.git",
    "/.idea",
    "/data",
    "/__pycache__",
    "*.pyc",
    "*.pyo",
    "*.pyd",
    ".DS_Store",
    "Thumbs.db"
]

# 包发现配置
[tool.hatch.build]
include = [
    "src/**/*.py",
    "config.yml",
    "README.md"
]

# 工具配置
[tool.ruff]
line-length = 120
target-version = "py312"

[tool.ruff.lint]
select = ["E", "F", "W", "I"]
ignore = ["E501"]  # 忽略行长度检查，因为已经在line-length中设置

[tool.uv.sources]
gdal = { path = "wheels/gdal-3.10.2-cp312-cp312-win_amd64.whl" }
