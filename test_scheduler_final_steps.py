#!/usr/bin/env python3
# coding: utf-8
"""
测试调度器的最终步骤功能
验证即使下载失败也会执行最终步骤
"""

import asyncio
import logging
import sys
from pathlib import Path

# 添加src目录到Python路径
src_path = Path(__file__).parent / "src"
sys.path.insert(0, str(src_path))

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

async def test_hourly_final_steps():
    """测试1小时任务的最终步骤"""
    logger.info("=== 测试1小时任务最终步骤 ===")
    
    try:
        from scheduler import WeatherTaskScheduler
        
        scheduler = WeatherTaskScheduler()
        
        # 测试PRE数据类型的最终步骤
        logger.info("测试PRE数据类型的最终步骤...")
        await scheduler._execute_hourly_final_steps("PRE", download_success=False)
        
        # 测试其他数据类型的最终步骤
        logger.info("测试TEM数据类型的最终步骤...")
        await scheduler._execute_hourly_final_steps("TEM", download_success=False)
        
        logger.info("✓ 1小时任务最终步骤测试完成")
        
    except Exception as e:
        logger.error(f"1小时任务最终步骤测试失败: {e}")
        raise

async def test_six_minute_final_steps():
    """测试6分钟任务的最终步骤"""
    logger.info("=== 测试6分钟任务最终步骤 ===")
    
    try:
        from scheduler import WeatherTaskScheduler
        
        scheduler = WeatherTaskScheduler()
        
        # 测试6分钟任务的最终步骤
        logger.info("测试6分钟任务的最终步骤...")
        await scheduler._execute_six_minute_final_steps(download_success=False)
        
        logger.info("✓ 6分钟任务最终步骤测试完成")
        
    except Exception as e:
        logger.error(f"6分钟任务最终步骤测试失败: {e}")
        raise

async def test_weather_cell_6m_summary():
    """测试weather_cell_6m的汇总函数"""
    logger.info("=== 测试weather_cell_6m汇总函数 ===")
    
    try:
        from weather_cell_6m import call_summary_procedures_async, AsyncDatabaseManager
        from config import PG_URL
        
        # 创建数据库管理器（但不实际连接）
        db_manager = AsyncDatabaseManager(PG_URL)
        
        # 测试空的汇总函数
        logger.info("测试6分钟汇总函数...")
        await call_summary_procedures_async(db_manager)
        
        logger.info("✓ 6分钟汇总函数测试完成")
        
    except Exception as e:
        logger.error(f"6分钟汇总函数测试失败: {e}")
        raise

async def main():
    """主测试函数"""
    logger.info("开始测试调度器最终步骤功能...")
    
    try:
        # 测试6分钟任务最终步骤（不需要数据库连接）
        await test_six_minute_final_steps()
        
        # 测试6分钟汇总函数（不需要数据库连接）
        await test_weather_cell_6m_summary()
        
        # 测试1小时任务最终步骤（需要数据库连接，可能会失败）
        try:
            await test_hourly_final_steps()
        except Exception as e:
            logger.warning(f"1小时任务最终步骤测试失败（可能是数据库连接问题）: {e}")
        
        logger.info("✓ 所有测试完成")
        
    except Exception as e:
        logger.error(f"测试过程中发生错误: {e}")
        raise

if __name__ == '__main__':
    asyncio.run(main())
