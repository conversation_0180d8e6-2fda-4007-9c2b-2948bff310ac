# PostgreSQL 分区表快速开始指南

## 概述

本指南将帮助您快速设置 PostgreSQL 分区表和自动分区管理。

## 前提条件

1. PostgreSQL 11+ 数据库
2. 已安装 `pg_partman` 扩展
3. 已安装 `pg_cron` 扩展（可选，用于自动维护）

## 快速开始

### 步骤 1: 安装必要的扩展

```sql
-- 安装 pg_partman 扩展（必需）
CREATE EXTENSION IF NOT EXISTS pg_partman;

-- 安装 pg_cron 扩展（推荐，用于自动维护）
CREATE EXTENSION IF NOT EXISTS pg_cron;
```

### 步骤 2: 创建分区表并配置分区管理

运行表创建脚本（现已集成分区管理配置）：

```bash
# 创建所有分区表并自动配置分区管理
python src/create_forecast_tables.py
```

**注意**: 此脚本现在会自动：
1. 创建所有分区表
2. 配置 pg_partman 分区管理（如果扩展已安装）
3. 设置自动维护任务（如果 pg_cron 已安装）

### 步骤 3: 验证设置

```bash
# 验证集成的分区管理配置
python test_integrated_partition_setup.py

# 或者使用独立的验证脚本
python verify_partman_setup.py

# 或者检查分区表结构
python test_partition_tables.py
```

### 步骤 4: 手动运行维护（可选）

```sql
-- 立即创建初始分区
CALL partman.run_maintenance_proc();
```

## 分区策略总览

| 表类型 | 表数量 | 分区键 | 分区粒度 | 预创建 | 保留期 |
|--------|--------|--------|----------|--------|--------|
| 核心数据表 | 11个 | `pre_time` | 每日 | 7天 | 90天 |
| 预警表 | 1个 | `publish_time` | 每月 | 4个月 | 3年 |

### 核心数据表（11个）
- `weather_cell_6m`
- `weather_cell_1h`
- `forecast_precipitation_6min_line`
- `forecast_precipitation_6min_polygon`
- `forecast_precipitation_6min_relation`
- `forecast_precipitation_hourly_line`
- `forecast_precipitation_hourly_polygon`
- `forecast_precipitation_hourly_relation`
- `forecast_precipitation_summary_line`
- `forecast_precipitation_summary_polygon`
- `forecast_precipitation_summary_relation`

### 预警表（1个）
- `weather_alarm`

## 主要变更

### 主键结构变更
所有表的主键都从单列 `id` 改为复合主键：
- 核心数据表: `(pre_time, id)` 或 `(pre_time, cell_id)`
- 预警表: `(publish_time, id)`

### 分区键约束
所有分区键列都设置为 `NOT NULL`。

### 唯一索引调整
`weather_alarm` 表的唯一索引已调整为包含分区键。

## 自动维护

pg_partman 将每天凌晨2点自动执行以下任务：
1. 创建新的分区（根据预创建策略）
2. 删除过期的分区（根据保留策略）
3. 更新分区统计信息

## 验证命令

### 检查分区配置
```sql
SELECT * FROM partman.part_config ORDER BY parent_table;
```

### 检查已创建的分区
```sql
SELECT 
    schemaname,
    tablename,
    pg_size_pretty(pg_total_relation_size(schemaname||'.'||tablename)) as size
FROM pg_tables 
WHERE tablename ~ '_p\d{8}$' OR tablename ~ '_p\d{6}$'
ORDER BY schemaname, tablename;
```

### 检查定时任务
```sql
SELECT * FROM cron.job WHERE jobname = 'partman-maintenance';
```

## 故障排除

### 问题 1: pg_partman 扩展未安装
```sql
-- 解决方案
CREATE EXTENSION pg_partman;
```

### 问题 2: 分区未自动创建
```sql
-- 手动运行维护
CALL partman.run_maintenance_proc();
```

### 问题 3: 定时任务未运行
```sql
-- 检查 pg_cron 状态
SELECT * FROM cron.job;

-- 重新创建任务
SELECT cron.schedule('partman-maintenance', '0 2 * * *', 'CALL partman.run_maintenance_proc()');
```

### 问题 4: 主键约束错误
确保应用程序代码已更新以适应新的复合主键结构。

## 性能优化建议

1. **查询优化**: 在查询中包含分区键以利用分区裁剪
2. **索引策略**: 在分区键上创建适当的索引
3. **统计信息**: 定期更新表统计信息
4. **监控**: 监控分区大小和查询性能

## 监控和维护

### 日常检查
- 检查分区创建是否正常
- 监控磁盘空间使用
- 查看 pg_partman 日志

### 定期任务
- 调整保留策略（如需要）
- 优化分区策略（根据使用模式）
- 备份分区配置

## 回滚计划

如果需要回滚到非分区表：
1. 导出所有分区数据
2. 删除分区表
3. 创建传统表结构
4. 导入数据

**注意**: 回滚是一个复杂的过程，建议在测试环境中先验证。

## 支持和文档

- [pg_partman 官方文档](https://github.com/pgpartman/pg_partman)
- [PostgreSQL 分区文档](https://www.postgresql.org/docs/current/ddl-partitioning.html)
- 项目文档: `PARTITION_MIGRATION_SUMMARY.md`
