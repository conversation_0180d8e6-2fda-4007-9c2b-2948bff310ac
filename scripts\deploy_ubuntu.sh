#!/bin/bash
# Weather Scheduler Ubuntu 部署脚本

set -e  # 遇到错误立即退出

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查是否以 root 用户运行
check_root() {
    if [[ $EUID -ne 0 ]]; then
        log_error "此脚本需要以 root 用户运行"
        log_info "请使用: sudo $0"
        exit 1
    fi
}

# 检查系统环境
check_system() {
    log_info "检查系统环境..."
    
    # 检查操作系统
    if [[ ! -f /etc/os-release ]]; then
        log_error "无法确定操作系统版本"
        exit 1
    fi
    
    source /etc/os-release
    log_info "操作系统: $PRETTY_NAME"
    
    # 检查 systemd
    if ! command -v systemctl &> /dev/null; then
        log_error "系统不支持 systemd"
        exit 1
    fi
    
    log_success "系统环境检查通过"
}

# 创建目录结构
create_directories() {
    log_info "创建目录结构..."
    
    # 创建数据目录
    mkdir -p /data/weather/{MPF,backup,precipitation_6min,precipitation_1h,temperature_1h,weather_1h,visibility_1h}
    
    # 创建日志目录
    mkdir -p /var/log/weather_scheduler
    
    # 设置权限
    chown -R root:root /data/weather
    chmod -R 755 /data/weather
    
    chown -R root:root /var/log/weather_scheduler
    chmod -R 755 /var/log/weather_scheduler
    
    log_success "目录结构创建完成"
}

# 检查项目文件
check_project_files() {
    log_info "检查项目文件..."
    
    PROJECT_DIR="/data/script/weather_script"
    
    if [[ ! -d "$PROJECT_DIR" ]]; then
        log_error "项目目录不存在: $PROJECT_DIR"
        log_info "请确保项目代码已部署到正确位置"
        exit 1
    fi
    
    # 检查关键文件
    REQUIRED_FILES=(
        "$PROJECT_DIR/run_weather_scheduler.py"
        "$PROJECT_DIR/src/config.py"
        "$PROJECT_DIR/config.yml"
        "$PROJECT_DIR/systemd/weather-scheduler.service"
    )
    
    for file in "${REQUIRED_FILES[@]}"; do
        if [[ ! -f "$file" ]]; then
            log_error "缺少必要文件: $file"
            exit 1
        fi
    done
    
    # 检查 Python 虚拟环境
    if [[ ! -f "$PROJECT_DIR/.venv/bin/python" ]]; then
        log_error "Python 虚拟环境不存在: $PROJECT_DIR/.venv"
        log_info "请先创建虚拟环境: cd $PROJECT_DIR && python -m venv .venv"
        exit 1
    fi
    
    log_success "项目文件检查通过"
}

# 安装 systemd 服务
install_systemd_service() {
    log_info "安装 systemd 服务..."
    
    PROJECT_DIR="/data/script/weather_script"
    SERVICE_FILE="$PROJECT_DIR/systemd/weather-scheduler.service"
    TARGET_FILE="/etc/systemd/system/weather-scheduler.service"
    
    # 复制服务文件
    cp "$SERVICE_FILE" "$TARGET_FILE"
    
    # 重新加载 systemd 配置
    systemctl daemon-reload
    
    log_success "systemd 服务安装完成"
}

# 创建环境变量文件
create_env_file() {
    log_info "创建环境变量文件..."
    
    PROJECT_DIR="/data/script/weather_script"
    ENV_FILE="$PROJECT_DIR/.env"
    
    if [[ -f "$ENV_FILE" ]]; then
        log_warning "环境变量文件已存在: $ENV_FILE"
        read -p "是否覆盖? (y/N): " -n 1 -r
        echo
        if [[ ! $REPLY =~ ^[Yy]$ ]]; then
            log_info "跳过环境变量文件创建"
            return
        fi
    fi
    
    # 创建 .env 文件
    cat > "$ENV_FILE" << EOF
# Weather Scheduler 环境变量配置文件
# 自动生成于 $(date)

# Python 环境配置
PYTHONPATH=$PROJECT_DIR/src
PYTHONUNBUFFERED=1

# 系统配置
TZ=Asia/Shanghai
LANG=zh_CN.UTF-8
LC_ALL=zh_CN.UTF-8
EOF
    
    chmod 600 "$ENV_FILE"
    chown root:root "$ENV_FILE"
    
    log_success "环境变量文件创建完成: $ENV_FILE"
}

# 测试配置
test_configuration() {
    log_info "测试配置..."
    
    PROJECT_DIR="/data/script/weather_script"
    
    # 测试 Python 环境
    if ! "$PROJECT_DIR/.venv/bin/python" --version &> /dev/null; then
        log_error "Python 环境测试失败"
        exit 1
    fi
    
    # 测试路径解析
    cd "$PROJECT_DIR"
    if ! "$PROJECT_DIR/.venv/bin/python" test_path_resolution.py &> /dev/null; then
        log_warning "路径解析测试失败，请检查配置"
    else
        log_success "路径解析测试通过"
    fi
    
    log_success "配置测试完成"
}

# 启动服务
start_service() {
    log_info "启动服务..."
    
    # 启用服务（开机自启）
    systemctl enable weather-scheduler.service
    
    # 启动服务
    systemctl start weather-scheduler.service
    
    # 等待服务启动
    sleep 3
    
    # 检查服务状态
    if systemctl is-active --quiet weather-scheduler.service; then
        log_success "服务启动成功"
    else
        log_error "服务启动失败"
        log_info "查看服务状态: systemctl status weather-scheduler.service"
        log_info "查看服务日志: journalctl -u weather-scheduler.service -f"
        exit 1
    fi
}

# 显示部署信息
show_deployment_info() {
    log_success "部署完成！"
    echo
    echo "=== 服务管理命令 ==="
    echo "查看服务状态: systemctl status weather-scheduler.service"
    echo "查看服务日志: journalctl -u weather-scheduler.service -f"
    echo "重启服务:     systemctl restart weather-scheduler.service"
    echo "停止服务:     systemctl stop weather-scheduler.service"
    echo
    echo "=== 目录结构 ==="
    echo "项目目录:     /data/script/weather_script"
    echo "数据目录:     /data/weather"
    echo "日志目录:     /var/log/weather_scheduler"
    echo "配置文件:     /data/script/weather_script/config.yml"
    echo "环境变量:     /data/script/weather_script/.env"
    echo
    echo "=== 下一步 ==="
    echo "1. 检查服务日志确认运行正常"
    echo "2. 根据需要修改 config.yml 配置"
    echo "3. 设置监控和告警"
}

# 主函数
main() {
    log_info "开始部署 Weather Scheduler..."
    
    check_root
    check_system
    check_project_files
    create_directories
    install_systemd_service
    create_env_file
    test_configuration
    start_service
    show_deployment_info
}

# 运行主函数
main "$@"
