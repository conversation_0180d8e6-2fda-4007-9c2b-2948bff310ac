#!/usr/bin/env python3
# coding: utf-8
"""
测试天气数据upsert功能
验证先设置为null再插入/更新的逻辑
"""
import asyncio
import logging
from datetime import datetime, timedelta
import sys
import os

# 添加src目录到路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from weather_cell_1h import AsyncDatabaseManager
from config import PG_URL

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

async def test_upsert_logic():
    """测试upsert逻辑"""
    logger.info("=== 开始测试天气数据upsert逻辑 ===")
    
    # 初始化数据库管理器
    db_manager = AsyncDatabaseManager(PG_URL)
    await db_manager.initialize()
    
    try:
        # 测试时间戳
        test_time = datetime.now().replace(minute=0, second=0, microsecond=0)
        logger.info(f"测试时间: {test_time}")
        
        # 测试cell_id（使用一个已知存在的cell_id）
        test_cell_ids = [1000001, 1000002, 1000003]
        
        # 1. 清理测试数据
        logger.info("1. 清理测试数据...")
        async with db_manager.pool.acquire() as conn:
            await conn.execute("""
                DELETE FROM weather_cell_1h 
                WHERE pre_time = $1 AND cell_id = ANY($2)
            """, test_time, test_cell_ids)
        
        # 2. 插入初始数据（包含所有字段）
        logger.info("2. 插入初始数据...")
        initial_data = {
            test_cell_ids[0]: {'PRE': 5.5, 'WEATHER': 1, 'VIS': 1000, 'TEM': 25.5},
            test_cell_ids[1]: {'PRE': 3.2, 'WEATHER': 2, 'VIS': 800, 'TEM': 22.0},
            test_cell_ids[2]: {'PRE': 0.0, 'WEATHER': 0, 'VIS': 1200, 'TEM': 28.0}
        }
        
        await db_manager.upsert_weather_data(test_time, initial_data)
        
        # 验证初始数据
        async with db_manager.pool.acquire() as conn:
            rows = await conn.fetch("""
                SELECT cell_id, pre, weather, vis, tem 
                FROM weather_cell_1h 
                WHERE pre_time = $1 AND cell_id = ANY($2)
                ORDER BY cell_id
            """, test_time, test_cell_ids)
            
            logger.info("初始数据验证:")
            for row in rows:
                logger.info(f"  cell_id={row['cell_id']}: PRE={row['pre']}, WEATHER={row['weather']}, VIS={row['vis']}, TEM={row['tem']}")
        
        # 3. 测试只更新PRE数据（应该将PRE字段先设为null，然后更新）
        logger.info("3. 测试只更新PRE数据...")
        pre_only_data = {
            test_cell_ids[0]: {'PRE': 8.8},  # 更新现有记录的PRE
            test_cell_ids[1]: {'PRE': 6.6},  # 更新现有记录的PRE
            # test_cell_ids[2] 不包含，其PRE应该被设为null
        }
        
        await db_manager.upsert_weather_data(test_time, pre_only_data)
        
        # 验证PRE更新结果
        async with db_manager.pool.acquire() as conn:
            rows = await conn.fetch("""
                SELECT cell_id, pre, weather, vis, tem 
                FROM weather_cell_1h 
                WHERE pre_time = $1 AND cell_id = ANY($2)
                ORDER BY cell_id
            """, test_time, test_cell_ids)
            
            logger.info("PRE更新后验证:")
            for row in rows:
                logger.info(f"  cell_id={row['cell_id']}: PRE={row['pre']}, WEATHER={row['weather']}, VIS={row['vis']}, TEM={row['tem']}")
        
        # 4. 测试只更新TEM数据
        logger.info("4. 测试只更新TEM数据...")
        tem_only_data = {
            test_cell_ids[0]: {'TEM': 30.0},  # 更新现有记录的TEM
            test_cell_ids[2]: {'TEM': 35.5},  # 更新现有记录的TEM
            # test_cell_ids[1] 不包含，其TEM应该被设为null
        }
        
        await db_manager.upsert_weather_data(test_time, tem_only_data)
        
        # 验证TEM更新结果
        async with db_manager.pool.acquire() as conn:
            rows = await conn.fetch("""
                SELECT cell_id, pre, weather, vis, tem 
                FROM weather_cell_1h 
                WHERE pre_time = $1 AND cell_id = ANY($2)
                ORDER BY cell_id
            """, test_time, test_cell_ids)
            
            logger.info("TEM更新后验证:")
            for row in rows:
                logger.info(f"  cell_id={row['cell_id']}: PRE={row['pre']}, WEATHER={row['weather']}, VIS={row['vis']}, TEM={row['tem']}")
        
        # 5. 测试混合数据更新
        logger.info("5. 测试混合数据更新...")
        mixed_data = {
            test_cell_ids[0]: {'PRE': 12.5, 'VIS': 500},  # 更新PRE和VIS
            test_cell_ids[1]: {'WEATHER': 3},             # 更新WEATHER
            test_cell_ids[2]: {'TEM': 40.0, 'PRE': 15.0}, # 更新TEM和PRE
        }
        
        await db_manager.upsert_weather_data(test_time, mixed_data)
        
        # 验证混合更新结果
        async with db_manager.pool.acquire() as conn:
            rows = await conn.fetch("""
                SELECT cell_id, pre, weather, vis, tem 
                FROM weather_cell_1h 
                WHERE pre_time = $1 AND cell_id = ANY($2)
                ORDER BY cell_id
            """, test_time, test_cell_ids)
            
            logger.info("混合更新后验证:")
            for row in rows:
                logger.info(f"  cell_id={row['cell_id']}: PRE={row['pre']}, WEATHER={row['weather']}, VIS={row['vis']}, TEM={row['tem']}")
        
        # 6. 清理空记录测试
        logger.info("6. 测试清理空记录...")
        await db_manager.cleanup_null_records()
        
        # 最终验证
        async with db_manager.pool.acquire() as conn:
            rows = await conn.fetch("""
                SELECT cell_id, pre, weather, vis, tem 
                FROM weather_cell_1h 
                WHERE pre_time = $1 AND cell_id = ANY($2)
                ORDER BY cell_id
            """, test_time, test_cell_ids)
            
            logger.info("清理后最终验证:")
            for row in rows:
                logger.info(f"  cell_id={row['cell_id']}: PRE={row['pre']}, WEATHER={row['weather']}, VIS={row['vis']}, TEM={row['tem']}")
        
        # 7. 清理测试数据
        logger.info("7. 清理测试数据...")
        async with db_manager.pool.acquire() as conn:
            await conn.execute("""
                DELETE FROM weather_cell_1h 
                WHERE pre_time = $1 AND cell_id = ANY($2)
            """, test_time, test_cell_ids)
        
        logger.info("=== 测试完成 ===")
        
    except Exception as e:
        logger.error(f"测试过程中发生错误: {e}")
        raise
    finally:
        await db_manager.close()

async def main():
    """主函数"""
    await test_upsert_logic()

if __name__ == '__main__':
    asyncio.run(main())
