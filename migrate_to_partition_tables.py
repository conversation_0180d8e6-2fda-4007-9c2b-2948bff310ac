#!/usr/bin/env python3
# coding: utf-8
"""
迁移到分区表脚本
删除现有表并重新创建为分区表
注意：这会删除所有现有数据！
"""

from sqlalchemy import create_engine, text
from src.config import PG_URL
from src.create_forecast_tables import create_all_tables

def backup_existing_data():
    """
    备份现有数据（可选）
    """
    print("⚠️  警告：此脚本将删除所有现有表数据！")
    print("如果需要保留数据，请先手动备份。")
    
    response = input("是否继续？(yes/no): ")
    if response.lower() != 'yes':
        print("操作已取消")
        return False
    return True

def drop_existing_tables():
    """
    删除现有的表
    """
    engine = create_engine(PG_URL)
    
    # 需要删除的表列表
    tables_to_drop = [
        'weather_cell_6m',
        'weather_cell_1h', 
        'weather_alarm',
        'forecast_precipitation_6min_line',
        'forecast_precipitation_6min_polygon',
        'forecast_precipitation_6min_relation',
        'forecast_precipitation_hourly_line',
        'forecast_precipitation_hourly_polygon',
        'forecast_precipitation_hourly_relation',
        'forecast_precipitation_summary_line',
        'forecast_precipitation_summary_polygon',
        'forecast_precipitation_summary_relation'
    ]
    
    try:
        with engine.begin() as conn:
            for table_name in tables_to_drop:
                print(f"删除表: {table_name}")
                try:
                    # 使用 CASCADE 删除依赖的外键约束
                    conn.execute(text(f'DROP TABLE IF EXISTS "public"."{table_name}" CASCADE'))
                    print(f"  ✓ {table_name} 删除成功")
                except Exception as e:
                    print(f"  ⚠️ {table_name} 删除失败: {e}")
                    # 继续删除其他表
                    continue
        
        print("\n✓ 所有表删除完成")
        
    except Exception as e:
        print(f"删除表时发生错误: {e}")
        raise
    finally:
        engine.dispose()

def create_partition_tables():
    """
    创建新的分区表
    """
    print("\n开始创建分区表...")
    try:
        create_all_tables()
        print("✓ 分区表创建完成")
    except Exception as e:
        print(f"创建分区表时发生错误: {e}")
        raise

def verify_partition_tables():
    """
    验证分区表创建是否成功
    """
    print("\n验证分区表...")
    engine = create_engine(PG_URL)
    
    try:
        with engine.begin() as conn:
            # 检查分区表
            partition_check_sql = """
            SELECT 
                n.nspname as schema_name,
                c.relname as table_name,
                c.relkind,
                pg_get_partkeydef(c.oid) as partition_key
            FROM pg_class c
            JOIN pg_namespace n ON n.oid = c.relnamespace
            WHERE n.nspname = 'public' 
              AND c.relkind = 'p'
              AND (c.relname LIKE 'weather_%' OR c.relname LIKE 'forecast_%')
            ORDER BY c.relname
            """
            
            result = conn.execute(text(partition_check_sql)).fetchall()
            
            if result:
                print("✓ 发现以下分区表:")
                for row in result:
                    print(f"  {row[1]} - 分区键: {row[3]}")
            else:
                print("⚠️ 没有发现分区表")
            
            # 检查主键
            pk_check_sql = """
            SELECT 
                t.table_name,
                string_agg(k.column_name, ', ' ORDER BY k.ordinal_position) as primary_key_columns
            FROM information_schema.tables t
            JOIN information_schema.key_column_usage k ON k.table_name = t.table_name
            JOIN information_schema.table_constraints c ON c.constraint_name = k.constraint_name
            WHERE t.table_schema = 'public' 
              AND (t.table_name LIKE 'weather_%' OR t.table_name LIKE 'forecast_%')
              AND c.constraint_type = 'PRIMARY KEY'
              AND t.table_type = 'BASE TABLE'
            GROUP BY t.table_name
            ORDER BY t.table_name
            """
            
            pk_result = conn.execute(text(pk_check_sql)).fetchall()
            
            if pk_result:
                print("\n✓ 主键信息:")
                for row in pk_result:
                    print(f"  {row[0]}: ({row[1]})")
            
    except Exception as e:
        print(f"验证过程中发生错误: {e}")
        raise
    finally:
        engine.dispose()

def main():
    """
    主函数
    """
    print("PostgreSQL 分区表迁移脚本")
    print("=" * 50)
    
    # 1. 确认操作
    if not backup_existing_data():
        return
    
    try:
        # 2. 删除现有表
        drop_existing_tables()
        
        # 3. 创建分区表
        create_partition_tables()
        
        # 4. 验证结果
        verify_partition_tables()
        
        print("\n" + "=" * 50)
        print("✓ 分区表迁移完成！")
        print("\n下一步:")
        print("1. 运行 test_partition_tables.py 进行详细验证")
        print("2. 根据数据量创建具体的分区")
        print("3. 如有数据需要导入，请使用相应的导入脚本")
        
    except Exception as e:
        print(f"\n❌ 迁移过程中发生错误: {e}")
        print("请检查错误信息并手动修复")
        raise

if __name__ == '__main__':
    main()
