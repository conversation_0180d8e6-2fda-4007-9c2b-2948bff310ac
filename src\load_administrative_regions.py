#!/usr/bin/env python3
"""
行政区数据加载脚本

读取市级和县级行政区的geojson文件，处理geo_code字段，
并将数据存储到PostgreSQL数据库的administrative_regions表中。

功能：
1. 读取两个geojson文件（市级和县级行政区）
2. 处理geo_code字段：
   - 市级：去掉前3个和后2个字符
   - 县级：去掉前3个字符
3. 将数据存储到数据库，每次运行都替换表内容
4. 创建适当的索引

作者：Weather Script Team
日期：2025-01-18
"""

import logging
import sys
from pathlib import Path
import geopandas as gpd
import pandas as pd
from sqlalchemy import create_engine, text
from geoalchemy2 import Geometry
from sqlalchemy.types import String, Integer

# 导入项目配置
from config import get_postgres_url, POSTGRES_CONFIG, ADMINISTRATIVE_REGIONS_CONFIG

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


class AdministrativeRegionLoader:
    """行政区数据加载器"""
    
    def __init__(self, city_geojson_path: str, county_geojson_path: str, table_name: str = "administrative_regions"):
        """
        初始化加载器
        
        Args:
            city_geojson_path: 市级行政区geojson文件路径
            county_geojson_path: 县级行政区geojson文件路径
            table_name: 目标数据库表名
        """
        self.city_geojson_path = Path(city_geojson_path)
        self.county_geojson_path = Path(county_geojson_path)
        self.table_name = table_name
        self.engine = None
        
    def _validate_files(self):
        """验证输入文件是否存在"""
        if not self.city_geojson_path.exists():
            raise FileNotFoundError(f"市级行政区文件不存在: {self.city_geojson_path}")
        if not self.county_geojson_path.exists():
            raise FileNotFoundError(f"县级行政区文件不存在: {self.county_geojson_path}")
        logger.info("✅ 输入文件验证通过")
    
    def _connect_database(self):
        """连接数据库"""
        try:
            pg_url = get_postgres_url()
            self.engine = create_engine(pg_url)
            # 测试连接
            with self.engine.connect() as conn:
                conn.execute(text("SELECT 1"))
            logger.info("✅ 数据库连接成功")
        except Exception as e:
            logger.error(f"❌ 数据库连接失败: {e}")
            raise
    
    def _process_geo_code(self, gb_code: str, region_type: str) -> str:
        """
        处理geo_code字段

        Args:
            gb_code: 原始GB代码
            region_type: 区域类型 ('city' 或 'county')

        Returns:
            处理后的geo_code
        """
        if not gb_code or not isinstance(gb_code, str):
            return ""

        if region_type == 'city':
            # 市级：去掉前3个和后2个字符
            if len(gb_code) > 5:
                return gb_code[3:-2]
            elif len(gb_code) == 5:
                # 如果长度刚好是5，去掉前3个字符，后面没有字符可去掉
                return gb_code[3:]
            else:
                logger.warning(f"市级GB代码长度不足: {gb_code}")
                return gb_code
        elif region_type == 'county':
            # 县级：去掉前3个字符
            if len(gb_code) > 3:
                return gb_code[3:]
            else:
                logger.warning(f"县级GB代码长度不足: {gb_code}")
                return gb_code
        else:
            raise ValueError(f"未知的区域类型: {region_type}")
    
    def _read_and_process_geojson(self, file_path: Path, region_type: str) -> gpd.GeoDataFrame:
        """
        读取并处理geojson文件
        
        Args:
            file_path: geojson文件路径
            region_type: 区域类型 ('city' 或 'county')
            
        Returns:
            处理后的GeoDataFrame
        """
        logger.info(f"📖 读取{region_type}级行政区文件: {file_path}")
        
        try:
            # 读取geojson文件
            gdf = gpd.read_file(file_path)
            logger.info(f"   原始数据: {len(gdf)} 条记录")
            
            # 检查必需字段
            required_fields = ['name', 'gb']
            missing_fields = [field for field in required_fields if field not in gdf.columns]
            if missing_fields:
                raise ValueError(f"缺少必需字段: {missing_fields}")
            
            # 处理数据
            processed_data = []
            for idx, row in gdf.iterrows():
                try:
                    name = str(row['name']).strip()
                    gb_code = str(row['gb']).strip()
                    geometry = row['geometry']
                    
                    # 处理geo_code
                    geo_code = self._process_geo_code(gb_code, region_type)
                    
                    if name and geo_code and geometry:
                        processed_data.append({
                            'name': name,
                            'geo_code': geo_code,
                            'geometry': geometry
                        })
                    else:
                        logger.warning(f"跳过无效记录 {idx}: name={name}, geo_code={geo_code}")
                        
                except Exception as e:
                    logger.warning(f"处理记录 {idx} 时出错: {e}")
                    continue
            
            if not processed_data:
                raise ValueError(f"没有有效的{region_type}级行政区数据")
            
            # 创建新的GeoDataFrame
            result_gdf = gpd.GeoDataFrame(processed_data, crs=gdf.crs)
            
            # 确保坐标系为WGS84
            if result_gdf.crs != 'EPSG:4326':
                logger.info(f"   转换坐标系从 {result_gdf.crs} 到 WGS84")
                result_gdf = result_gdf.to_crs('EPSG:4326')
            
            logger.info(f"   处理完成: {len(result_gdf)} 条有效记录")
            return result_gdf
            
        except Exception as e:
            logger.error(f"❌ 读取{region_type}级行政区文件失败: {e}")
            raise
    
    def _create_table_and_indexes(self):
        """创建表和索引"""
        logger.info("🔧 创建数据库表和索引...")

        create_table_sql = f"""
        -- 删除现有表（如果存在）
        DROP TABLE IF EXISTS {self.table_name} CASCADE;

        -- 创建行政区表（只包含三个字段）
        CREATE TABLE {self.table_name} (
            geo_code VARCHAR(20) NOT NULL PRIMARY KEY,
            name VARCHAR(100) NOT NULL,
            shape GEOMETRY(GEOMETRY, 4326) NOT NULL
        );

        -- 创建索引
        CREATE INDEX idx_{self.table_name}_name ON {self.table_name}(name);
        CREATE INDEX idx_{self.table_name}_shape ON {self.table_name} USING GIST(shape);

        -- 添加表注释
        COMMENT ON TABLE {self.table_name} IS '行政区数据表，包含市级和县级行政区信息';
        COMMENT ON COLUMN {self.table_name}.geo_code IS '地理编码（主键）';
        COMMENT ON COLUMN {self.table_name}.name IS '行政区名称';
        COMMENT ON COLUMN {self.table_name}.shape IS '几何形状';
        """
        
        try:
            with self.engine.begin() as conn:
                conn.execute(text(create_table_sql))
            logger.info("✅ 表和索引创建成功")
        except Exception as e:
            logger.error(f"❌ 创建表失败: {e}")
            raise
    
    def _save_to_database(self, combined_gdf: gpd.GeoDataFrame):
        """保存数据到数据库"""
        logger.info(f"💾 保存 {len(combined_gdf)} 条记录到数据库...")
        
        # 准备数据类型映射
        dtype_map = {
            'geo_code': String(20),
            'name': String(100),
            'shape': Geometry('GEOMETRY', srid=4326)
        }
        
        try:
            # 重命名geometry列为shape并设置为活动几何列
            combined_gdf = combined_gdf.rename(columns={'geometry': 'shape'})
            combined_gdf = combined_gdf.set_geometry('shape')

            # 保存到数据库
            combined_gdf.to_postgis(
                name=self.table_name,
                con=self.engine,
                if_exists="append",  # 表已经创建，直接追加数据
                index=False,
                dtype=dtype_map
            )
            
            logger.info("✅ 数据保存成功")
            
            # 验证数据
            with self.engine.connect() as conn:
                result = conn.execute(text(f"SELECT COUNT(*) FROM {self.table_name}"))
                count = result.scalar()
                logger.info(f"📊 数据库中共有 {count} 条记录")
                
                # 显示前几条记录作为验证
                result = conn.execute(text(f"""
                    SELECT geo_code, name
                    FROM {self.table_name}
                    ORDER BY geo_code
                    LIMIT 5
                """))
                logger.info("   前5条记录:")
                for row in result:
                    logger.info(f"     {row[0]}: {row[1]}")
                    
        except Exception as e:
            logger.error(f"❌ 保存数据失败: {e}")
            raise
    
    def load_data(self):
        """执行完整的数据加载流程"""
        logger.info("🚀 开始加载行政区数据...")
        
        try:
            # 1. 验证文件
            self._validate_files()
            
            # 2. 连接数据库
            self._connect_database()
            
            # 3. 读取和处理数据
            city_gdf = self._read_and_process_geojson(self.city_geojson_path, 'city')
            county_gdf = self._read_and_process_geojson(self.county_geojson_path, 'county')
            
            # 4. 合并数据
            logger.info("🔄 合并市级和县级数据...")
            combined_gdf = pd.concat([city_gdf, county_gdf], ignore_index=True)
            logger.info(f"   合并后总计: {len(combined_gdf)} 条记录")
            
            # 5. 创建表和索引
            self._create_table_and_indexes()
            
            # 6. 保存到数据库
            self._save_to_database(combined_gdf)
            
            logger.info("🎉 行政区数据加载完成！")
            
        except Exception as e:
            logger.error(f"❌ 数据加载失败: {e}")
            raise
        finally:
            if self.engine:
                self.engine.dispose()


def main():
    """主函数"""
    # 支持命令行参数或使用配置文件中的默认路径
    if len(sys.argv) == 1:
        # 使用配置文件中的默认路径
        city_file = ADMINISTRATIVE_REGIONS_CONFIG["city_geojson"]
        county_file = ADMINISTRATIVE_REGIONS_CONFIG["county_geojson"]
        logger.info("使用配置文件中的默认路径:")
        logger.info(f"  市级文件: {city_file}")
        logger.info(f"  县级文件: {county_file}")
    elif len(sys.argv) == 3:
        # 使用命令行参数
        city_file = sys.argv[1]
        county_file = sys.argv[2]
        logger.info("使用命令行参数指定的文件路径")
    else:
        print("用法:")
        print("  1. 使用配置文件默认路径: python load_administrative_regions.py")
        print("  2. 指定文件路径: python load_administrative_regions.py <市级geojson文件> <县级geojson文件>")
        print("示例: python load_administrative_regions.py ./data/city_regions.geojson ./data/county_regions.geojson")
        sys.exit(1)

    try:
        loader = AdministrativeRegionLoader(city_file, county_file)
        loader.load_data()
    except Exception as e:
        logger.error(f"程序执行失败: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
