#!/usr/bin/env python3
# coding: utf-8
"""
验证分区表是否正确创建
"""

from sqlalchemy import create_engine, text
from src.config import PG_URL

def check_partitions():
    """检查分区表状态"""
    engine = create_engine(PG_URL)
    
    try:
        with engine.begin() as conn:
            print("=== 检查分区表状态 ===")
            
            # 检查 partman 配置
            print("\n1. 检查 partman 配置:")
            partman_config = conn.execute(text("""
                SELECT 
                    parent_table,
                    partition_type,
                    partition_interval,
                    control,
                    premake,
                    retention,
                    retention_keep_table
                FROM partman.part_config
                ORDER BY parent_table
            """)).fetchall()
            
            for config in partman_config:
                print(f"  表: {config[0]}")
                print(f"    类型: {config[1]}, 间隔: {config[2]}, 控制列: {config[3]}")
                print(f"    预创建: {config[4]}, 保留: {config[5]}, 保留表: {config[6]}")
                print()
            
            # 检查已创建的分区
            print("2. 检查已创建的分区:")
            partitions = conn.execute(text("""
                SELECT 
                    schemaname,
                    tablename,
                    pg_size_pretty(pg_total_relation_size(schemaname||'.'||tablename)) as size
                FROM pg_tables 
                WHERE tablename LIKE '%_p%'
                   OR tablename LIKE '%_default'
                ORDER BY schemaname, tablename
            """)).fetchall()
            
            if partitions:
                for partition in partitions:
                    print(f"  {partition[0]}.{partition[1]} ({partition[2]})")
            else:
                print("  暂无分区表（将在首次插入数据时自动创建）")
            
            # 检查表结构
            print("\n3. 检查主表结构:")
            tables_to_check = [
                'weather_cell_6m',
                'weather_cell_1h', 
                'forecast_precipitation_6min_line',
                'weather_alarm'
            ]
            
            for table in tables_to_check:
                try:
                    result = conn.execute(text(f"""
                        SELECT 
                            column_name,
                            data_type,
                            is_nullable
                        FROM information_schema.columns
                        WHERE table_schema = 'public' 
                          AND table_name = '{table}'
                          AND column_name IN ('id', 'pre_time', 'publish_time')
                        ORDER BY ordinal_position
                    """)).fetchall()
                    
                    print(f"  {table}:")
                    for col in result:
                        print(f"    {col[0]}: {col[1]} ({'NULL' if col[2] == 'YES' else 'NOT NULL'})")
                except Exception as e:
                    print(f"  {table}: 检查失败 - {e}")
            
            print("\n✅ 分区表检查完成")
            
    except Exception as e:
        print(f"❌ 检查分区表时发生错误: {e}")
    finally:
        engine.dispose()

if __name__ == '__main__':
    check_partitions()
