#!/usr/bin/env python3
# coding: utf-8
"""
配置验证脚本
验证config.yml中的所有配置项是否正确
"""

import sys
from pathlib import Path
import logging

# 添加src目录到Python路径
src_path = Path(__file__).parent / "src"
sys.path.insert(0, str(src_path))

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)

logger = logging.getLogger(__name__)


def validate_database_config():
    """验证数据库配置"""
    logger.info("验证数据库配置...")
    
    try:
        from config import PG_URL, POSTGRES_CONFIG
        
        logger.info(f"PostgreSQL配置: {POSTGRES_CONFIG}")
        logger.info(f"连接URL: {PG_URL}")
        
        # 测试数据库连接
        import asyncio
        import asyncpg
        
        async def test_db():
            try:
                conn = await asyncpg.connect(PG_URL)
                version = await conn.fetchval("SELECT version()")
                await conn.close()
                logger.info(f"✓ 数据库连接成功: {version}")
                return True
            except Exception as e:
                logger.error(f"✗ 数据库连接失败: {e}")
                return False
        
        return asyncio.run(test_db())
        
    except Exception as e:
        logger.error(f"✗ 数据库配置验证失败: {e}")
        return False


def validate_weather_download_config():
    """验证天气数据下载配置"""
    logger.info("验证天气数据下载配置...")
    
    try:
        from config import WEATHER_DOWNLOAD_CONFIG
        
        required_keys = ["api_base_url", "api_key", "enable_download"]
        for key in required_keys:
            if key not in WEATHER_DOWNLOAD_CONFIG:
                logger.error(f"✗ 缺少必需的配置项: weather_download.{key}")
                return False
        
        logger.info(f"API基础URL: {WEATHER_DOWNLOAD_CONFIG['api_base_url']}")
        logger.info(f"API密钥: {WEATHER_DOWNLOAD_CONFIG['api_key'][:10]}...")
        logger.info(f"下载功能启用: {WEATHER_DOWNLOAD_CONFIG['enable_download']}")
        
        logger.info("✓ 天气数据下载配置验证通过")
        return True
        
    except Exception as e:
        logger.error(f"✗ 天气数据下载配置验证失败: {e}")
        return False


def validate_scheduler_config():
    """验证调度器配置"""
    logger.info("验证调度器配置...")
    
    try:
        from config import get_scheduler_config
        
        config = get_scheduler_config()
        
        # 验证uptime检查配置
        uptime_check = config["uptime_check"]
        assert isinstance(uptime_check["retry_interval_seconds"], int), "retry_interval_seconds必须是整数"
        assert isinstance(uptime_check["max_retry_duration_minutes"], int), "max_retry_duration_minutes必须是整数"
        
        # 验证任务配置
        tasks = config["tasks"]
        hourly = tasks["hourly"]
        six_minute = tasks["six_minute"]
        
        assert isinstance(hourly["data_types"], list), "hourly.data_types必须是列表"
        assert len(hourly["data_types"]) > 0, "hourly.data_types不能为空"
        assert isinstance(hourly["timeout_minutes"], int), "hourly.timeout_minutes必须是整数"
        
        assert isinstance(six_minute["data_type"], str), "six_minute.data_type必须是字符串"
        assert isinstance(six_minute["timeout_minutes"], int), "six_minute.timeout_minutes必须是整数"
        
        # 验证资源配置
        resources = config["resources"]
        db_config = resources["database"]
        
        assert isinstance(db_config["max_connections"], int), "database.max_connections必须是整数"
        assert isinstance(db_config["min_connections"], int), "database.min_connections必须是整数"
        assert db_config["max_connections"] >= db_config["min_connections"], "max_connections必须大于等于min_connections"
        
        # 验证Web配置
        web = config["web"]
        assert isinstance(web["host"], str), "web.host必须是字符串"
        assert isinstance(web["port"], int), "web.port必须是整数"
        assert 1 <= web["port"] <= 65535, "web.port必须在1-65535范围内"
        
        logger.info("✓ 调度器配置验证通过")
        logger.info(f"  - 1小时任务类型: {hourly['data_types']}")
        logger.info(f"  - 6分钟任务类型: {six_minute['data_type']}")
        logger.info(f"  - Web服务: {web['host']}:{web['port']}")
        logger.info(f"  - 重试间隔: {config['uptime_check']['retry_interval_seconds']}秒")
        
        return True
        
    except Exception as e:
        logger.error(f"✗ 调度器配置验证失败: {e}")
        return False


def validate_weather_processing_config():
    """验证天气数据处理配置"""
    logger.info("验证天气数据处理配置...")
    
    try:
        from config import get_weather_6m_config, get_weather_1h_config
        
        # 验证6分钟配置
        config_6m = get_weather_6m_config()
        assert isinstance(config_6m["batch_size"], int), "6m.batch_size必须是整数"
        assert isinstance(config_6m["max_concurrent_db"], int), "6m.max_concurrent_db必须是整数"
        
        # 验证1小时配置
        config_1h = get_weather_1h_config()
        assert isinstance(config_1h["batch_size"], int), "1h.batch_size必须是整数"
        assert isinstance(config_1h["max_concurrent_db"], int), "1h.max_concurrent_db必须是整数"
        assert isinstance(config_1h["data_types"], dict), "1h.data_types必须是字典"
        
        logger.info("✓ 天气数据处理配置验证通过")
        logger.info(f"  - 6分钟批处理大小: {config_6m['batch_size']}")
        logger.info(f"  - 1小时批处理大小: {config_1h['batch_size']}")
        
        return True
        
    except Exception as e:
        logger.error(f"✗ 天气数据处理配置验证失败: {e}")
        return False


def main():
    """主验证函数"""
    logger.info("🔍 开始验证配置文件...")
    logger.info("=" * 50)
    
    results = []
    
    # 验证各个配置模块
    results.append(("数据库配置", validate_database_config()))
    results.append(("天气数据下载配置", validate_weather_download_config()))
    results.append(("调度器配置", validate_scheduler_config()))
    results.append(("天气数据处理配置", validate_weather_processing_config()))
    
    # 汇总结果
    logger.info("=" * 50)
    logger.info("📊 配置验证结果:")
    
    all_passed = True
    for name, passed in results:
        status = "✅ 通过" if passed else "❌ 失败"
        logger.info(f"  {name}: {status}")
        if not passed:
            all_passed = False
    
    logger.info("=" * 50)
    if all_passed:
        logger.info("🎉 所有配置验证通过！系统可以正常启动。")
        logger.info("")
        logger.info("💡 下一步:")
        logger.info("   uv run python run_weather_scheduler.py")
        sys.exit(0)
    else:
        logger.error("💥 配置验证失败！请检查config.yml文件。")
        sys.exit(1)


if __name__ == "__main__":
    main()
