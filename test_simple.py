#!/usr/bin/env python3
import sys
import os
sys.path.append('src')

from build_lookup import normalize_xzqh_code

# 测试几个关键用例
tests = [
    ('532622000000', '532622'),  # 12位截取到6位，去0后保持6位
    ('530722206000', '530722'),  # 12位截取到6位，去0后保持6位
    ('530102000', '530102'),     # 9位截取到6位，去0后保持6位
    ('530102001', '530102'),     # 9位截取到6位，去0后保持6位  
    ('530000', '5300'),          # 6位去0后变4位
    ('53', '53'),                # 2位保持2位
    ('5300', '5300'),            # 4位保持4位
]

print("=== 测试行政区划代码标准化（修改后的逻辑）===")
for inp, exp in tests:
    result = normalize_xzqh_code(inp)
    status = "✓" if result == exp else "✗"
    print(f"{status} {inp} -> {result} (期望: {exp})")
    if result != exp:
        print(f"    实际结果与期望不符")
