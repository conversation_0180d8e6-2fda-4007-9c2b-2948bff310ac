#!/usr/bin/env python3
# coding: utf-8
"""
Weather Script 项目源代码包

包含天气数据处理的所有核心模块：
- weather_download: 天气数据下载和NetCDF处理
- weather_common: 公共函数和类
- weather_cell_6m: 6分钟天气数据处理
- weather_cell_1h: 1小时天气数据处理
- build_lookup: 地理空间查找表构建
- create_forecast_tables: 预报表创建
- config: 统一配置管理
"""

__version__ = "1.0.0"
__author__ = "Weather Script Team"
__description__ = "天气数据处理和分析系统"

# 导出主要模块
__all__ = [
    "weather_download",
    "weather_common", 
    "weather_cell_6m",
    "weather_cell_1h",
    "build_lookup",
    "create_forecast_tables",
    "config"
]
