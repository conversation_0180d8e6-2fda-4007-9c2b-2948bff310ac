#!/usr/bin/env python3
# coding: utf-8
"""
天气预警信息获取和处理模块
- 从API获取预警信息
- 从MySQL获取区域代码映射
- 处理预警信息的新增、更新和解除
- 每10分钟执行一次
"""

import asyncio
import logging
import json
import aiohttp
import asyncpg
import pymysql
import re
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple
from urllib.parse import urlencode
from concurrent.futures import ThreadPoolExecutor

# 导入统一配置
from config import (
    PG_URL, MYSQL_CONFIG, WEATHER_ALARM_CONFIG
)

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


class AlarmContentParser:
    """预警内容解析器"""

    def __init__(self, mysql_conn):
        self.mysql_conn = mysql_conn
        self.region_cache = {}  # 缓存区域数据

    def get_region_data_by_geo_codes(self, geo_codes: List[str]) -> Dict[str, List[Dict]]:
        """根据geo_code列表批量获取区域数据"""
        if not geo_codes:
            return {}

        try:
            with self.mysql_conn.cursor() as cursor:
                # 使用IN查询提高效率
                placeholders = ','.join(['%s'] * len(geo_codes))
                sql = f"""
                SELECT data_name, data_code, sup_code
                FROM sys_region_code
                WHERE sup_code IN ({placeholders})
                AND sup_code != '%%'
                AND sup_code IS NOT NULL
                AND sup_code != ''
                """
                cursor.execute(sql, geo_codes)
                results = cursor.fetchall()

                # 按sup_code分组
                region_data = {}
                for row in results:
                    data_name, data_code, sup_code = row
                    if sup_code not in region_data:
                        region_data[sup_code] = []
                    region_data[sup_code].append({
                        'data_name': data_name,
                        'data_code': data_code
                    })

                logger.info(f"获取到 {len(results)} 条区域数据，涉及 {len(region_data)} 个上级区域")
                return region_data

        except Exception as e:
            logger.error(f"获取区域数据失败: {e}")
            return {}



    def extract_admin_region_name(self, alarm_content: str) -> str:
        """提取行政区名称（气象台之前的内容）"""
        match = re.search(r'^(.+?)气象台', alarm_content)
        if match:
            return match.group(1).strip()
        return ""

    def extract_content_after_colon(self, alarm_content: str) -> str:
        """提取第一个冒号后的内容"""
        colon_index = alarm_content.find('：')
        if colon_index == -1:
            colon_index = alarm_content.find(':')
        if colon_index != -1:
            return alarm_content[colon_index + 1:].strip()
        return ""

    def extract_time_and_location(self, content: str) -> str:
        """提取时间信息后的地点内容"""
        # 简化的匹配策略
        # 1. 先找到"天"或"小时"的位置
        time_patterns = [r'未来\d+(?:天|小时)', r'\d+(?:天|小时)', r'(?:天|小时)']

        for time_pattern in time_patterns:
            match = re.search(time_pattern, content)
            if match:
                # 获取时间词后的内容
                after_time = content[match.end():].strip()

                # 去除开头的标点符号和空格
                after_time = re.sub(r'^[，,。！？\s]+', '', after_time)

                # 查找第一个包含地名的部分（包含县、市、镇、乡、区等）
                # 或者查找第一个逗号前的内容
                location_patterns = [
                    r'([^，,。！？]*?(?:县|市|镇|乡|区|街道|村|等)[^，,。！？]*?)(?:[，,。！？]|$)',  # 包含地名标识
                    r'([^，,。！？]+?)(?:[，,。！？]|$)'  # 第一个逗号前的内容
                ]

                for loc_pattern in location_patterns:
                    loc_match = re.search(loc_pattern, after_time)
                    if loc_match:
                        location_text = loc_match.group(1).strip()
                        if location_text:
                            return location_text

                break

        return ""

    def clean_location_prefix(self, location: str, admin_region: str) -> str:
        """清理地点前缀"""
        # 去除常见前缀
        prefixes = ['我县', '我区', '我州', '我市']
        for prefix in prefixes:
            if location.startswith(prefix):
                location = location[len(prefix):].strip()
                break

        # 去除行政区名称前缀
        if admin_region and location.startswith(admin_region):
            location = location[len(admin_region):].strip()

        return location

    def split_locations(self, location_text: str) -> List[str]:
        """使用顿号分割地点"""
        # 使用顿号分割
        locations = re.split(r'[、]', location_text)
        return [loc.strip() for loc in locations if loc.strip()]

    def remove_suffix(self, location: str) -> str:
        """去除地名后缀"""
        suffixes = ['市', '乡', '镇', '县', '村', '区', '街', '街道']
        for suffix in suffixes:
            if location.endswith(suffix):
                return location[:-len(suffix)]
        return location

    def match_region_names(self, locations: List[str], region_data: List[Dict]) -> List[Dict]:
        """匹配区域名称"""
        matched_results = []

        if not locations or not region_data:
            return matched_results

        # 情况一：处理除最后一个之外的所有项
        for i, location in enumerate(locations[:-1]):
            matches = self._find_exact_matches(location, region_data)
            if len(matches) == 1:
                matched_results.append(matches[0])
            elif len(matches) == 0:
                # 尝试去除后缀再匹配
                location_no_suffix = self.remove_suffix(location)
                matches = self._find_exact_matches(location_no_suffix, region_data)
                if len(matches) == 1:
                    matched_results.append(matches[0])
                # 如果还是没有匹配或多匹配，则跳过

        # 情况二：处理最后一个项（如果只有一个项，也是最后一个）
        if locations:
            last_location = locations[-1]
            matched_result = self._match_last_location(last_location, region_data)
            if matched_result:
                matched_results.append(matched_result)

        return matched_results

    def _find_exact_matches(self, location: str, region_data: List[Dict]) -> List[Dict]:
        """查找精确匹配的区域"""
        matches = []
        for region in region_data:
            if region['data_name'].startswith(location):
                matches.append(region)
        return matches

    def _match_last_location(self, location: str, region_data: List[Dict]) -> Optional[Dict]:
        """匹配最后一个地点（逐字增加匹配）"""
        for i in range(1, len(location) + 1):
            partial_location = location[:i]
            matches = self._find_exact_matches(partial_location, region_data)

            if len(matches) == 1:
                # 如果是单字匹配，需要验证前两个字是否相同
                if i == 1:
                    region_name = matches[0]['data_name']
                    if len(region_name) >= 2 and len(location) >= 2:
                        if region_name[:2] == location[:2]:
                            return matches[0]
                    # 单字匹配但前两字不同，继续增加字符
                    continue
                else:
                    return matches[0]
            elif len(matches) == 0:
                # 没有匹配，说明不是地名
                return None
            # 多个匹配，继续增加字符

        return None

    def parse_alarm_content(self, alarm_content: str, geo_code: str, region_data: Dict[str, List[Dict]]) -> List[Dict]:
        """解析预警内容，返回匹配的区域信息"""
        try:
            # 1. 提取行政区名称
            admin_region = self.extract_admin_region_name(alarm_content)

            # 2. 提取冒号后的内容
            content_after_colon = self.extract_content_after_colon(alarm_content)
            if not content_after_colon:
                return []

            # 3. 提取时间后的地点内容
            location_text = self.extract_time_and_location(content_after_colon)
            if not location_text:
                return []

            # 4. 清理地点前缀
            cleaned_location = self.clean_location_prefix(location_text, admin_region)
            if not cleaned_location:
                return []

            # 5. 分割地点
            locations = self.split_locations(cleaned_location)
            if not locations:
                return []

            # 6. 获取该geo_code对应的区域数据
            current_region_data = region_data.get(geo_code, [])
            if not current_region_data:
                logger.warning(f"未找到geo_code {geo_code}对应的区域数据")
                return []

            # 7. 匹配区域名称
            matched_results = self.match_region_names(locations, current_region_data)

            logger.info(f"预警内容解析完成: geo_code={geo_code}, 匹配到 {len(matched_results)} 个区域")
            return matched_results

        except Exception as e:
            logger.error(f"解析预警内容失败: {e}, content: {alarm_content[:100]}...")
            return []

class WeatherAlarmProcessor:
    """天气预警信息处理器"""

    def __init__(self):
        self.pg_pool = None
        self.mysql_conn = None
        self.session = None
        self.content_parser = None
        self.executor = ThreadPoolExecutor(max_workers=4)  # 用于并行处理

        # API配置
        self.api_base_url = WEATHER_ALARM_CONFIG.get("api_base_url")
        self.api_key = WEATHER_ALARM_CONFIG.get("api_key")
        self.api_type = WEATHER_ALARM_CONFIG.get("type")
        self.max_areas_per_request = WEATHER_ALARM_CONFIG.get("max_areas_per_request", 20)
        self.request_timeout = WEATHER_ALARM_CONFIG.get("request_timeout", 30)
        self.max_retries = WEATHER_ALARM_CONFIG.get("max_retries", 3)
        self.retry_interval = WEATHER_ALARM_CONFIG.get("retry_interval", 5)
        
    async def initialize(self):
        """初始化连接"""
        logger.info("初始化天气预警处理器...")

        # 初始化PostgreSQL连接池
        await self._init_pg_pool()

        # 初始化MySQL连接
        await self._init_mysql_conn()

        # 初始化内容解析器
        self.content_parser = AlarmContentParser(self.mysql_conn)

        # 初始化HTTP会话
        self.session = aiohttp.ClientSession(
            timeout=aiohttp.ClientTimeout(total=self.request_timeout)
        )

        logger.info("天气预警处理器初始化完成")
    
    async def _init_pg_pool(self):
        """初始化PostgreSQL连接池"""
        try:
            # 解析PostgreSQL URL
            import urllib.parse as urlparse
            parsed = urlparse.urlparse(PG_URL)
            password = urlparse.unquote_plus(parsed.password) if parsed.password else None
            
            self.pg_pool = await asyncpg.create_pool(
                host=parsed.hostname,
                port=parsed.port or 5432,
                user=parsed.username,
                password=password,
                database=parsed.path[1:],
                min_size=5,
                max_size=20,
                command_timeout=60
            )
            logger.info("PostgreSQL连接池初始化完成")
        except Exception as e:
            logger.error(f"PostgreSQL连接池初始化失败: {e}")
            raise
    
    async def _init_mysql_conn(self):
        """初始化MySQL连接"""
        try:
            self.mysql_conn = pymysql.connect(
                host=MYSQL_CONFIG["host"],
                port=MYSQL_CONFIG["port"],
                user=MYSQL_CONFIG["username"],
                password=MYSQL_CONFIG["password"],
                database=MYSQL_CONFIG["database"],
                charset='utf8mb4',
                autocommit=True
            )
            logger.info("MySQL连接初始化完成")
        except Exception as e:
            logger.error(f"MySQL连接初始化失败: {e}")
            raise
    
    async def close(self):
        """关闭连接"""
        if self.pg_pool:
            await self.pg_pool.close()
            logger.info("PostgreSQL连接池已关闭")

        if self.mysql_conn:
            self.mysql_conn.close()
            logger.info("MySQL连接已关闭")

        if self.session:
            await self.session.close()
            logger.info("HTTP会话已关闭")

        if self.executor:
            self.executor.shutdown(wait=True)
            logger.info("线程池已关闭")
    
    def get_weather_id_mapping(self) -> Dict[int, str]:
        """从MySQL获取weather_id和data_code的映射"""
        try:
            with self.mysql_conn.cursor() as cursor:
                sql = """
                SELECT weather_id, data_code 
                FROM sys_region_code 
                WHERE weather_id IS NOT NULL
                """
                cursor.execute(sql)
                results = cursor.fetchall()
                
                mapping = {row[0]: row[1] for row in results}
                logger.info(f"获取到 {len(mapping)} 个weather_id映射")
                return mapping
                
        except Exception as e:
            logger.error(f"获取weather_id映射失败: {e}")
            return {}
    
    def chunk_weather_ids(self, weather_ids: List[int]) -> List[List[int]]:
        """将weather_id列表分组，每组最多20个"""
        chunks = []
        for i in range(0, len(weather_ids), self.max_areas_per_request):
            chunk = weather_ids[i:i + self.max_areas_per_request]
            chunks.append(chunk)
        return chunks
    
    async def fetch_alarm_data(self, weather_ids: List[int]) -> Optional[Dict]:
        """获取预警数据"""
        if not weather_ids:
            return None
        
        # 构建请求URL
        area_param = "|".join(map(str, weather_ids))
        params = {
            "type": self.api_type,
            "key": self.api_key,
            "area": area_param
        }
        
        url = f"{self.api_base_url}?{urlencode(params)}"
        
        for attempt in range(self.max_retries):
            try:
                async with self.session.get(url) as response:
                    if response.status == 200:
                        # 先获取文本内容
                        text_content = await response.text()

                        # 尝试解析为JSON
                        try:
                            data = json.loads(text_content)

                            # 检查是否有错误
                            if isinstance(data, dict) and "alarm" in data:
                                return data
                            else:
                                logger.warning(f"API返回异常数据: {data}")
                                return None
                        except json.JSONDecodeError:
                            # 如果不是JSON格式，记录原始内容
                            logger.warning(f"API返回非JSON格式数据: {text_content[:200]}...")

                            # 检查是否是错误信息
                            if any(error_code in text_content for error_code in ['CC1000', 'CC1003', 'CC1004', 'CC1005', 'CC1006', 'CC1100', 'CC1101', 'CC1102', 'CC1200', 'CC1201', 'CC1300', 'CC1301', 'CC1302', 'CC1303']):
                                logger.error(f"API返回错误信息: {text_content}")

                            return None
                    else:
                        logger.warning(f"API请求失败，状态码: {response.status}")

            except Exception as e:
                logger.error(f"请求预警数据失败 (尝试 {attempt + 1}/{self.max_retries}): {e}")

                if attempt < self.max_retries - 1:
                    await asyncio.sleep(self.retry_interval)
        
        return None
    
    async def process_alarm_data(self, alarm_data: Dict, weather_id_mapping: Dict[int, str]):
        """处理预警数据"""
        if not alarm_data or "alarm" not in alarm_data:
            return

        alarm_info = alarm_data["alarm"]

        # 收集所有需要查询的geo_code
        geo_codes = []
        for weather_id_str, weather_data in alarm_info.items():
            try:
                weather_id = int(weather_id_str)
                region_code = weather_id_mapping.get(weather_id)
                if region_code:
                    geo_codes.append(region_code)
            except Exception:
                continue

        # 批量获取区域数据
        region_data = self.content_parser.get_region_data_by_geo_codes(geo_codes)

        # 并行处理预警数据
        tasks = []
        for weather_id_str, weather_data in alarm_info.items():
            try:
                weather_id = int(weather_id_str)
                region_code = weather_id_mapping.get(weather_id)

                if not region_code:
                    logger.warning(f"未找到weather_id {weather_id}对应的region_code")
                    continue

                # 创建处理任务
                task = self._process_single_weather_alarm(weather_id, region_code, weather_data, region_data)
                tasks.append(task)

            except Exception as e:
                logger.error(f"处理weather_id {weather_id_str}的预警数据失败: {e}")

        # 并行执行所有任务
        if tasks:
            await asyncio.gather(*tasks, return_exceptions=True)
    
    async def _process_single_weather_alarm(self, weather_id: int, region_code: str, weather_data: Dict, region_data: Dict[str, List[Dict]]):
        """处理单个weather_id的预警信息"""
        if "1001003" not in weather_data:
            return

        alarm_list = weather_data["1001003"]

        # 获取当前数据库中该weather_id未解除的预警
        current_alarms = await self._get_current_alarms(weather_id)
        current_alarm_infos = {alarm["alarm_info"] for alarm in current_alarms}

        # 处理API返回的预警信息
        api_alarm_infos = set()

        for alarm_item in alarm_list:
            try:
                alarm_info = alarm_item.get("010")  # 预警信息字段
                alarm_content = alarm_item.get("009")  # 预警发布内容

                if not alarm_info:
                    continue

                api_alarm_infos.add(alarm_info)

                # 解析预警内容获取子区域信息
                sub_regions = []
                if alarm_content:
                    try:
                        # 在线程池中执行内容解析（CPU密集型任务）
                        loop = asyncio.get_event_loop()
                        sub_regions = await loop.run_in_executor(
                            self.executor,
                            self.content_parser.parse_alarm_content,
                            alarm_content,
                            region_code,
                            region_data
                        )
                    except Exception as e:
                        logger.error(f"解析预警内容失败: {e}")

                # 检查是否已存在
                if alarm_info in current_alarm_infos:
                    # 更新现有记录
                    await self._update_alarm(weather_id, alarm_info, alarm_item, region_code, sub_regions)
                else:
                    # 新增记录
                    await self._insert_alarm(weather_id, alarm_info, alarm_item, region_code, sub_regions)

            except Exception as e:
                logger.error(f"处理预警项失败: {e}")

        # 处理已解除的预警（在数据库中存在但API中不存在的）
        resolved_alarms = current_alarm_infos - api_alarm_infos
        for resolved_alarm_info in resolved_alarms:
            await self._resolve_alarm(weather_id, resolved_alarm_info)

    async def _get_current_alarms(self, weather_id: int) -> List[Dict]:
        """获取当前数据库中该weather_id未解除的预警"""
        try:
            async with self.pg_pool.acquire() as conn:
                rows = await conn.fetch("""
                    SELECT alarm_info, publish_status
                    FROM weather_alarm
                    WHERE weather_id = $1 AND cancel_time IS NULL
                """, weather_id)

                return [{"alarm_info": row["alarm_info"], "publish_status": row["publish_status"]} for row in rows]
        except Exception as e:
            logger.error(f"获取当前预警失败: {e}")
            return []

    async def _insert_alarm(self, weather_id: int, alarm_info: str, alarm_item: Dict, region_code: str, sub_regions: List[Dict] = None):
        """新增预警记录"""
        try:
            # 准备子区域信息
            sub_code_list = []
            sub_name_list = []
            if sub_regions:
                sub_code_list = [region['data_code'] for region in sub_regions]
                sub_name_list = [region['data_name'] for region in sub_regions]

            sub_code = ','.join(sub_code_list) if sub_code_list else None
            sub_name = ','.join(sub_name_list) if sub_name_list else None

            async with self.pg_pool.acquire() as conn:
                await conn.execute("""
                    INSERT INTO weather_alarm (
                        weather_id, region_code, province_name, city_name, county_name,
                        sub_code, sub_name,
                        alarm_category_code, alarm_category_name, alarm_level_code, alarm_level_name,
                        publish_time, alarm_content, alarm_info, jump_url, alarm_title, publish_status
                    ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14, $15, $16, $17)
                """,
                    weather_id, region_code,
                    alarm_item.get("001"),  # 省级名称
                    alarm_item.get("002"),  # 市级名称
                    alarm_item.get("003"),  # 县级名称
                    sub_code,  # 子区域代码
                    sub_name,  # 子区域名称
                    alarm_item.get("004"),  # 预警类别编号
                    alarm_item.get("005"),  # 预警类别名称
                    alarm_item.get("006"),  # 预警级别编号
                    alarm_item.get("007"),  # 预警级别名称
                    self._parse_datetime(alarm_item.get("008")),  # 预警发布时间
                    alarm_item.get("009"),  # 预警发布内容
                    alarm_item.get("010"),  # 预警信息
                    alarm_item.get("011"),  # 天气网跳转地址
                    alarm_item.get("012"),  # 预警标题
                    alarm_item.get("013", "Alert")  # 发布状态
                )
                logger.info(f"新增预警: weather_id={weather_id}, alarm_info={alarm_info}, 子区域数量={len(sub_regions) if sub_regions else 0}")
        except Exception as e:
            logger.error(f"新增预警失败: {e}")

    async def _update_alarm(self, weather_id: int, alarm_info: str, alarm_item: Dict, region_code: str, sub_regions: List[Dict] = None):
        """更新预警记录"""
        try:
            # 准备子区域信息
            sub_code_list = []
            sub_name_list = []
            if sub_regions:
                sub_code_list = [region['data_code'] for region in sub_regions]
                sub_name_list = [region['data_name'] for region in sub_regions]

            sub_code = ','.join(sub_code_list) if sub_code_list else None
            sub_name = ','.join(sub_name_list) if sub_name_list else None

            async with self.pg_pool.acquire() as conn:
                await conn.execute("""
                    UPDATE weather_alarm SET
                        province_name = $3, city_name = $4, county_name = $5,
                        sub_code = $6, sub_name = $7,
                        alarm_category_code = $8, alarm_category_name = $9,
                        alarm_level_code = $10, alarm_level_name = $11,
                        publish_time = $12, alarm_content = $13, jump_url = $14,
                        alarm_title = $15, publish_status = $16
                    WHERE weather_id = $1 AND alarm_info = $2 AND cancel_time IS NULL
                """,
                    weather_id, alarm_info,
                    alarm_item.get("001"),  # 省级名称
                    alarm_item.get("002"),  # 市级名称
                    alarm_item.get("003"),  # 县级名称
                    sub_code,  # 子区域代码
                    sub_name,  # 子区域名称
                    alarm_item.get("004"),  # 预警类别编号
                    alarm_item.get("005"),  # 预警类别名称
                    alarm_item.get("006"),  # 预警级别编号
                    alarm_item.get("007"),  # 预警级别名称
                    self._parse_datetime(alarm_item.get("008")),  # 预警发布时间
                    alarm_item.get("009"),  # 预警发布内容
                    alarm_item.get("011"),  # 天气网跳转地址
                    alarm_item.get("012"),  # 预警标题
                    alarm_item.get("013", "Alert")  # 发布状态
                )
                logger.info(f"更新预警: weather_id={weather_id}, alarm_info={alarm_info}, 子区域数量={len(sub_regions) if sub_regions else 0}")
        except Exception as e:
            logger.error(f"更新预警失败: {e}")

    async def _resolve_alarm(self, weather_id: int, alarm_info: str):
        """解除预警"""
        try:
            # 使用Python的datetime.now()获取当前时间，与调度器保持一致
            current_time = datetime.now()

            async with self.pg_pool.acquire() as conn:
                await conn.execute("""
                    UPDATE weather_alarm SET
                        cancel_time = $3
                    WHERE weather_id = $1 AND alarm_info = $2 AND cancel_time IS NULL
                """, weather_id, alarm_info, current_time)
                logger.info(f"解除预警: weather_id={weather_id}, alarm_info={alarm_info}, cancel_time={current_time}")
        except Exception as e:
            logger.error(f"解除预警失败: {e}")

    def _parse_datetime(self, datetime_str: str) -> Optional[datetime]:
        """解析日期时间字符串"""
        if not datetime_str:
            return None

        try:
            # 尝试解析格式: "2019-05-21 16:50"
            return datetime.strptime(datetime_str, "%Y-%m-%d %H:%M")
        except ValueError:
            try:
                # 尝试其他可能的格式
                return datetime.strptime(datetime_str, "%Y-%m-%d %H:%M:%S")
            except ValueError:
                logger.warning(f"无法解析日期时间: {datetime_str}")
                return None

    async def run_once(self):
        """执行一次预警信息获取和处理"""
        try:
            logger.info("开始获取预警信息...")

            # 1. 获取weather_id映射
            weather_id_mapping = self.get_weather_id_mapping()
            if not weather_id_mapping:
                logger.warning("未获取到weather_id映射，跳过本次执行")
                return

            weather_ids = list(weather_id_mapping.keys())
            logger.info(f"需要查询 {len(weather_ids)} 个weather_id的预警信息")

            # 2. 分组请求
            weather_id_chunks = self.chunk_weather_ids(weather_ids)
            logger.info(f"分为 {len(weather_id_chunks)} 组进行请求")

            # 3. 并发请求所有分组
            tasks = []
            for chunk in weather_id_chunks:
                task = self.fetch_alarm_data(chunk)
                tasks.append(task)

            results = await asyncio.gather(*tasks, return_exceptions=True)

            # 4. 处理所有结果
            for i, result in enumerate(results):
                if isinstance(result, Exception):
                    logger.error(f"第 {i+1} 组请求失败: {result}")
                    continue

                if result:
                    await self.process_alarm_data(result, weather_id_mapping)

            logger.info("预警信息获取和处理完成")

        except Exception as e:
            logger.error(f"执行预警信息获取失败: {e}")

async def main():
    """主函数"""
    processor = WeatherAlarmProcessor()

    try:
        await processor.initialize()
        await processor.run_once()
    finally:
        await processor.close()

if __name__ == "__main__":
    asyncio.run(main())
