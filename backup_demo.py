#!/usr/bin/env python3
# coding: utf-8
"""
天气数据备份功能演示
展示新的按年/月/日划分的备份目录结构
"""

import os
import sys
from pathlib import Path
from datetime import datetime, timedelta

# 添加src目录到Python路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from weather_download import WeatherDownloader
import logging

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


def demo_backup_structure():
    """演示新的备份目录结构"""
    print("=" * 80)
    print("天气数据备份功能演示")
    print("=" * 80)
    
    print("\n1. 新的备份目录结构:")
    print("   backup/")
    print("   ├── 2025/           # 年份目录")
    print("   │   ├── 01/         # 月份目录")
    print("   │   │   ├── 15/     # 日期目录")
    print("   │   │   │   ├── weather_data_001.nc")
    print("   │   │   │   ├── weather_data_002.nc")
    print("   │   │   │   └── weather_data_003_142530.nc  # 重名文件添加时间戳")
    print("   │   │   ├── 16/")
    print("   │   │   │   └── weather_data_004.nc")
    print("   │   │   └── 17/")
    print("   │   │       └── weather_data_005.nc")
    print("   │   ├── 02/")
    print("   │   │   └── 01/")
    print("   │   │       └── weather_data_006.nc")
    print("   │   └── 03/")
    print("   │       └── 15/")
    print("   │           └── weather_data_007.nc")
    print("   └── 2024/")
    print("       └── 12/")
    print("           └── 31/")
    print("               └── old_weather_data.nc")
    
    print("\n2. 主要优势:")
    print("   ✓ 按日期自动分类，便于查找特定日期的备份文件")
    print("   ✓ 避免单个目录文件过多导致的性能问题")
    print("   ✓ 支持按日期批量清理旧备份")
    print("   ✓ 自动处理重名文件（添加时间戳）")
    print("   ✓ 保持与旧格式备份文件的兼容性")
    print("   ✓ 清理时会自动删除空的日期/月份/年份目录")


def demo_usage_examples():
    """演示使用示例"""
    print("\n" + "=" * 80)
    print("使用示例")
    print("=" * 80)
    
    print("\n1. 基本备份操作:")
    print("   ```python")
    print("   from weather_download import WeatherDownloader")
    print("   ")
    print("   # 创建下载器实例")
    print("   downloader = WeatherDownloader('gz_mpfv3')")
    print("   ")
    print("   # 备份文件（会自动按当前日期创建目录结构）")
    print("   success = downloader.move_to_backup('/path/to/weather_data.nc')")
    print("   if success:")
    print("       print('备份成功')")
    print("   ```")
    
    print("\n2. 批量备份现有文件:")
    print("   ```python")
    print("   # 备份NC文件目录中的所有现有文件")
    print("   success = downloader.backup_existing_files()")
    print("   if success:")
    print("       print('批量备份成功')")
    print("   ```")
    
    print("\n3. 清理旧备份:")
    print("   ```python")
    print("   # 清理30天前的备份文件")
    print("   downloader.cleanup_old_backups(keep_days=30)")
    print("   ")
    print("   # 清理7天前的备份文件")
    print("   downloader.cleanup_old_backups(keep_days=7)")
    print("   ```")
    
    print("\n4. 便捷函数:")
    print("   ```python")
    print("   from weather_download import backup_weather_file")
    print("   ")
    print("   # 直接备份文件")
    print("   success = backup_weather_file('/path/to/file.nc', 'gz_mpfv3')")
    print("   ```")


def demo_configuration():
    """演示配置选项"""
    print("\n" + "=" * 80)
    print("配置选项")
    print("=" * 80)
    
    print("\n在 config.yml 中的相关配置:")
    print("```yaml")
    print("weather_download:")
    print("  # 备份配置")
    print("  backup_keep_days: 30        # 备份文件保留天数")
    print("  auto_cleanup_backups: true  # 是否自动清理旧备份")
    print("  enable_backup: true         # 是否启用备份功能")
    print("```")
    
    print("\n配置说明:")
    print("- backup_keep_days: 设置备份文件的保留天数，超过此天数的备份会被自动清理")
    print("- auto_cleanup_backups: 是否在下载新文件时自动清理旧备份")
    print("- enable_backup: 总开关，控制是否启用备份功能")


def demo_migration_guide():
    """演示迁移指南"""
    print("\n" + "=" * 80)
    print("迁移指南")
    print("=" * 80)
    
    print("\n如果您之前使用的是旧的备份格式（文件直接存储在backup目录下），")
    print("新版本会自动兼容处理：")
    
    print("\n1. 新备份文件会使用新的目录结构")
    print("2. 旧备份文件会继续保留在原位置")
    print("3. 清理功能会同时处理新旧两种格式的备份文件")
    print("4. 不需要手动迁移现有的备份文件")
    
    print("\n建议的迁移步骤（可选）:")
    print("1. 让系统运行一段时间，新备份会使用新格式")
    print("2. 定期清理会自动处理旧格式的过期文件")
    print("3. 如需手动整理，可以将旧格式文件按日期移动到对应的新目录结构中")


def show_current_backup_status():
    """显示当前备份状态"""
    print("\n" + "=" * 80)
    print("当前备份状态检查")
    print("=" * 80)
    
    try:
        # 检查各种数据类型的备份目录
        data_types = ["gz_mpfv3", "gz_didiforecast1hTEM", "gz_didiforecast1hPRE", 
                     "gz_didiforecast1hWEATHER", "gz_didiforecast1hVIS"]
        
        for data_type in data_types:
            try:
                downloader = WeatherDownloader(data_type)
                backup_dir = downloader.backup_dir
                
                print(f"\n{data_type} ({downloader.data_type_config['name']}):")
                print(f"  备份目录: {backup_dir}")
                
                if backup_dir.exists():
                    # 统计备份文件
                    total_files = 0
                    date_dirs = []
                    
                    # 统计新格式的备份文件
                    for year_dir in backup_dir.glob("*"):
                        if year_dir.is_dir() and year_dir.name.isdigit():
                            for month_dir in year_dir.glob("*"):
                                if month_dir.is_dir() and month_dir.name.isdigit():
                                    for day_dir in month_dir.glob("*"):
                                        if day_dir.is_dir() and day_dir.name.isdigit():
                                            files = list(day_dir.glob("*.nc"))
                                            if files:
                                                total_files += len(files)
                                                date_dirs.append(f"{year_dir.name}-{month_dir.name}-{day_dir.name}")
                    
                    # 统计旧格式的备份文件
                    old_files = [f for f in backup_dir.glob("*.nc") if f.is_file()]
                    total_files += len(old_files)
                    
                    print(f"  总备份文件数: {total_files}")
                    if date_dirs:
                        print(f"  最近备份日期: {sorted(date_dirs)[-1] if date_dirs else '无'}")
                    if old_files:
                        print(f"  旧格式文件数: {len(old_files)}")
                else:
                    print("  备份目录不存在")
                    
            except Exception as e:
                print(f"  检查失败: {e}")
                
    except Exception as e:
        print(f"检查备份状态时发生错误: {e}")


if __name__ == "__main__":
    # 运行演示
    demo_backup_structure()
    demo_usage_examples()
    demo_configuration()
    demo_migration_guide()
    show_current_backup_status()
    
    print("\n" + "=" * 80)
    print("演示完成")
    print("=" * 80)
    print("\n现在您的天气数据备份将按照年/月/日的目录结构自动组织，")
    print("这样可以更好地管理和查找历史备份文件。")
