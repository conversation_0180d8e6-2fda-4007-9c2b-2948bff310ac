#!/usr/bin/env python3
# coding: utf-8
"""
混合架构：多进程 + 协程   NetCDF → weather_cell_1h
- 使用多进程处理CPU密集型的NetCDF数据解析
- 使用协程处理I/O密集型的数据库操作和存储过程调用
- 减少内存占用，提高I/O并发性能
- 支持四种天气数据类型：PRE（降水）、WEATHER（天气现象）、VIS（能见度）、TEM（温度）
- 每个tar.gz解压出来的多个NC文件一起处理，使用upsert机制更新数据
- 支持从接口下载天气数据，处理后自动备份
"""
import os, math, uuid, multiprocessing as mp, asyncio
import numpy as np, pandas as pd, xarray as xr
from sqlalchemy import create_engine
import asyncpg
import logging
from typing import Dict, List
from concurrent.futures import ProcessPoolExecutor
import argparse
from datetime import datetime, timedelta

# 导入统一配置
from config import (
    PG_URL, GRID_X_MIN, GRID_Y_MIN, GRID_X_MAX, GRID_Y_MAX, GRID_SIZE,
    STEP, LON_OFF, LAT_OFF, PROCESSING_CONFIG, WEATHER_DOWNLOAD_CONFIG
)

# 导入天气数据下载模块
try:
    from weather_download import download_and_get_latest_weather_data, process_and_backup_weather_file
    WEATHER_DOWNLOAD_AVAILABLE = True
except ImportError:
    WEATHER_DOWNLOAD_AVAILABLE = False

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# ─────────────── 全局配置 ───────────────
# 格网生成顺序：先Y后X，所以行数是Y方向，列数是X方向
NC_NROWS = int(round((GRID_Y_MAX-GRID_Y_MIN)/GRID_SIZE))   # 820 (Y方向)
NC_NCOLS = int(round((GRID_X_MAX-GRID_X_MIN)/GRID_SIZE))   # 875 (X方向)

PROCESSES = PROCESSING_CONFIG["max_processes"] or max(1, mp.cpu_count() - 1)  # 并行进程数拉满
CHUNK_SIZE = PROCESSING_CONFIG["chunk_size"]                                   # 一个进程一次处理多少 time index

# 协程配置 - 优化为高性能模式
MAX_CONCURRENT_DB_OPERATIONS = 20  # 增加并发数据库操作数
MAX_CONCURRENT_PROCEDURES = 10     # 提高存储过程并发数到10个

# 1小时天气数据类型配置
WEATHER_1H_TYPES = {
    'PRE': {
        'var_name': 'PRE',
        'data_type': 'float',
        'scale_factor': 0.1,
        'description': '降水量'
    },
    'WEATHER': {
        'var_name': 'WEATHER',
        'data_type': 'int',
        'scale_factor': 1.0,
        'description': '天气现象'
    },
    'VIS': {
        'var_name': 'VIS',
        'data_type': 'int',
        'scale_factor': 1.0,
        'description': '能见度'
    },
    'TEM': {
        'var_name': 'TEM',
        'data_type': 'float',
        'scale_factor': 0.1,
        'description': '温度'
    }
}

# ========== 标准格网编码/解码函数 ==========
# 0.01° 等纬经标准格网的 ID 方案

def encode_cell(lon: float, lat: float) -> int:
    """经纬 → cell_id（32bit 整数）"""
    ix = int(math.floor((lon + LON_OFF) / STEP))
    iy = int(math.floor((lat + LAT_OFF) / STEP))
    return (iy << 16) | ix          # 行主序打包

def decode_cell(cell_id: int) -> tuple[float, float]:
    """cell_id → 左下角 (lon_min, lat_min)"""
    ix = cell_id & 0xFFFF
    iy = cell_id >> 16
    lon_min = ix * STEP - LON_OFF
    lat_min = iy * STEP - LAT_OFF
    return lon_min, lat_min

def get_netcdf_indices_from_coords(lon: float, lat: float,
                                   nc_lon_min: float, nc_lat_min: float) -> tuple[int, int]:
    """根据经纬度坐标直接计算NetCDF索引"""
    lon_idx = int(round((lon - nc_lon_min) / STEP))
    lat_idx = int(round((lat - nc_lat_min) / STEP))
    return lat_idx, lon_idx

def get_nc_files_path(data_types: list = None):
    """
    获取要处理的NC文件路径
    如果启用下载功能，先尝试下载最新数据
    优先处理PRE数据（因为比较小，下载快）

    Args:
        data_types: 数据类型列表，如 ['PRE', 'WEATHER', 'VIS', 'TEM']

    Returns:
        tuple: (nc_files_dict, downloaded_files_info, data_types_used)
    """
    # 如果没有指定数据类型，使用所有类型，但PRE优先
    if data_types is None:
        data_types = ['PRE', 'WEATHER', 'VIS', 'TEM']  # PRE放在第一位
    elif 'PRE' in data_types and data_types[0] != 'PRE':
        # 确保PRE在第一位
        data_types = ['PRE'] + [dt for dt in data_types if dt != 'PRE']

    # 数据类型到下载类型的映射
    download_type_map = {
        'PRE': 'gz_didiforecast1hPRE',
        'WEATHER': 'gz_didiforecast1hWEATHER',
        'VIS': 'gz_didiforecast1hVIS',
        'TEM': 'gz_didiforecast1hTEM'
    }

    nc_files_dict = {}
    downloaded_files_info = {}

    # 检查是否启用下载功能
    if WEATHER_DOWNLOAD_CONFIG.get("enable_download", False) and WEATHER_DOWNLOAD_AVAILABLE:
        logger.info("启用下载功能，正在获取1小时天气数据")

        for data_type in data_types:
            download_type = download_type_map.get(data_type)
            if not download_type:
                logger.warning(f"未知的数据类型: {data_type}")
                continue

            logger.info(f"正在下载数据类型 [{data_type}] ({download_type})")
            try:
                # 下载数据
                latest_files = download_and_get_latest_weather_data(download_type)
                if latest_files:
                    # 获取解压后的所有NC文件
                    nc_files = find_nc_files_in_directories(data_type)
                    if nc_files:
                        nc_files_dict[data_type] = nc_files
                        downloaded_files_info[data_type] = {
                            'downloaded': True,
                            'download_type': download_type,
                            'files': nc_files
                        }
                        logger.info(f"成功下载并获取 {data_type} 数据: {len(nc_files)} 个文件")
                    else:
                        logger.warning(f"下载 {data_type} 数据后未找到NC文件")
                else:
                    logger.warning(f"下载 {data_type} 数据失败，尝试使用本地文件")
                    # 下载失败，尝试使用本地文件
                    nc_files = find_nc_files_in_directories(data_type)
                    if nc_files:
                        nc_files_dict[data_type] = nc_files
                        downloaded_files_info[data_type] = {
                            'downloaded': False,
                            'download_type': download_type,
                            'files': nc_files
                        }
                        logger.info(f"使用本地 {data_type} 文件: {len(nc_files)} 个")
            except Exception as e:
                logger.error(f"处理数据类型 {data_type} 时发生错误: {e}")
                # 发生错误，尝试使用本地文件
                nc_files = find_nc_files_in_directories(data_type)
                if nc_files:
                    nc_files_dict[data_type] = nc_files
                    downloaded_files_info[data_type] = {
                        'downloaded': False,
                        'download_type': download_type,
                        'files': nc_files
                    }
                    logger.info(f"使用本地 {data_type} 文件: {len(nc_files)} 个")
    else:
        # 未启用下载功能，直接使用本地文件
        logger.info("未启用下载功能，使用本地NC文件")
        for data_type in data_types:
            nc_files = find_nc_files_in_directories(data_type)
            if nc_files:
                nc_files_dict[data_type] = nc_files
                downloaded_files_info[data_type] = {
                    'downloaded': False,
                    'download_type': download_type_map.get(data_type),
                    'files': nc_files
                }
                logger.info(f"使用本地 {data_type} 文件: {len(nc_files)} 个")
            else:
                logger.warning(f"未找到 {data_type} 类型的本地NC文件")

    return nc_files_dict, downloaded_files_info, data_types

class AsyncDatabaseManager:
    """异步数据库管理器"""

    def __init__(self, pg_url: str, max_connections: int = 50):  # 增加连接池大小
        self.pg_url = pg_url
        self.max_connections = max_connections
        self.pool = None
    
    async def initialize(self):
        """初始化连接池"""
        # 解析PostgreSQL URL
        import urllib.parse as urlparse
        parsed = urlparse.urlparse(self.pg_url)

        # 解码密码中的特殊字符
        password = urlparse.unquote_plus(parsed.password) if parsed.password else None

        logger.info(f"连接数据库: {parsed.hostname}:{parsed.port or 5432}/{parsed.path[1:]} 用户: {parsed.username}")

        self.pool = await asyncpg.create_pool(
            host=parsed.hostname,
            port=parsed.port or 5432,
            user=parsed.username,
            password=password,
            database=parsed.path[1:],  # 去掉开头的 '/'
            min_size=5,
            max_size=self.max_connections,
            command_timeout=300
        )
        logger.info(f"数据库连接池初始化完成，最大连接数: {self.max_connections}")
    
    async def close(self):
        """关闭连接池"""
        if self.pool:
            await self.pool.close()
            logger.info("数据库连接池已关闭")
    
    async def upsert_weather_data(self, timestamp, time_data: Dict):
        """异步upsert天气数据"""
        async with self.pool.acquire() as conn:
            async with conn.transaction():
                # 1. 确定当前处理的数据类型
                data_types_in_batch = set()
                for cell_id, weather_data in time_data.items():
                    for data_type in ['PRE', 'WEATHER', 'VIS', 'TEM']:
                        if weather_data.get(data_type) is not None:
                            data_types_in_batch.add(data_type)

                # 2. 先将该时间点的相关字段设置为null
                if data_types_in_batch:
                    update_clauses = []
                    for data_type in data_types_in_batch:
                        if data_type == 'PRE':
                            update_clauses.append("pre = NULL")
                        elif data_type == 'WEATHER':
                            update_clauses.append("weather = NULL")
                        elif data_type == 'VIS':
                            update_clauses.append("vis = NULL")
                        elif data_type == 'TEM':
                            update_clauses.append("tem = NULL")

                    if update_clauses:
                        update_sql = f"""
                            UPDATE weather_cell_1h
                            SET {', '.join(update_clauses)}
                            WHERE pre_time = $1
                        """
                        await conn.execute(update_sql, timestamp)
                        logger.info(f"已将时间 {timestamp} 的字段 {list(data_types_in_batch)} 设置为NULL")

                # 3. 创建临时表
                tmp_tbl = f"tmp_weather_1h_{uuid.uuid4().hex}"
                await conn.execute(f"""
                    CREATE TEMP TABLE {tmp_tbl} (
                        pre_time TIMESTAMP NOT NULL,
                        cell_id INTEGER NOT NULL,
                        pre NUMERIC(8,3),
                        weather INTEGER,
                        vis INTEGER,
                        tem NUMERIC(8,3)
                    ) ON COMMIT DROP;
                """)

                # 4. 准备数据
                data_rows = []
                for cell_id, weather_data in time_data.items():
                    pre_val = weather_data.get('PRE')
                    weather_val = weather_data.get('WEATHER')
                    vis_val = weather_data.get('VIS')
                    tem_val = weather_data.get('TEM')

                    data_rows.append((
                        timestamp, cell_id,
                        float(pre_val) if pre_val is not None else None,
                        int(weather_val) if weather_val is not None else None,
                        int(vis_val) if vis_val is not None else None,
                        float(tem_val) if tem_val is not None else None
                    ))

                # 5. 批量插入临时表
                await conn.copy_records_to_table(
                    tmp_tbl,
                    records=data_rows,
                    columns=['pre_time', 'cell_id', 'pre', 'weather', 'vis', 'tem']
                )

                # 6. Upsert到目标表
                result = await conn.execute(f"""
                    INSERT INTO weather_cell_1h (pre_time, cell_id, pre, weather, vis, tem)
                    SELECT pre_time, cell_id, pre, weather, vis, tem
                    FROM {tmp_tbl}
                    ON CONFLICT (pre_time, cell_id) DO UPDATE SET
                        pre = CASE WHEN EXCLUDED.pre IS NOT NULL THEN EXCLUDED.pre ELSE weather_cell_1h.pre END,
                        weather = CASE WHEN EXCLUDED.weather IS NOT NULL THEN EXCLUDED.weather ELSE weather_cell_1h.weather END,
                        vis = CASE WHEN EXCLUDED.vis IS NOT NULL THEN EXCLUDED.vis ELSE weather_cell_1h.vis END,
                        tem = CASE WHEN EXCLUDED.tem IS NOT NULL THEN EXCLUDED.tem ELSE weather_cell_1h.tem END
                """)

                return len(data_rows)
    
    async def cleanup_null_records(self):
        """清理空记录 - 删除所有字段都为NULL的记录"""
        async with self.pool.acquire() as conn:
            result = await conn.execute("""
                DELETE FROM weather_cell_1h
                WHERE pre IS NULL AND weather IS NULL AND vis IS NULL AND tem IS NULL
            """)
            deleted_count = int(result.split()[-1]) if result and result.split() else 0
            if deleted_count > 0:
                logger.info(f"清理了 {deleted_count} 条空记录")
            return result
    
    async def call_stored_procedure(self, procedure_type: str, timestamp):
        """异步调用存储过程，失败后不重试"""
        try:
            # 设置10分钟超时，避免无限等待
            async with asyncio.timeout(600):  # 10分钟超时
                async with self.pool.acquire() as conn:
                    if procedure_type == "polygon":
                        await conn.execute("""
                            CALL sp_precip_polygon($1::timestamp, $2::timestamp, $3::text, $4::text[], $5::text, $6::text, $7::text, $8::text)
                        """, timestamp, None, 'get_rainfall_type_hourly', ['pre'], 'weather_cell_1h',
                             'pre', 'forecast_precipitation_hourly_polygon', 'forecast_precipitation_hourly_relation')

                    elif procedure_type == "line":
                        await conn.execute("""
                            CALL sp_precip_line($1::timestamp, $2::timestamp, $3::text, $4::text[], $5::text, $6::text, $7::text)
                        """, timestamp, None, 'get_rainfall_type_hourly', ['pre'], 'weather_cell_1h',
                             'pre', 'forecast_precipitation_hourly_line')

                    return True
        except asyncio.TimeoutError:
            logger.error(f"存储过程调用超时 {procedure_type} {timestamp}: 超过10分钟，放弃执行")
            return False
        except Exception as e:
            logger.error(f"存储过程调用失败 {procedure_type} {timestamp}: {e}")
            return False

def process_nc_files_cpu_intensive(nc_files_dict: dict, t_index_slice, valid_cells) -> Dict:
    """
    CPU密集型任务：在独立进程中处理NetCDF文件
    返回处理后的数据，不直接操作数据库
    """
    all_time_data = {}
    
    for data_type, nc_files in nc_files_dict.items():
        logger.info(f"PID {os.getpid()} 开始处理 {data_type} 类型数据")
        
        var_config = WEATHER_1H_TYPES[data_type]
        var_name = var_config['var_name']
        
        for nc_file in nc_files:
            try:
                ds = xr.open_dataset(nc_file, engine="netcdf4")
                
                if var_name not in ds.data_vars:
                    continue
                
                data_var = ds[var_name]
                t_values = ds['time'].values
                
                # 获取NetCDF坐标范围
                lon_vals = ds.coords['lon'].values
                lat_vals = ds.coords['lat'].values
                nc_lon_min, nc_lat_min = lon_vals[0], lat_vals[0]
                
                # 预计算NetCDF索引
                nc_lat_indices = []
                nc_lon_indices = []
                for cell_id in valid_cells:
                    lon_min, lat_min = decode_cell(cell_id)
                    lat_idx, lon_idx = get_netcdf_indices_from_coords(
                        lon_min, lat_min, nc_lon_min, nc_lat_min)
                    nc_lat_indices.append(lat_idx)
                    nc_lon_indices.append(lon_idx)
                
                nc_lat_indices = np.array(nc_lat_indices)
                nc_lon_indices = np.array(nc_lon_indices)
                
                # 处理时间切片
                for ti in range(min(t_index_slice.start, len(t_values)),
                               min(t_index_slice.stop, len(t_values))):
                    
                    # 解析时间戳
                    if 'reftime' in ds.coords:
                        base_time_str = str(ds.coords['reftime'].values[0])
                        base_time = pd.to_datetime(base_time_str, format='%Y%m%d%H%M')
                        ts = base_time + pd.Timedelta(hours=int(t_values[ti]))
                    else:
                        continue
                    
                    data_2d = data_var.isel(time=ti).values
                    vals = data_2d[nc_lat_indices, nc_lon_indices]
                    
                    # 数据验证
                    if var_config['data_type'] == 'float':
                        msk = ~np.isnan(vals) & np.isfinite(vals)
                    else:
                        msk = (vals >= 0) & (vals < 32767)
                    
                    if ts not in all_time_data:
                        all_time_data[ts] = {}
                    
                    # 存储有效数据
                    valid_indices = np.where(msk)[0]
                    for i in valid_indices:
                        cell_id = valid_cells[i]
                        val = vals[i]
                        
                        if var_config['data_type'] == 'float':
                            converted_val = float(val) * var_config['scale_factor']
                        else:
                            converted_val = int(val) * var_config['scale_factor']
                        
                        # 对于PRE数据，只存储降水量大于0的记录
                        if data_type == 'PRE' and converted_val <= 0:
                            continue
                        
                        if cell_id not in all_time_data[ts]:
                            all_time_data[ts][cell_id] = {}
                        
                        all_time_data[ts][cell_id][data_type] = converted_val
                
                ds.close()
                
            except Exception as e:
                logger.error(f"处理文件 {nc_file} 时出错: {e}")
                continue
    
    return all_time_data



async def process_time_data_async(db_manager: AsyncDatabaseManager, all_time_data: Dict):
    """
    异步处理时间数据：并发执行数据库upsert操作
    """
    logger.info(f"开始异步处理 {len(all_time_data)} 个时间步的数据")

    # 创建信号量限制并发数
    semaphore = asyncio.Semaphore(MAX_CONCURRENT_DB_OPERATIONS)

    async def process_single_timestamp(ts, time_data):
        async with semaphore:
            try:
                if time_data:
                    upserted_count = await db_manager.upsert_weather_data(ts, time_data)
                    logger.info(f"时间步 {ts}: upsert {upserted_count} 个格网")
                    return ts, True
                else:
                    logger.info(f"时间步 {ts}: 无新数据")
                    return ts, False
            except Exception as e:
                logger.error(f"处理时间步 {ts} 时出错: {e}")
                return ts, False

    # 并发处理所有时间步
    tasks = [process_single_timestamp(ts, time_data)
             for ts, time_data in all_time_data.items()]

    results = await asyncio.gather(*tasks, return_exceptions=True)

    # 统计结果
    successful_timestamps = []
    for result in results:
        if isinstance(result, tuple) and result[1]:
            successful_timestamps.append(result[0])

    logger.info(f"异步数据处理完成，成功: {len(successful_timestamps)}/{len(all_time_data)}")
    return successful_timestamps

async def call_stored_procedures_async(db_manager: AsyncDatabaseManager, processed_timestamps: List):
    """
    异步调用存储过程：并发执行polygon和line存储过程
    """
    if not processed_timestamps:
        logger.info("无处理时间戳，跳过存储过程")
        return

    logger.info(f"开始异步调用存储过程，处理 {len(processed_timestamps)} 个时间步")

    # 首先检查哪些时间步有PRE数据
    valid_timestamps = []
    async with db_manager.pool.acquire() as conn:
        for ts in processed_timestamps:
            try:
                result = await conn.fetchval("""
                    SELECT COUNT(*) FROM weather_cell_1h
                    WHERE pre_time = $1 AND pre IS NOT NULL AND pre > 0
                """, ts)

                if result > 0:
                    valid_timestamps.append(ts)
                else:
                    logger.info(f"时间步 {ts}: 无降水数据，跳过存储过程")
            except Exception as e:
                logger.error(f"检查时间步 {ts} 数据失败: {e}")
                continue

    if not valid_timestamps:
        logger.info("无有效降水数据，跳过存储过程")
        return

    # 创建任务列表：每个时间步 × 2种类型(line/polygon)
    semaphore = asyncio.Semaphore(MAX_CONCURRENT_PROCEDURES)

    async def execute_procedure_task(ts, procedure_type):
        async with semaphore:
            try:
                success = await db_manager.call_stored_procedure(procedure_type, ts)
                if success:
                    logger.info(f"✓ 完成存储过程 {procedure_type} for {ts}")
                else:
                    logger.warning(f"✗ 存储过程失败 {procedure_type} for {ts}，不再重试")
                return success
            except Exception as e:
                logger.error(f"✗ 存储过程异常 {procedure_type} for {ts}: {e}，不再重试")
                return False

    # 创建所有任务
    tasks = []
    for ts in valid_timestamps:
        tasks.append(execute_procedure_task(ts, "polygon"))
        tasks.append(execute_procedure_task(ts, "line"))

    logger.info(f"并发执行 {len(tasks)} 个存储过程任务...")

    # 并发执行所有存储过程，失败后不重试
    try:
        results = await asyncio.gather(*tasks, return_exceptions=True)

        # 统计结果
        success_count = sum(1 for r in results if r is True)
        failed_count = len(tasks) - success_count

        logger.info(f"存储过程调用完成，成功: {success_count}/{len(tasks)}")
        if failed_count > 0:
            logger.warning(f"有 {failed_count} 个存储过程失败，已跳过不再重试")

    except Exception as e:
        logger.error(f"存储过程批量执行异常: {e}，停止所有存储过程调用")

async def call_summary_procedures_async(db_manager: AsyncDatabaseManager):
    """
    异步调用汇总存储过程：并发执行24h、48h、72h汇总
    """
    logger.info("开始异步调用汇总存储过程...")

    # 获取时间范围
    async with db_manager.pool.acquire() as conn:
        result = await conn.fetchrow("""
            SELECT MIN(pre_time), MAX(pre_time)
            FROM weather_cell_1h
            WHERE pre IS NOT NULL AND pre > 0
        """)

        if not result or not result[0]:
            logger.info("未找到PRE数据，跳过汇总存储过程")
            return

        min_time, max_time = result
        logger.info(f"PRE数据时间范围: {min_time} 到 {max_time}")

    # 计算时间范围
    from datetime import datetime, timedelta

    # 第一组：基于当前时间的24小时、48小时、72小时（先执行）
    now = datetime.now().replace(minute=0, second=0, microsecond=0)
    current_time_ranges = [
        (now, now + timedelta(hours=23), "当前24小时"),
        (now, now + timedelta(hours=47), "当前48小时"),
        (now, now + timedelta(hours=71), "当前72小时")
    ]

    # 第二组：获取当前时间的下一个整点作为起始时间
    start_hour = now.hour + 1

    # 如果超过23点，则从第二天0点开始
    if start_hour >= 24:
        base_start = datetime(now.year, now.month, now.day, 0, 0, 0) + timedelta(days=1)
    else:
        base_start = datetime(now.year, now.month, now.day, start_hour, 0, 0)

    # 计算各个时间范围的结束时间（24小时是到第二天同一时间的前一小时）
    end_24h = base_start + timedelta(hours=23)
    end_48h = base_start + timedelta(hours=47)
    end_72h = base_start + timedelta(hours=71)

    next_hour_time_ranges = [
        (base_start, end_24h, "下一整点24小时"),
        (base_start, end_48h, "下一整点48小时"),
        (base_start, end_72h, "下一整点72小时")
    ]

    # 合并时间范围，当前时间的先执行
    time_ranges = current_time_ranges + next_hour_time_ranges

    # 创建并发任务
    semaphore = asyncio.Semaphore(MAX_CONCURRENT_PROCEDURES)

    async def execute_summary_task(start_time, end_time, description, task_type):
        async with semaphore:
            try:
                logger.info(f"开始处理{description}{task_type}汇总: {start_time} 到 {end_time}")

                # 设置10分钟超时，避免无限等待
                async with asyncio.timeout(600):
                    async with db_manager.pool.acquire() as conn:
                        if task_type == "line":
                            await conn.execute("""
                                CALL sp_precip_line($1::timestamp, $2::timestamp, $3::text, $4::text[], $5::text, $6::text, $7::text)
                            """, start_time, end_time, 'get_rainfall_type_hourly', ['pre'], 'weather_cell_1h',
                                 'pre', 'forecast_precipitation_summary_line')

                        elif task_type == "polygon":
                            await conn.execute("""
                                CALL sp_precip_polygon($1::timestamp, $2::timestamp, $3::text, $4::text[], $5::text, $6::text, $7::text, $8::text)
                            """, start_time, end_time, 'get_rainfall_type_hourly', ['pre'], 'weather_cell_1h',
                                 'pre', 'forecast_precipitation_summary_polygon', 'forecast_precipitation_summary_relation')

                logger.info(f"✓ 完成 {description}{task_type}汇总")
                return True

            except asyncio.TimeoutError:
                logger.error(f"{description}{task_type}汇总存储过程超时: 超过10分钟，放弃执行")
                return False
            except Exception as e:
                logger.error(f"{description}{task_type}汇总存储过程失败: {e}，不再重试")
                return False

    # 创建12个并行任务：6个时间段 × 2种类型
    tasks = []
    for start_time, end_time, description in time_ranges:
        tasks.append(execute_summary_task(start_time, end_time, description, "line"))
        tasks.append(execute_summary_task(start_time, end_time, description, "polygon"))

    logger.info(f"并发执行汇总存储过程，{len(tasks)} 个任务（包含当前时间和下一整点两组时间范围）...")

    # 并发执行所有汇总任务，失败后不重试
    try:
        results = await asyncio.gather(*tasks, return_exceptions=True)

        # 统计结果
        success_count = sum(1 for r in results if r is True)
        failed_count = len(tasks) - success_count

        logger.info(f"汇总存储过程完成: {success_count}/{len(tasks)} 个任务成功")
        if failed_count > 0:
            logger.warning(f"有 {failed_count} 个汇总存储过程失败，已跳过不再重试")

    except Exception as e:
        logger.error(f"汇总存储过程批量执行异常: {e}，停止所有汇总存储过程调用")

async def hybrid_worker_async(nc_files_dict: dict, t_index_slice, valid_cells):
    """
    混合架构的异步工作函数：
    1. 使用进程池处理CPU密集型的NetCDF解析
    2. 使用协程处理I/O密集型的数据库操作
    """
    # 初始化数据库管理器
    db_manager = AsyncDatabaseManager(PG_URL)
    await db_manager.initialize()

    try:
        logger.info(f"开始混合处理 time index {t_index_slice.start}-{t_index_slice.stop-1}")

        # 1. 使用进程池处理CPU密集型任务
        with ProcessPoolExecutor(max_workers=1) as executor:
            loop = asyncio.get_event_loop()
            all_time_data = await loop.run_in_executor(
                executor,
                process_nc_files_cpu_intensive,
                nc_files_dict, t_index_slice, valid_cells
            )

        # 2. 使用协程处理I/O密集型任务
        if all_time_data:
            processed_timestamps = await process_time_data_async(db_manager, all_time_data)

            # 3. 清理空记录
            await db_manager.cleanup_null_records()

            # 4. 异步调用存储过程
            await call_stored_procedures_async(db_manager, processed_timestamps)

        logger.info(f"完成混合处理 time index {t_index_slice.start}-{t_index_slice.stop-1}")

    finally:
        await db_manager.close()

def find_nc_files_in_directories(data_type: str) -> list:
    """根据数据类型查找对应目录下的NC文件"""
    from config import SCRIPT_DIR
    type_dir_map = {
        'PRE': str(SCRIPT_DIR / 'data/precipitation_1h/nc_files'),
        'WEATHER': str(SCRIPT_DIR / 'data/weather_1h/nc_files'),
        'VIS': str(SCRIPT_DIR / 'data/visibility_1h/nc_files'),
        'TEM': str(SCRIPT_DIR / 'data/temperature_1h/nc_files')
    }

    base_dir = type_dir_map.get(data_type)
    if not base_dir:
        logger.error(f"未知的数据类型: {data_type}")
        return []

    nc_files = []
    for root, _, files in os.walk(base_dir):
        for file in files:
            if file.endswith('.nc'):
                nc_files.append(os.path.join(root, file))

    logger.info(f"找到 {data_type} 类型的NC文件 {len(nc_files)} 个")
    return sorted(nc_files)



async def process_weather_data_hybrid(data_types=None, nc_files_dict=None):
    """
    混合架构的1小时天气数据处理（供调度器调用）

    Args:
        data_types: 要处理的数据类型列表，如 ['PRE', 'TEM']，None表示处理所有类型
        nc_files_dict: 已下载的NC文件字典，格式为 {data_type: [file_paths]}
    """
    logger.info("=== 混合架构1小时天气数据处理系统启动 ===")

    if data_types:
        logger.info(f"指定处理数据类型: {data_types}")
    else:
        logger.info("处理所有数据类型")

    try:
        # 1. 获取NC文件路径
        if nc_files_dict:
            # 使用调度器提供的已下载文件
            logger.info(f"使用调度器提供的NC文件: {nc_files_dict}")
            downloaded_files_info = {}
            used_data_types = list(nc_files_dict.keys())
        else:
            # 自动获取NC文件路径（可能包含下载）
            nc_files_dict, downloaded_files_info, used_data_types = get_nc_files_path(data_types)

        if not nc_files_dict:
            logger.error("未找到任何NC文件，程序退出")
            return

        logger.info(f"将处理以下数据类型: {list(nc_files_dict.keys())}")
        for data_type, files in nc_files_dict.items():
            download_info = downloaded_files_info.get(data_type, {})
            source = "下载" if download_info.get('downloaded', False) else "本地现有"
            logger.info(f"  {data_type}: {len(files)} 个文件 (来源: {source})")

        # 2. 预读LUT cell_id
        logger.info("预读 LUT cell_id …")
        engine = create_engine(PG_URL)
        with engine.begin() as conn:
            valid_cells = pd.read_sql("""
                SELECT DISTINCT cell_id
                FROM weather_cell_route_lut
            """, conn)['cell_id'].values
        valid_cells.sort()
        logger.info(f"有效格网 {len(valid_cells):,}")

        # 3. 确定时间步数量
        first_file = None
        for files in nc_files_dict.values():
            if files:
                first_file = files[0]
                break

        if not first_file:
            logger.error("未找到参考NC文件")
            return

        with xr.open_dataset(first_file, engine="netcdf4") as ds0:
            nt = ds0.sizes['time']
        logger.info(f"NetCDF 共 {nt} 个时间步")

        # 4. 创建时间切片
        CHUNK_SIZE = PROCESSING_CONFIG["chunk_size"]
        slices = []
        for start in range(0, nt, CHUNK_SIZE):
            stop = min(start + CHUNK_SIZE, nt)
            slices.append(slice(start, stop))

        # 5. 使用混合架构处理：每个切片使用一个协程
        logger.info(f"使用混合架构处理 {len(slices)} 个时间切片...")

        # 创建协程任务
        tasks = []
        for slice_obj in slices:
            task = hybrid_worker_async(nc_files_dict, slice_obj, valid_cells)
            tasks.append(task)

        # 并发执行所有任务（限制并发数避免资源耗尽）
        semaphore = asyncio.Semaphore(3)  # 最多3个并发切片处理

        async def limited_task(task):
            async with semaphore:
                await task

        limited_tasks = [limited_task(task) for task in tasks]
        await asyncio.gather(*limited_tasks)

        logger.info("✓ 全部切片处理完成")

        # 6. 如果处理的是PRE数据，调用汇总存储过程
        if data_types and 'PRE' in data_types:
            logger.info("开始调用PRE数据汇总存储过程...")
            db_manager = AsyncDatabaseManager(PG_URL)
            await db_manager.initialize()
            try:
                await call_summary_procedures_async(db_manager)
                logger.info("✓ PRE数据汇总存储过程完成")
            finally:
                await db_manager.close()

        # 7. 处理完成后备份文件（如果是下载的文件且启用备份）
        if (WEATHER_DOWNLOAD_CONFIG.get("enable_backup", True) and
            WEATHER_DOWNLOAD_AVAILABLE):
            logger.info("开始备份处理完的文件...")

            for data_type, download_info in downloaded_files_info.items():
                if download_info.get('downloaded', False):
                    files_to_backup = download_info.get('files', [])
                    download_type = download_info.get('download_type')

                    backup_success_count = 0
                    for file_path in files_to_backup:
                        try:
                            backup_success = process_and_backup_weather_file(
                                file_path, True, download_type
                            )
                            if backup_success:
                                backup_success_count += 1
                            else:
                                logger.warning(f"备份文件失败: {file_path}")
                        except Exception as e:
                            logger.error(f"备份文件 {file_path} 时发生错误: {e}")

                    logger.info(f"数据类型 {data_type}: 成功备份 {backup_success_count}/{len(files_to_backup)} 个文件")
                else:
                    logger.info(f"数据类型 {data_type}: 文件不是下载的，跳过备份")
        else:
            logger.info("跳过文件备份（未启用备份功能或下载模块不可用）")

        logger.info("=== 混合架构1小时天气数据处理完成 ===")

    except Exception as e:
        logger.error(f"处理过程中发生错误: {e}")
        raise


async def main():
    """主函数：混合架构的天气数据处理（命令行版本）"""
    # 解析命令行参数
    parser = argparse.ArgumentParser(description='1小时天气数据处理系统（异步版本）')
    parser.add_argument('data_types', nargs='*',
                       choices=list(WEATHER_1H_TYPES.keys()),
                       help='要处理的数据类型 (PRE, WEATHER, VIS, TEM)，不指定则处理所有类型')

    args = parser.parse_args()
    data_types = args.data_types if args.data_types else None

    # 调用处理函数
    await process_weather_data_hybrid(data_types)


if __name__ == '__main__':
    asyncio.run(main())
