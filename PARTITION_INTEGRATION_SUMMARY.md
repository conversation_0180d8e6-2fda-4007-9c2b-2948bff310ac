# 分区管理集成总结

## 概述

已将 pg_partman 分区管理配置集成到 `create_forecast_tables.py` 中，现在运行一个脚本即可完成：
1. 创建所有分区表
2. 配置自动分区管理
3. 设置维护任务

## 集成的功能

### 自动检测和配置
- **扩展检测**: 自动检测 `pg_partman` 和 `pg_cron` 扩展是否已安装
- **智能配置**: 根据扩展可用性自动配置相应功能
- **错误处理**: 即使分区管理配置失败，表创建仍会成功

### 分区策略配置
- **核心数据表** (11个): 每日分区，保留90天，预创建7天
- **预警表** (1个): 每月分区，保留3年，预创建4个月
- **自动维护**: 每天凌晨2点自动运行维护任务

## 使用方法

### 一键部署
```bash
# 确保扩展已安装
psql -d your_database -c "CREATE EXTENSION IF NOT EXISTS pg_partman;"
psql -d your_database -c "CREATE EXTENSION IF NOT EXISTS pg_cron;"

# 运行集成脚本
python src/create_forecast_tables.py
```

### 验证部署
```bash
# 使用集成测试脚本
python test_integrated_partition_setup.py
```

## 输出示例

运行 `create_forecast_tables.py` 时的输出：

```
开始创建降水预报相关数据表...
创建表: weather_cell_6m
✓ weather_cell_6m 表创建/检查完成
...
✓ 所有预报表创建完成

开始创建PostgreSQL函数...
✓ get_rainfall_type 函数创建完成
✓ get_rainfall_type_hourly 函数创建完成

开始创建存储过程...
✓ sp_precip_polygon 存储过程创建完成
✓ sp_precip_line 存储过程创建完成

✓ 所有函数和存储过程创建完成

开始配置分区管理...
✓ pg_partman 扩展已安装
配置核心数据表 (每日分区, 保留90天):
  ✓ public.weather_cell_6m
  ✓ public.weather_cell_1h
  ✓ public.forecast_precipitation_6min_line
  ...
配置预警表 (每月分区, 保留3年):
  ✓ public.weather_alarm
设置自动维护任务:
  ✓ 定时维护任务已创建 (ID: 1)
✓ 分区管理配置完成
```

## 错误处理

### 扩展未安装
如果 `pg_partman` 未安装：
```
⚠️ pg_partman 扩展未安装，跳过分区管理配置
   建议安装: CREATE EXTENSION pg_partman;
```

### pg_cron 未安装
如果 `pg_cron` 未安装：
```
⚠️ pg_cron 扩展未安装，请手动设置维护任务
   或安装: CREATE EXTENSION pg_cron;
```

### 配置失败
如果分区管理配置失败：
```
⚠️ 分区管理配置失败: [错误信息]
   表创建成功，但分区管理需要手动配置
```

## 手动配置选项

如果自动配置失败，仍可使用独立脚本：

### 使用 Python 脚本
```bash
python setup_partition_management.py
```

### 使用 SQL 脚本
```bash
psql -d your_database -f setup_partman.sql
```

## 验证工具

### 集成测试
```bash
python test_integrated_partition_setup.py
```
检查：
- 扩展状态
- 分区配置
- 维护任务
- 表结构
- 主键结构

### 独立验证
```bash
python verify_partman_setup.py
```

### 分区表结构验证
```bash
python test_partition_tables.py
```

## 配置详情

### 核心数据表配置
```sql
SELECT partman.create_parent(
    p_parent_table := 'public.weather_cell_6m',
    p_control := 'pre_time',
    p_type := 'native',
    p_interval := '1 day',
    p_premake := 7,
    p_retention := '90 days',
    p_retention_keep_table := false
);
```

### 预警表配置
```sql
SELECT partman.create_parent(
    p_parent_table := 'public.weather_alarm',
    p_control := 'publish_time',
    p_type := 'native',
    p_interval := '1 month',
    p_premake := 4,
    p_retention := '3 years',
    p_retention_keep_table := false
);
```

### 维护任务配置
```sql
SELECT cron.schedule(
    'partman-maintenance', 
    '0 2 * * *', 
    'CALL partman.run_maintenance_proc()'
);
```

## 优势

1. **简化部署**: 一个命令完成所有配置
2. **智能检测**: 自动适应环境
3. **错误隔离**: 分区配置失败不影响表创建
4. **完整验证**: 提供多种验证工具
5. **向后兼容**: 保持原有功能不变

## 注意事项

1. **扩展依赖**: 需要预先安装 `pg_partman` 扩展
2. **权限要求**: 需要创建扩展和定时任务的权限
3. **数据迁移**: 如有现有数据，需要先备份
4. **监控**: 部署后需要监控分区管理是否正常工作

## 故障排除

### 问题 1: 扩展权限不足
```sql
-- 解决方案：使用超级用户安装扩展
CREATE EXTENSION pg_partman;
CREATE EXTENSION pg_cron;
```

### 问题 2: 分区配置重复
如果表已配置分区管理，会显示 "可能已配置" 警告，这是正常的。

### 问题 3: 维护任务创建失败
检查 `pg_cron` 配置和权限：
```sql
-- 检查 pg_cron 配置
SHOW cron.database_name;
SELECT * FROM cron.job;
```

## 监控和维护

### 检查分区状态
```sql
SELECT * FROM partman.part_config;
```

### 检查维护任务
```sql
SELECT * FROM cron.job WHERE jobname = 'partman-maintenance';
```

### 手动运行维护
```sql
CALL partman.run_maintenance_proc();
```

## 总结

通过集成分区管理配置，大大简化了部署流程。现在只需要：
1. 安装必要的扩展
2. 运行 `create_forecast_tables.py`
3. 验证配置结果

这种集成方式既保持了灵活性，又提供了便利性，是生产环境部署的理想选择。
