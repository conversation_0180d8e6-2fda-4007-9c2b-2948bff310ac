#!/usr/bin/env python3
# coding: utf-8
"""
天气数据定时任务调度器快速停止脚本
用于WinSW服务的快速停止
"""

import sys
import signal
import psutil
import time
import logging
from pathlib import Path

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


def find_scheduler_processes():
    """查找调度器进程"""
    processes = []
    
    for proc in psutil.process_iter(['pid', 'name', 'cmdline']):
        try:
            cmdline = proc.info['cmdline']
            if cmdline and any('run_weather_scheduler.py' in arg for arg in cmdline):
                processes.append(proc)
        except (psutil.NoSuchProcess, psutil.AccessDenied):
            continue
    
    return processes


def kill_process_tree(pid):
    """杀死进程树"""
    try:
        parent = psutil.Process(pid)
        children = parent.children(recursive=True)
        
        # 先尝试优雅关闭
        logger.info(f"发送SIGTERM信号给进程 {pid}")
        parent.terminate()
        
        # 给子进程发送终止信号
        for child in children:
            try:
                child.terminate()
            except psutil.NoSuchProcess:
                pass
        
        # 等待进程结束
        gone, alive = psutil.wait_procs([parent] + children, timeout=3)
        
        # 强制杀死仍存活的进程
        for p in alive:
            try:
                logger.warning(f"强制杀死进程 {p.pid}")
                p.kill()
            except psutil.NoSuchProcess:
                pass
                
        logger.info(f"进程 {pid} 及其子进程已停止")
        return True
        
    except psutil.NoSuchProcess:
        logger.info(f"进程 {pid} 不存在")
        return True
    except Exception as e:
        logger.error(f"杀死进程 {pid} 失败: {e}")
        return False


def main():
    """主函数"""
    logger.info("开始快速停止天气调度器...")
    
    # 查找调度器进程
    processes = find_scheduler_processes()
    
    if not processes:
        logger.info("未找到运行中的调度器进程")
        return 0
    
    logger.info(f"找到 {len(processes)} 个调度器进程")
    
    # 停止所有进程
    success_count = 0
    for proc in processes:
        try:
            pid = proc.pid
            logger.info(f"停止进程 {pid}")
            
            if kill_process_tree(pid):
                success_count += 1
            else:
                logger.error(f"停止进程 {pid} 失败")
                
        except Exception as e:
            logger.error(f"处理进程时出错: {e}")
    
    if success_count == len(processes):
        logger.info("所有调度器进程已成功停止")
        return 0
    else:
        logger.error(f"部分进程停止失败 ({success_count}/{len(processes)})")
        return 1


if __name__ == "__main__":
    sys.exit(main())
