# 天气数据定时任务调度器使用指南

## 概述

天气数据定时任务调度器是一个基于uvicorn的定时器任务系统，用于自动化执行天气数据的下载和处理任务。系统支持：

- **5个定时任务**：1小时任务4个参数（PRE、TEM、WEATHER、VIS），6分钟任务1个
- **智能调度**：1h任务在整点+4分钟执行，6m任务在0,6,12...分钟+4分钟执行
- **数据时效性检查**：如果uptime不在5分钟内，每30秒重试一次
- **防重复执行**：相同任务不会重复执行，后续任务等待前面任务完成
- **超时控制**：6m任务10分钟超时，1h任务30分钟超时，自动杀死假死任务
- **统一资源管理**：连接池、线程池、进程池统一管理

## 快速开始

### 1. 安装依赖

```bash
# 安装项目依赖
uv sync
```

### 2. 启动调度器

```bash
# 启动调度器服务
uv run python run_weather_scheduler.py
```

调度器将在 `http://localhost:8000` 启动，提供Web API接口。

### 3. 监控任务状态

访问以下URL查看系统状态：

- **健康检查**: `http://localhost:8000/health`
- **调度器状态**: `http://localhost:8000/status`
- **所有任务**: `http://localhost:8000/tasks`
- **运行中任务**: `http://localhost:8000/tasks/running`
- **队列统计**: `http://localhost:8000/queues`
- **资源统计**: `http://localhost:8000/resources`

## 调度规则

### 1小时任务调度

- **执行时间**：每小时的第4分钟（如：01:04, 02:04, 03:04...）
- **数据类型**：PRE（降水）、TEM（温度）、WEATHER（天气现象）、VIS（能见度）
- **超时时间**：30分钟
- **执行顺序**：串行执行，一个完成后执行下一个

### 6分钟任务调度

- **执行时间**：每小时的4,10,16,22,28,34,40,46,52,58分钟
- **数据类型**：gz_mpfv3（6分钟降水数据）
- **超时时间**：10分钟
- **执行频率**：每6分钟一次

### API数据获取

1. **API调用**：调用天气数据API获取下载信息
2. **重试机制**：如果API调用失败，每30秒重试一次
3. **最大等待**：最多等待60分钟获取API数据
4. **下载执行**：获取到API数据后开始下载和处理

### 任务状态管理

1. **内存状态跟踪**：每个任务在内存中维护最后处理的uptime时间
2. **防重复处理**：相同uptime的数据不会重复处理
3. **首次运行检测**：内存为空时表示首次运行，直接执行
4. **状态持久化**：任务完成后更新内存中的uptime记录
5. **5个独立任务**：1h任务4个参数（PRE、TEM、WEATHER、VIS）+ 6m任务1个（gz_mpfv3）
6. **智能跳过**：已处理的uptime数据自动跳过，避免重复下载和处理

## API接口

### 基础接口

#### GET /
根路径，返回系统基本信息

#### GET /health
健康检查接口
```json
{
  "status": "healthy",
  "timestamp": "2025-07-13T12:00:00",
  "scheduler": {...}
}
```

#### GET /status
获取调度器完整状态
```json
{
  "scheduler_running": true,
  "next_hourly_schedule": "2025-07-13T13:04:00",
  "next_six_minute_schedule": "2025-07-13T12:10:00",
  "running_tasks": {...},
  "queue_stats": {...},
  "resource_stats": {...}
}
```

### 任务管理接口

#### GET /tasks
获取所有任务列表

#### GET /tasks/running
获取正在运行的任务

#### GET /tasks/{task_id}
获取指定任务的详细状态

#### POST /tasks/{task_id}/cancel
取消指定任务（仅对等待中的任务有效）

### 监控接口

#### GET /queues
获取任务队列统计信息

#### GET /resources
获取资源池使用统计

#### GET /data-freshness/{data_type}
检查指定数据类型的新鲜度

#### GET /schedule/next
获取下一次调度时间

### 任务状态管理接口

#### GET /task-states
获取所有任务状态
```json
{
  "task_states": {
    "1h_PRE": {
      "task_type": "1h",
      "data_type": "PRE",
      "last_uptime": "202507131304",
      "last_update": "2025-07-13T13:04:00",
      "total_executions": 5,
      "is_first_run": false
    }
  },
  "statistics": {
    "total_tasks": 5,
    "first_run_tasks": 0,
    "completed_tasks": 5,
    "total_executions": 25
  }
}
```

#### GET /task-states/{task_type}/{data_type}
获取指定任务状态

#### POST /task-states/reset
重置所有任务状态

#### POST /task-states/{task_type}/{data_type}/reset
重置指定任务状态

## 配置说明

### 统一配置文件：config.yml

系统使用统一的 `config.yml` 文件进行配置，包含所有模块的配置项：

```yaml
# 数据库配置
database:
  postgres:
    host: "***************"
    port: 5432
    database: "middle-data-dev"
    username: "root"
    password: "Ylzx@9008*12-3*"

# 天气数据下载配置
weather_download:
  api_base_url: "http://way.weatherdt.com/apimall/basic/ncfile.htm"
  api_key: "d23bf68181a1a038a0dfb0deaa04232f"
  enable_download: true
  download_timeout: 300
  max_retries: 3

# 定时任务调度器配置
scheduler:
  # API重试配置
  uptime_check:
    retry_interval_seconds: 30  # 重试间隔（秒）
    max_retry_duration_minutes: 60  # 最大重试时长（分钟）

  # 任务配置
  tasks:
    # 1小时任务配置
    hourly:
      data_types: ["PRE", "TEM", "WEATHER", "VIS"]
      timeout_minutes: 30
      schedule_pattern: "0 4 * * *"  # 每小时的4分钟执行

    # 6分钟任务配置
    six_minute:
      data_type: "gz_mpfv3"
      timeout_minutes: 10
      schedule_pattern: "4,10,16,22,28,34,40,46,52,58 * * * *"  # 每6分钟+4分钟执行

  # 资源池配置
  resources:
    database:
      max_connections: 20
      min_connections: 5

    thread_pool:
      max_workers: 32

    process_pool:
      max_workers: null  # 自动检测CPU核心数

  # 监控配置
  monitoring:
    log_level: "INFO"
    log_file: "weather_scheduler.log"
    health_check_interval: 30  # 秒

  # Web服务配置
  web:
    host: "0.0.0.0"
    port: 8000
    reload: false
```

## 系统架构

### 核心组件

1. **ResourceManager**: 统一资源池管理
   - 数据库连接池
   - 线程池
   - 进程池

2. **TaskManager**: 任务队列和执行管理
   - 防重复执行
   - 超时控制
   - 状态跟踪

3. **UptimeChecker**: 数据时效性检查
   - API调用
   - 时间解析
   - 重试逻辑

4. **WeatherTaskScheduler**: 定时任务调度
   - 时间计算
   - 任务提交
   - 调度管理

5. **FastAPI应用**: Web接口和监控
   - REST API
   - 状态监控
   - 健康检查

6. **TaskStateManager**: 任务状态管理
   - 内存状态跟踪
   - uptime记录管理
   - 防重复处理
   - 执行统计

### 数据流程

1. **调度触发** → 2. **时效性检查** → 3. **状态检查** → 4. **数据下载** → 5. **数据处理** → 6. **状态更新**

### 任务状态流程

1. **检查首次运行** → 2. **比较uptime** → 3. **决定是否处理** → 4. **执行任务** → 5. **更新状态**

## 日志和监控

### 日志文件

- **主日志**: `weather_scheduler.log`
- **日志级别**: INFO
- **日志格式**: `%(asctime)s - %(name)s - %(levelname)s - %(message)s`

### 监控指标

- 任务执行状态
- 队列长度
- 资源池使用率
- 数据新鲜度
- 系统健康状态

## 故障排除

### 常见问题

1. **任务超时**
   - 检查网络连接
   - 查看数据源状态
   - 调整超时配置

2. **数据不新鲜**
   - 检查API响应
   - 验证时间格式
   - 调整重试参数

3. **资源不足**
   - 监控资源池状态
   - 调整池大小配置
   - 检查系统资源

### 调试命令

```bash
# 查看日志
tail -f weather_scheduler.log

# 检查系统状态
curl http://localhost:8000/health

# 查看运行任务
curl http://localhost:8000/tasks/running

# 检查资源使用
curl http://localhost:8000/resources
```

## 部署建议

### 生产环境

1. **系统要求**
   - Python 3.12+
   - 足够的内存和CPU资源
   - 稳定的网络连接

2. **配置优化**
   - 调整资源池大小
   - 设置合适的超时时间
   - 配置日志轮转

3. **监控告警**
   - 设置健康检查
   - 监控任务失败率
   - 配置资源告警

### 高可用部署

- 使用进程管理器（如systemd）
- 配置自动重启
- 设置负载均衡（如需要）
- 数据库连接池配置
