# PostgreSQL 正式环境配置建议
# 基于系统配置自动生成

# 连接配置
max_connections = 246
superuser_reserved_connections = 3

# 内存配置
shared_buffers = 7GB
effective_cache_size = 23GB
work_mem = 131MB
maintenance_work_mem = 1792MB

# 并发配置
max_worker_processes = 20
max_parallel_workers = 10
max_parallel_workers_per_gather = 5

# 超时配置
statement_timeout = 1800000  # 30分钟
lock_timeout = 300000        # 5分钟
idle_in_transaction_session_timeout = 600000  # 10分钟

# WAL配置
wal_buffers = 16MB
checkpoint_completion_target = 0.9
max_wal_size = 2GB
min_wal_size = 1GB

# 日志配置
log_min_duration_statement = 1000  # 记录超过1秒的查询
log_checkpoints = on
log_connections = on
log_disconnections = on
log_lock_waits = on