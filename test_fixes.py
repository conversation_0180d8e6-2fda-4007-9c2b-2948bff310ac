#!/usr/bin/env python3
# coding: utf-8
"""
测试修复效果的脚本
"""

import sys
from pathlib import Path

# 添加src目录到路径
src_path = Path(__file__).parent / "src"
sys.path.insert(0, str(src_path))

import logging

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


def test_uptime_format():
    """测试uptime格式修复"""
    logger.info("=== 测试uptime格式修复 ===")
    
    try:
        from scheduler import WeatherTaskScheduler
        
        scheduler = WeatherTaskScheduler()
        
        # 测试问题中的uptime格式
        test_cases = [
            "202025071308",  # 问题中的格式
            "202507131308",  # 正确格式
            "20250713130800",  # 带秒格式
            "2507131308",  # 短格式
            "invalid",  # 无效格式
        ]
        
        logger.info("测试uptime格式转换:")
        for uptime in test_cases:
            formatted = scheduler._format_uptime(uptime)
            status = "✅" if formatted else "❌"

            # 特别检查问题格式是否被正确修正
            if uptime == "202025071308" and formatted == "202507131308":
                status = "✅ (已修正)"
            elif uptime == "202025071308" and formatted == "202025071308":
                status = "⚠️ (未修正)"

            logger.info(f"  {status} {uptime} -> {formatted}")
        
        return True
        
    except Exception as e:
        logger.error(f"测试失败: {e}")
        return False


def test_download_progress():
    """测试下载进度计算"""
    logger.info("=== 测试下载进度计算 ===")
    
    try:
        # 模拟下载进度计算
        test_cases = [
            (100, 200, 50.0),  # 正常情况
            (200, 200, 100.0),  # 完成
            (250, 200, 100.0),  # 超过100%，应该限制为100%
            (0, 0, 0),  # 总大小为0
        ]
        
        logger.info("测试下载进度计算:")
        for downloaded, total, expected in test_cases:
            if total > 0:
                progress = min((downloaded / total) * 100, 100.0)
            else:
                progress = 0
            
            status = "✅" if abs(progress - expected) < 0.1 else "❌"
            logger.info(f"  {status} {downloaded}/{total} -> {progress:.1f}% (期望: {expected}%)")
        
        return True
        
    except Exception as e:
        logger.error(f"测试失败: {e}")
        return False


def test_task_state_parsing():
    """测试任务状态时间解析"""
    logger.info("=== 测试任务状态时间解析 ===")
    
    try:
        from task_state_manager import TaskState
        
        # 创建测试任务状态
        task_state = TaskState("test_task", "TEST")
        
        # 测试时间解析
        test_cases = [
            "202507131308",  # 正确格式
            "202025071308",  # 错误格式（应该失败）
            "20250713130800",  # 带秒格式（应该失败，因为长度不是12）
        ]
        
        logger.info("测试任务状态时间解析:")
        for uptime in test_cases:
            try:
                should_process = task_state.should_process(uptime)
                logger.info(f"  ✅ {uptime} -> should_process: {should_process}")
            except Exception as e:
                logger.info(f"  ❌ {uptime} -> 解析失败: {e}")
        
        return True
        
    except Exception as e:
        logger.error(f"测试失败: {e}")
        return False


def main():
    """主函数"""
    logger.info("开始测试修复效果...")
    
    tests = [
        ("uptime格式修复", test_uptime_format),
        ("下载进度计算", test_download_progress),
        ("任务状态时间解析", test_task_state_parsing),
    ]
    
    results = []
    for test_name, test_func in tests:
        logger.info(f"\n{'='*50}")
        logger.info(f"运行测试: {test_name}")
        logger.info(f"{'='*50}")
        
        try:
            result = test_func()
            results.append((test_name, result))
            status = "通过" if result else "失败"
            logger.info(f"测试 {test_name}: {status}")
        except Exception as e:
            logger.error(f"测试 {test_name} 异常: {e}")
            results.append((test_name, False))
    
    # 总结
    logger.info(f"\n{'='*50}")
    logger.info("测试总结:")
    logger.info(f"{'='*50}")
    
    passed = sum(1 for _, result in results if result)
    total = len(results)
    
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        logger.info(f"  {status} {test_name}")
    
    logger.info(f"\n总计: {passed}/{total} 个测试通过")
    
    if passed == total:
        logger.info("🎉 所有测试都通过了！")
        return True
    else:
        logger.warning(f"⚠️  有 {total - passed} 个测试失败")
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
