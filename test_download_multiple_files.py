#!/usr/bin/env python3
# coding: utf-8
"""
测试下载多个文件的功能
"""

import sys
import logging
from pathlib import Path

# 添加src目录到路径
src_path = Path(__file__).parent / "src"
sys.path.insert(0, str(src_path))

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

logger = logging.getLogger(__name__)

def test_extract_method():
    """测试解压方法是否正确返回文件列表"""
    try:
        from weather_download import WeatherDownloader
        
        # 创建下载器实例
        downloader = WeatherDownloader("gz_didiforecast1hPRE")
        
        # 检查方法签名
        import inspect
        sig = inspect.signature(downloader._extract_tar_gz)
        logger.info(f"_extract_tar_gz 方法签名: {sig}")
        
        # 检查返回类型注解
        return_annotation = sig.return_annotation
        logger.info(f"返回类型注解: {return_annotation}")
        
        # 检查download_from_api方法签名
        sig2 = inspect.signature(downloader.download_from_api)
        logger.info(f"download_from_api 方法签名: {sig2}")
        
        return_annotation2 = sig2.return_annotation
        logger.info(f"download_from_api 返回类型注解: {return_annotation2}")
        
        logger.info("✅ 方法签名检查通过")
        return True
        
    except Exception as e:
        logger.error(f"测试失败: {e}")
        return False

def test_convenience_functions():
    """测试便捷函数"""
    try:
        from weather_download import download_weather_data_with_api_result, download_weather_data_all
        
        # 检查函数签名
        import inspect
        
        sig1 = inspect.signature(download_weather_data_with_api_result)
        logger.info(f"download_weather_data_with_api_result 签名: {sig1}")
        
        sig2 = inspect.signature(download_weather_data_all)
        logger.info(f"download_weather_data_all 签名: {sig2}")
        
        logger.info("✅ 便捷函数签名检查通过")
        return True
        
    except Exception as e:
        logger.error(f"测试失败: {e}")
        return False

def main():
    """主测试函数"""
    logger.info("开始测试下载多个文件的功能...")
    
    success = True
    
    # 测试方法签名
    if not test_extract_method():
        success = False
    
    # 测试便捷函数
    if not test_convenience_functions():
        success = False
    
    if success:
        logger.info("✅ 所有测试通过！")
        logger.info("现在下载器会返回所有解压的NC文件，而不是只返回一个")
        logger.info("这样可以处理1-24、25-48、49-72小时的完整预报数据")
    else:
        logger.error("❌ 测试失败")
        return False
    
    return True

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
