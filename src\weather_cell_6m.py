#!/usr/bin/env python3
# coding: utf-8
"""
混合架构：多进程 + 协程   NetCDF → weather_cell_6m
- 使用多进程处理CPU密集型的NetCDF数据解析
- 使用协程处理I/O密集型的数据库操作和存储过程调用
- 减少内存占用，提高I/O并发性能
- 支持6分钟降水数据处理，包含降水量(rainfall)和相态(phase)
- 支持从接口下载天气数据，处理后自动备份
"""
import os, math, uuid, multiprocessing as mp, asyncio
import numpy as np, pandas as pd, xarray as xr
from sqlalchemy import create_engine
import asyncpg
import logging
from typing import Dict, List
from concurrent.futures import ProcessPoolExecutor
import argparse

# 导入统一配置
from config import (
    PG_URL, NC_PATH, VAR_NAME, PHASE_VAR,
    GRID_X_MIN, GRID_Y_MIN, GRID_X_MAX, GRID_Y_MAX, GRID_SIZE,
    STEP, LON_OFF, LAT_OFF, PROCESSING_CONFIG, WEATHER_DOWNLOAD_CONFIG
)

# 导入天气数据下载模块
try:
    from weather_download import download_and_get_latest_weather_data, process_and_backup_weather_file
    WEATHER_DOWNLOAD_AVAILABLE = True
except ImportError:
    WEATHER_DOWNLOAD_AVAILABLE = False

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# ─────────────── 全局配置 ───────────────
# 格网生成顺序：先Y后X，所以行数是Y方向，列数是X方向
NC_NROWS = int(round((GRID_Y_MAX-GRID_Y_MIN)/GRID_SIZE))   # 820 (Y方向)
NC_NCOLS = int(round((GRID_X_MAX-GRID_X_MIN)/GRID_SIZE))   # 875 (X方向)

PROCESSES = PROCESSING_CONFIG["max_processes"] or max(1, mp.cpu_count() - 1)  # 并行进程数拉满
CHUNK_SIZE = PROCESSING_CONFIG["chunk_size"]                                   # 一个进程一次处理多少 time index

# 协程配置 - 优化为高性能模式
MAX_CONCURRENT_DB_OPERATIONS = 20  # 增加并发数据库操作数
MAX_CONCURRENT_PROCEDURES = 10     # 提高存储过程并发数到10个

# ========== 标准格网编码/解码函数 ==========
# 0.01° 等纬经标准格网的 ID 方案

def encode_cell(lon: float, lat: float) -> int:
    """经纬 → cell_id（32bit 整数）"""
    ix = int(math.floor((lon + LON_OFF) / STEP))
    iy = int(math.floor((lat + LAT_OFF) / STEP))
    return (iy << 16) | ix          # 行主序打包

def decode_cell(cell_id: int) -> tuple[float, float]:
    """cell_id → 左下角 (lon_min, lat_min)"""
    ix = cell_id & 0xFFFF
    iy = cell_id >> 16
    lon_min = ix * STEP - LON_OFF
    lat_min = iy * STEP - LAT_OFF
    return lon_min, lat_min

def get_netcdf_indices_from_coords(lon: float, lat: float,
                                   nc_lon_min: float, nc_lat_min: float) -> tuple[int, int]:
    """根据经纬度坐标直接计算NetCDF索引"""
    lon_idx = int(round((lon - nc_lon_min) / STEP))
    lat_idx = int(round((lat - nc_lat_min) / STEP))
    return lat_idx, lon_idx

def get_nc_file_path(data_type: str = None):
    """
    获取要处理的NC文件路径
    如果启用下载功能，先尝试下载最新数据

    Args:
        data_type: 数据类型，如 'gz_mpfv3', 'gz_didiforecast1hTEM' 等

    Returns:
        tuple: (nc_file_path, is_downloaded_file, data_type)
    """
    # 使用传入的数据类型或配置的默认类型
    data_type = data_type or WEATHER_DOWNLOAD_CONFIG.get("default_data_type", "gz_mpfv3")

    # 检查是否启用下载功能
    if WEATHER_DOWNLOAD_CONFIG.get("enable_download", False) and WEATHER_DOWNLOAD_AVAILABLE:
        logger.info(f"启用下载功能，正在获取数据类型 [{data_type}] 的最新数据")
        try:
            latest_file = download_and_get_latest_weather_data(data_type)
            if latest_file and os.path.exists(latest_file):
                logger.info(f"成功获取最新文件: {latest_file}")
                return latest_file, True, data_type
            else:
                logger.warning("下载失败，使用配置的默认文件")
        except Exception as e:
            logger.error(f"下载过程中发生错误: {e}")
            logger.warning("下载失败，使用配置的默认文件")

    # 使用配置的默认文件
    if os.path.exists(NC_PATH):
        logger.info(f"使用配置的默认文件: {NC_PATH}")
        return NC_PATH, False, data_type
    else:
        logger.error(f"默认NC文件不存在: {NC_PATH}")
        raise FileNotFoundError(f"NC文件不存在: {NC_PATH}")

class AsyncDatabaseManager:
    """异步数据库管理器"""

    def __init__(self, pg_url: str, max_connections: int = 50):  # 增加连接池大小
        self.pg_url = pg_url
        self.max_connections = max_connections
        self.pool = None
    
    async def initialize(self):
        """初始化连接池"""
        # 解析PostgreSQL URL
        import urllib.parse as urlparse
        parsed = urlparse.urlparse(self.pg_url)

        # 解码密码中的特殊字符
        password = urlparse.unquote_plus(parsed.password) if parsed.password else None

        logger.info(f"连接数据库: {parsed.hostname}:{parsed.port or 5432}/{parsed.path[1:]} 用户: {parsed.username}")

        self.pool = await asyncpg.create_pool(
            host=parsed.hostname,
            port=parsed.port or 5432,
            user=parsed.username,
            password=password,
            database=parsed.path[1:],  # 去掉开头的 '/'
            min_size=5,
            max_size=self.max_connections,
            command_timeout=300
        )
        logger.info(f"数据库连接池初始化完成，最大连接数: {self.max_connections}")
    
    async def close(self):
        """关闭连接池"""
        if self.pool:
            await self.pool.close()
            logger.info("数据库连接池已关闭")
    
    async def upsert_weather_data(self, timestamp, time_data: Dict):
        """异步upsert天气数据"""
        async with self.pool.acquire() as conn:
            async with conn.transaction():
                # 先删除该时间步的所有现有记录
                deleted_count = await conn.execute(
                    "DELETE FROM weather_cell_6m WHERE pre_time = $1", timestamp
                )
                
                if not time_data:
                    logger.info(f"时间步 {timestamp}: 删除 {deleted_count} 行, 无新数据")
                    return 0
                
                # 创建临时表
                tmp_tbl = f"tmp_weather_6m_{uuid.uuid4().hex}"
                await conn.execute(f"""
                    CREATE TEMP TABLE {tmp_tbl} (
                        pre_time TIMESTAMP NOT NULL,
                        cell_id INTEGER NOT NULL,
                        rainfall NUMERIC(8,3) NOT NULL,
                        phase INTEGER NOT NULL
                    ) ON COMMIT DROP;
                """)
                
                # 准备数据
                data_rows = []
                for cell_id, weather_data in time_data.items():
                    rainfall_val = weather_data.get('rainfall')
                    phase_val = weather_data.get('phase')
                    
                    if rainfall_val is not None and phase_val is not None:
                        data_rows.append((
                            timestamp, cell_id,
                            float(rainfall_val),
                            int(phase_val)
                        ))
                
                if data_rows:
                    # 批量插入临时表
                    await conn.copy_records_to_table(
                        tmp_tbl, 
                        records=data_rows,
                        columns=['pre_time', 'cell_id', 'rainfall', 'phase']
                    )
                    
                    # 从临时表插入到正式表
                    await conn.execute(f"""
                        INSERT INTO weather_cell_6m (pre_time, cell_id, rainfall, phase)
                        SELECT pre_time, cell_id, rainfall, phase
                        FROM {tmp_tbl}
                    """)
                    
                    logger.info(f"时间步 {timestamp}: 删除 {deleted_count} 行, 插入 {len(data_rows)} 行")
                    return len(data_rows)
                else:
                    logger.info(f"时间步 {timestamp}: 删除 {deleted_count} 行, 无有效数据插入")
                    return 0
    
    async def call_stored_procedure(self, procedure_type: str, timestamp):
        """异步调用存储过程，失败后不重试"""
        try:
            # 设置10分钟超时，避免无限等待
            async with asyncio.timeout(600):  # 10分钟超时
                async with self.pool.acquire() as conn:
                    if procedure_type == "polygon":
                        await conn.execute("""
                            CALL sp_precip_polygon($1::timestamp, $2::timestamp, $3::text, $4::text[], $5::text, $6::text, $7::text, $8::text)
                        """, timestamp, None, 'get_rainfall_type', ['rainfall', 'phase'], 'weather_cell_6m',
                             'rainfall', 'forecast_precipitation_6min_polygon', 'forecast_precipitation_6min_relation')

                    elif procedure_type == "line":
                        await conn.execute("""
                            CALL sp_precip_line($1::timestamp, $2::timestamp, $3::text, $4::text[], $5::text, $6::text, $7::text)
                        """, timestamp, None, 'get_rainfall_type', ['rainfall', 'phase'], 'weather_cell_6m',
                             'rainfall', 'forecast_precipitation_6min_line')

                    return True
        except asyncio.TimeoutError:
            logger.error(f"存储过程调用超时 {procedure_type} {timestamp}: 超过10分钟，放弃执行")
            return False
        except Exception as e:
            logger.error(f"存储过程调用失败 {procedure_type} {timestamp}: {e}")
            return False

def process_nc_file_cpu_intensive(nc_file_path: str, t_index_slice, valid_cells) -> Dict:
    """
    CPU密集型任务：在独立进程中处理NetCDF文件
    返回处理后的数据，不直接操作数据库
    """
    all_time_data = {}
    
    try:
        logger.info(f"PID {os.getpid()} 开始处理6分钟降水数据")
        
        ds = xr.open_dataset(nc_file_path, engine="netcdf4")
        
        if VAR_NAME not in ds.data_vars or PHASE_VAR not in ds.data_vars:
            logger.error(f"NetCDF文件中缺少必要变量: {VAR_NAME} 或 {PHASE_VAR}")
            return all_time_data
        
        pre = ds[VAR_NAME]
        phase = ds[PHASE_VAR]
        t_pre = ds['time'].values
        t_phase = ds['timePhase'].values if 'timePhase' in phase.dims else t_pre
        ratio = len(t_pre)//len(t_phase) if len(t_phase) else 1
        
        # 获取NetCDF坐标范围
        lon_vals = ds.coords['lon'].values
        lat_vals = ds.coords['lat'].values
        nc_lon_min, nc_lat_min = lon_vals[0], lat_vals[0]
        
        # 预计算NetCDF索引
        nc_lat_indices = []
        nc_lon_indices = []
        for cell_id in valid_cells:
            lon_min, lat_min = decode_cell(cell_id)
            lat_idx, lon_idx = get_netcdf_indices_from_coords(
                lon_min, lat_min, nc_lon_min, nc_lat_min)
            nc_lat_indices.append(lat_idx)
            nc_lon_indices.append(lon_idx)
        
        nc_lat_indices = np.array(nc_lat_indices)
        nc_lon_indices = np.array(nc_lon_indices)
        
        # 处理时间切片
        for ti in range(min(t_index_slice.start, len(t_pre)),
                       min(t_index_slice.stop, len(t_pre))):
            
            ts = pd.to_datetime(str(t_pre[ti]))
            rain_2d = pre.isel(time=ti).values
            ph_idx = ti//ratio
            phase_2d = phase.isel(timePhase=ph_idx).values
            
            # 使用预计算的NetCDF索引获取数据
            vals = rain_2d[nc_lat_indices, nc_lon_indices]
            phs = phase_2d[nc_lat_indices, nc_lon_indices]
            msk = (~np.isnan(vals)) & (vals > 0)
            
            if msk.any():
                if ts not in all_time_data:
                    all_time_data[ts] = {}
                
                # 存储有效数据
                valid_indices = np.where(msk)[0]
                for i in valid_indices:
                    cell_id = valid_cells[i]
                    rainfall_val = float(vals[i])
                    phase_val = int(phs[i])

                    # 只存储降水量大于0的记录
                    if rainfall_val > 0:
                        all_time_data[ts][cell_id] = {
                            'rainfall': round(rainfall_val, 3),  # 保留3位小数
                            'phase': phase_val
                        }
        
        ds.close()
        
    except Exception as e:
        logger.error(f"处理文件 {nc_file_path} 时出错: {e}")
    
    return all_time_data

async def process_time_data_async(db_manager: AsyncDatabaseManager, all_time_data: Dict):
    """
    异步处理时间数据：并发执行数据库upsert操作
    """
    logger.info(f"开始异步处理 {len(all_time_data)} 个时间步的数据")

    # 创建信号量限制并发数
    semaphore = asyncio.Semaphore(MAX_CONCURRENT_DB_OPERATIONS)

    async def process_single_timestamp(ts, time_data):
        async with semaphore:
            try:
                if time_data:
                    upserted_count = await db_manager.upsert_weather_data(ts, time_data)
                    logger.info(f"时间步 {ts}: upsert {upserted_count} 个格网")
                    return ts, True
                else:
                    logger.info(f"时间步 {ts}: 无新数据")
                    return ts, False
            except Exception as e:
                logger.error(f"处理时间步 {ts} 时出错: {e}")
                return ts, False

    # 并发处理所有时间步
    tasks = [process_single_timestamp(ts, time_data)
             for ts, time_data in all_time_data.items()]

    results = await asyncio.gather(*tasks, return_exceptions=True)

    # 统计结果
    successful_timestamps = []
    for result in results:
        if isinstance(result, tuple) and result[1]:
            successful_timestamps.append(result[0])

    logger.info(f"异步数据处理完成，成功: {len(successful_timestamps)}/{len(all_time_data)}")
    return successful_timestamps

async def call_stored_procedures_async(db_manager: AsyncDatabaseManager, processed_timestamps: List):
    """
    异步调用存储过程：并发执行polygon和line存储过程
    """
    if not processed_timestamps:
        logger.info("无处理时间戳，跳过存储过程")
        return

    logger.info(f"开始异步调用存储过程，处理 {len(processed_timestamps)} 个时间步")

    # 首先检查哪些时间步有降水数据
    valid_timestamps = []
    async with db_manager.pool.acquire() as conn:
        for ts in processed_timestamps:
            try:
                result = await conn.fetchval("""
                    SELECT COUNT(*) FROM weather_cell_6m
                    WHERE pre_time = $1 AND rainfall > 0
                """, ts)

                if result > 0:
                    valid_timestamps.append(ts)
                else:
                    logger.info(f"时间步 {ts}: 无降水数据，跳过存储过程")
            except Exception as e:
                logger.error(f"检查时间步 {ts} 数据失败: {e}")
                continue

    if not valid_timestamps:
        logger.info("无有效降水数据，跳过存储过程")
        return

    # 创建任务列表：每个时间步 × 2种类型(line/polygon)
    semaphore = asyncio.Semaphore(MAX_CONCURRENT_PROCEDURES)

    async def execute_procedure_task(ts, procedure_type):
        async with semaphore:
            try:
                success = await db_manager.call_stored_procedure(procedure_type, ts)
                if success:
                    logger.info(f"✓ 完成存储过程 {procedure_type} for {ts}")
                else:
                    logger.warning(f"✗ 存储过程失败 {procedure_type} for {ts}，不再重试")
                return success
            except Exception as e:
                logger.error(f"✗ 存储过程异常 {procedure_type} for {ts}: {e}，不再重试")
                return False

    # 创建所有任务
    tasks = []
    for ts in valid_timestamps:
        tasks.append(execute_procedure_task(ts, "polygon"))
        tasks.append(execute_procedure_task(ts, "line"))

    logger.info(f"并发执行 {len(tasks)} 个存储过程任务...")

    # 并发执行所有存储过程，失败后不重试
    try:
        results = await asyncio.gather(*tasks, return_exceptions=True)

        # 统计结果
        success_count = sum(1 for r in results if r is True)
        failed_count = len(tasks) - success_count

        logger.info(f"存储过程调用完成，成功: {success_count}/{len(tasks)}")
        if failed_count > 0:
            logger.warning(f"有 {failed_count} 个存储过程失败，已跳过不再重试")

    except Exception as e:
        logger.error(f"存储过程批量执行异常: {e}，停止所有存储过程调用")

async def call_summary_procedures_async(db_manager: AsyncDatabaseManager):
    """
    异步调用汇总存储过程

    注意：6分钟任务暂时没有必须执行的汇总存储过程，此函数为空实现
    """
    logger.info("6分钟任务暂时没有必须执行的汇总存储过程")
    pass

async def hybrid_worker_async(nc_file_path: str, t_index_slice, valid_cells):
    """
    混合架构的异步工作函数：
    1. 使用进程池处理CPU密集型的NetCDF解析
    2. 使用协程处理I/O密集型的数据库操作
    """
    # 初始化数据库管理器
    db_manager = AsyncDatabaseManager(PG_URL)
    await db_manager.initialize()

    try:
        logger.info(f"开始混合处理 time index {t_index_slice.start}-{t_index_slice.stop-1}")

        # 1. 使用进程池处理CPU密集型任务
        with ProcessPoolExecutor(max_workers=1) as executor:
            loop = asyncio.get_event_loop()
            all_time_data = await loop.run_in_executor(
                executor,
                process_nc_file_cpu_intensive,
                nc_file_path, t_index_slice, valid_cells
            )

        # 2. 使用协程处理I/O密集型任务
        if all_time_data:
            processed_timestamps = await process_time_data_async(db_manager, all_time_data)

            # 3. 异步调用存储过程
            await call_stored_procedures_async(db_manager, processed_timestamps)

        logger.info(f"完成混合处理 time index {t_index_slice.start}-{t_index_slice.stop-1}")

    finally:
        await db_manager.close()

async def process_weather_data_hybrid(data_type=None, nc_file_path=None):
    """
    混合架构的6分钟天气数据处理（供调度器调用）

    Args:
        data_type: 数据类型，如 gz_mpfv3（默认）
        nc_file_path: 已下载的NC文件路径（如果提供，则跳过下载）
    """
    logger.info("=== 混合架构6分钟天气数据处理系统启动 ===")

    if data_type:
        logger.info(f"指定数据类型: {data_type}")
    else:
        logger.info("使用默认数据类型")

    try:
        # 1. 获取NC文件路径
        if nc_file_path:
            # 使用调度器提供的已下载文件
            logger.info(f"使用调度器提供的NC文件: {nc_file_path}")
            is_downloaded_file = True
            used_data_type = data_type or "gz_mpfv3"
        else:
            # 自动获取NC文件路径（可能包含下载）
            nc_file_path, is_downloaded_file, used_data_type = get_nc_file_path(data_type)
        logger.info(f"使用NC文件: {nc_file_path}")
        logger.info(f"数据类型: {used_data_type}")
        logger.info(f"文件来源: {'下载' if is_downloaded_file else '本地现有'}")

        # 2. 预读LUT cell_id
        logger.info("预读 LUT cell_id …")
        engine = create_engine(PG_URL)
        with engine.begin() as conn:
            valid_cells = pd.read_sql("""
                SELECT DISTINCT cell_id
                FROM weather_cell_route_lut
            """, conn)['cell_id'].values
        valid_cells.sort()
        logger.info(f"有效格网 {len(valid_cells):,}")

        # 3. 确定时间步数量
        with xr.open_dataset(nc_file_path, engine="netcdf4") as ds0:
            nt = ds0.sizes['time']
        logger.info(f"NetCDF 共 {nt} 个时间步")

        # 4. 创建时间切片
        CHUNK_SIZE = PROCESSING_CONFIG["chunk_size"]
        slices = []
        for start in range(0, nt, CHUNK_SIZE):
            stop = min(start + CHUNK_SIZE, nt)
            slices.append(slice(start, stop))

        # 5. 使用混合架构处理：每个切片使用一个协程
        logger.info(f"使用混合架构处理 {len(slices)} 个时间切片...")

        # 创建协程任务
        tasks = []
        for slice_obj in slices:
            task = hybrid_worker_async(nc_file_path, slice_obj, valid_cells)
            tasks.append(task)

        # 并发执行所有任务（限制并发数避免资源耗尽）
        semaphore = asyncio.Semaphore(3)  # 最多3个并发切片处理

        async def limited_task(task):
            async with semaphore:
                await task

        limited_tasks = [limited_task(task) for task in tasks]
        await asyncio.gather(*limited_tasks)

        logger.info("✓ 全部切片处理完成")

        # 6. 处理完成后备份文件（如果是下载的文件且启用备份）
        if (is_downloaded_file and
            WEATHER_DOWNLOAD_CONFIG.get("enable_backup", True) and
            WEATHER_DOWNLOAD_AVAILABLE):
            logger.info("开始备份处理完的文件...")
            try:
                backup_success = process_and_backup_weather_file(nc_file_path, is_downloaded_file, used_data_type)
                if backup_success:
                    logger.info("文件备份成功")
                else:
                    logger.warning("文件备份失败")
            except Exception as e:
                logger.error(f"备份过程中发生错误: {e}")
        else:
            logger.info("跳过文件备份（文件不是下载的或未启用备份功能）")

        logger.info("=== 混合架构6分钟天气数据处理完成 ===")

    except Exception as e:
        logger.error(f"处理过程中发生错误: {e}")
        raise


async def main():
    """主函数：混合架构的6分钟天气数据处理（命令行版本）"""
    # 解析命令行参数
    parser = argparse.ArgumentParser(description='6分钟天气数据处理系统（异步版本）')
    parser.add_argument('data_type', nargs='?', default=None,
                       help='数据类型，如 gz_mpfv3（默认）')

    args = parser.parse_args()
    data_type = args.data_type

    # 调用处理函数
    await process_weather_data_hybrid(data_type)


if __name__ == '__main__':
    asyncio.run(main())
