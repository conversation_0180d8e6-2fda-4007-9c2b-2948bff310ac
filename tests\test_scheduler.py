#!/usr/bin/env python3
# coding: utf-8
"""
测试天气数据定时任务调度器
"""

import asyncio
import sys
from pathlib import Path
import logging

# 添加src目录到Python路径
src_path = Path(__file__).parent / "src"
sys.path.insert(0, str(src_path))

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

logger = logging.getLogger(__name__)


async def test_resource_manager():
    """测试资源管理器"""
    logger.info("测试资源管理器...")
    
    try:
        from resource_manager import resource_manager
        
        # 初始化资源管理器
        await resource_manager.initialize()
        logger.info("✓ 资源管理器初始化成功")
        
        # 测试数据库连接
        async with resource_manager.get_db_connection() as conn:
            result = await conn.fetchval("SELECT 1")
            assert result == 1
            logger.info("✓ 数据库连接测试成功")
        
        # 获取统计信息
        stats = resource_manager.get_stats()
        logger.info(f"✓ 资源统计: {stats}")
        
        # 关闭资源管理器
        await resource_manager.close()
        logger.info("✓ 资源管理器关闭成功")
        
    except Exception as e:
        logger.error(f"✗ 资源管理器测试失败: {e}")
        raise


async def test_uptime_checker():
    """测试uptime检查器"""
    logger.info("测试uptime检查器...")
    
    try:
        from uptime_checker import UptimeChecker, is_data_fresh
        
        # 测试6分钟数据检查
        checker = UptimeChecker("gz_mpfv3")
        is_fresh, api_result = await checker.check_data_freshness()
        logger.info(f"✓ 6分钟数据新鲜度检查: {is_fresh}")
        
        if api_result:
            logger.info(f"✓ API结果: {api_result}")
        
        # 测试便捷函数
        fresh = await is_data_fresh("gz_mpfv3")
        logger.info(f"✓ 便捷函数测试: {fresh}")
        
    except Exception as e:
        logger.error(f"✗ uptime检查器测试失败: {e}")
        # 这个测试可能因为网络问题失败，不抛出异常
        logger.warning("uptime检查器测试失败，可能是网络问题")


async def test_task_manager():
    """测试任务管理器"""
    logger.info("测试任务管理器...")
    
    try:
        from task_manager import task_manager
        from datetime import datetime, timedelta
        
        # 启动任务处理
        await task_manager.start_processing()
        logger.info("✓ 任务管理器启动成功")
        
        # 提交测试任务
        async def test_task():
            logger.info("执行测试任务...")
            await asyncio.sleep(2)  # 模拟任务执行
            return "任务完成"
        
        scheduled_time = datetime.now() + timedelta(seconds=5)
        task_id = await task_manager.submit_task(
            task_type="test",
            data_type="test_data",
            scheduled_time=scheduled_time,
            task_func=test_task
        )
        logger.info(f"✓ 测试任务提交成功: {task_id}")
        
        # 等待任务执行
        await asyncio.sleep(8)
        
        # 检查任务状态
        task_info = task_manager.get_task_status(task_id)
        if task_info:
            logger.info(f"✓ 任务状态: {task_info.status}")
        
        # 获取队列统计
        queue_stats = task_manager.get_queue_stats()
        logger.info(f"✓ 队列统计: {queue_stats}")
        
        # 停止任务管理器
        await task_manager.stop_processing()
        logger.info("✓ 任务管理器停止成功")
        
    except Exception as e:
        logger.error(f"✗ 任务管理器测试失败: {e}")
        raise


async def test_scheduler():
    """测试调度器"""
    logger.info("测试调度器...")
    
    try:
        from scheduler import WeatherTaskScheduler
        
        scheduler = WeatherTaskScheduler()
        
        # 测试时间计算
        next_hourly = scheduler._get_next_hourly_schedule()
        next_six_minute = scheduler._get_next_six_minute_schedule()
        
        logger.info(f"✓ 下一个1小时任务时间: {next_hourly}")
        logger.info(f"✓ 下一个6分钟任务时间: {next_six_minute}")
        
        # 获取状态
        status = scheduler.get_status()
        logger.info(f"✓ 调度器状态: {status}")
        
    except Exception as e:
        logger.error(f"✗ 调度器测试失败: {e}")
        raise


async def main():
    """主测试函数"""
    logger.info("开始测试天气数据定时任务调度器...")
    
    try:
        # 测试各个组件
        await test_resource_manager()
        await test_uptime_checker()
        await test_task_manager()
        await test_scheduler()
        
        logger.info("✓ 所有测试通过！")
        
    except Exception as e:
        logger.error(f"✗ 测试失败: {e}")
        sys.exit(1)


if __name__ == "__main__":
    asyncio.run(main())
