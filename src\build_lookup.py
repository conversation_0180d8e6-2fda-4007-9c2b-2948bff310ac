import geopandas as gpd
import pandas as pd
import math
from shapely.geometry import LineString
# linemerge 不再需要，使用强制连接策略
from sqlalchemy import create_engine, text
from geoalchemy2 import Geometry
from sqlalchemy.types import String, Integer, Numeric

# 导入统一配置
from config import (
    SHP_FILE_PATH, POSTGRES_CONFIG, ROUTE_TB, GRID_TB, LUT_TB, LUT_LINE_TB,
    GRID_X_MIN, GRID_Y_MIN, GRID_X_MAX, GRID_Y_MAX, GRID_SIZE_DEG, GRID_BUFFER_DEG,
    MYSQL_URL, MYSQL_CONFIG, STEP, LON_OFF, LAT_OFF,
    SHP_FIELD_PILE_START, SHP_FIELD_PILE_END, SHP_FIELD_OWNER_UNIT,
    SHP_FIELD_ROUTE_CODE, SHP_FIELD_ROUTE_NAME, SHP_FIELD_MAINTENANCE_SECTION,
    SHP_FIELD_XZQH, XZQH_TARGET_DIGITS,
    SNAP_TOL, get_postgres_url, load_yaml_config
)
# ---------- 0. 参数 ----------
SCHEMA = POSTGRES_CONFIG["schema"]

def encode_cell(lon: float, lat: float) -> int:
    """经纬 → cell_id（32bit 整数）"""
    ix = int(math.floor((lon + LON_OFF) / STEP))
    iy = int(math.floor((lat + LAT_OFF) / STEP))
    return (iy << 16) | ix          # 行主序打包

def decode_cell(cell_id: int) -> tuple[float, float]:
    """cell_id → 左下角 (lon_min, lat_min)"""
    ix = cell_id & 0xFFFF
    iy = cell_id >> 16
    lon_min = ix * STEP - LON_OFF
    lat_min = iy * STEP - LAT_OFF
    return lon_min, lat_min

def get_netcdf_indices(cell_id: int) -> tuple[int, int]:
    """cell_id → NetCDF 行列索引 (lat_idx, lon_idx)"""
    ix = cell_id & 0xFFFF
    iy = cell_id >> 16
    return iy, ix  # 返回 (lat_idx, lon_idx)

# ================================

# ---------- 1. GeoPandas 预处理 ----------
print("1) 读取并清洗路线 shp (用于天气数据分析)...")
routes_gdf = gpd.read_file(SHP_FILE_PATH)

# 字段名现在从config.py导入，无需重复定义

# ========== 行政区划代码处理函数 ==========
def normalize_xzqh_code(xzqh_str, target_digits=None):
    """
    标准化行政区划代码
    先根据target_digits截取前N位，然后根据去除末尾0后的实际位数，自动判断应该属于哪个行政层级，然后补充到对应的标准位数

    行政区划层级对应位数：
    - 2位：省级 (如：53 -> 53)
    - 4位：市级 (如：5301 -> 5301)
    - 6位：县级 (如：530102 -> 530102)
    - 9位：乡镇级 (如：530102001 -> 530102001)
    - 12位：村级 (如：530102001001 -> 530102001001)

    Args:
        xzqh_str: 原始行政区划代码字符串
        target_digits: 预处理截取位数，如果为None则使用配置中的默认值

    Returns:
        标准化后的行政区划代码字符串
    """
    if not xzqh_str or pd.isna(xzqh_str):
        return None

    # 转换为字符串并去除空格
    code_str = str(xzqh_str).strip()
    if not code_str:
        return None

    # 使用配置中的预处理截取位数
    if target_digits is None:
        target_digits = XZQH_TARGET_DIGITS

    # 预处理：先截取前target_digits位
    if len(code_str) > target_digits:
        code_str = code_str[:target_digits]

    # 去除末尾的0，但要小心不要把有效的10、20等去掉
    code_str = code_str.rstrip('0')

    # 如果全部都是0或者为空，返回None
    if not code_str:
        return None

    # 如果处理后为空，返回None
    if not code_str:
        return None

    # 根据当前长度确定应该补充到哪个标准长度
    current_len = len(code_str)
    if current_len <= 2:
        target_len = 2  # 省级
    elif current_len <= 4:
        target_len = 4  # 市级
    elif current_len <= 6:
        target_len = 6  # 县级
    elif current_len <= 9:
        target_len = 9  # 乡镇级
    elif current_len <= 12:
        target_len = 12 # 村级
    else:
        # 超过12位，截取到12位
        target_len = 12

    # 补充0到目标位数
    if current_len < target_len:
        code_str = code_str + '0' * (target_len - current_len)
    elif current_len > target_len:
        # 如果超过目标位数，截取到目标位数
        code_str = code_str[:target_len]

    return code_str

def get_region_mapping():
    """
    从MySQL获取行政区划代码和名称的映射关系

    Returns:
        dict: {data_code: data_name} 的映射字典
    """
    import pymysql

    try:
        # 使用config中的MySQL配置
        connection = pymysql.connect(
            host=MYSQL_CONFIG['host'],
            port=MYSQL_CONFIG['port'],
            user=MYSQL_CONFIG['username'],
            password=MYSQL_CONFIG['password'],
            database=MYSQL_CONFIG['database'],
            charset='utf8mb4'
        )

        with connection.cursor() as cursor:
            sql = """
            SELECT data_code, data_name
            FROM sys_region_code
            WHERE data_code IS NOT NULL
            AND data_code != ''
            AND data_name IS NOT NULL
            AND data_name != ''
            """
            cursor.execute(sql)
            results = cursor.fetchall()

            # 构建映射字典
            mapping = {}
            for row in results:
                data_code, data_name = row
                mapping[str(data_code)] = data_name

            print(f"   ‣ 从MySQL获取到 {len(mapping)} 条行政区划映射关系")
            return mapping

    except Exception as e:
        print(f"   ⚠ 获取行政区划映射关系失败: {e}")
        return {}
    finally:
        if 'connection' in locals():
            connection.close()

# ========== ★★ 你的合并工具函数（原样搬过来） ★★ ==========
# ========== 线段合并工具函数 ==========

def _try_merge_group_to_single_line(group_rows, max_gap_distance=None):
    """
    尝试将一组线段合并为单个线段

    Args:
        group_rows: 要合并的线段行列表
        max_gap_distance: 最大允许的间隙距离（度），如果为None则无距离限制

    策略：
    1. 按桩号排序
    2. 强制连接所有线段（可选距离限制）
    3. 创建合并后的线段
    """
    if len(group_rows) == 1:
        return group_rows

    group_rows = sorted(group_rows, key=lambda r: r[SHP_FIELD_PILE_START])
    geoms = [r.geometry for r in group_rows]

    try:
        # 使用强制连接策略（可选距离限制）
        merged_geom = _force_connect_lines(geoms, max_gap_distance)

    except Exception:
        merged_geom = None

    # 如果合并成功，创建合并后的行
    if merged_geom is not None and merged_geom.geom_type == "LineString":
        merged_row = group_rows[0].copy()
        merged_row.geometry = merged_geom
        merged_row[SHP_FIELD_PILE_END] = group_rows[-1][SHP_FIELD_PILE_END]
        return [merged_row]
    else:
        # 合并失败，返回原始行
        return group_rows

def _force_connect_lines(geoms, max_gap_distance=None):
    """
    强制连接线段

    Args:
        geoms: 要连接的线段列表
        max_gap_distance: 最大允许的间隙距离（度），如果为None则无距离限制

    策略：
    1. 按桩号顺序已经排序的线段，直接按顺序连接
    2. 对于每个后续线段，选择距离前一段终点更近的端点作为起点
    3. 如果设置了距离限制且间隙超过限制，则不连接该线段
    4. 如果需要，反转线段方向以实现最佳连接
    """
    try:
        if len(geoms) <= 1:
            return geoms[0] if geoms else None

        # 将所有线段的坐标点收集起来，按顺序连接
        all_coords = []

        for i, geom in enumerate(geoms):
            coords = list(geom.coords)

            if i == 0:
                # 第一个线段，直接添加所有坐标
                all_coords.extend(coords)
            else:
                # 后续线段，检查是否需要反向以实现更好的连接
                prev_end = all_coords[-1]
                current_start = coords[0]
                current_end = coords[-1]

                # 计算两种连接方式的距离
                dist_normal = ((prev_end[0] - current_start[0])**2 + (prev_end[1] - current_start[1])**2)**0.5
                dist_reverse = ((prev_end[0] - current_end[0])**2 + (prev_end[1] - current_end[1])**2)**0.5

                # 选择距离更短的连接方式
                min_distance = min(dist_normal, dist_reverse)

                # 如果设置了距离限制，检查是否超过限制
                if max_gap_distance is not None and min_distance > max_gap_distance:
                    # 间隙太大，停止连接，返回已连接的部分
                    break

                if dist_reverse < dist_normal:
                    # 反向连接距离更短，反转坐标
                    coords = list(reversed(coords))

                # 添加坐标（跳过第一个点以避免重复）
                all_coords.extend(coords[1:])

        # 创建连接后的线段
        if len(all_coords) >= 2:
            return LineString(all_coords)
        else:
            return geoms[0]

    except Exception:
        # 如果强制连接失败，返回第一个几何体
        return geoms[0] if geoms else None

def _merge_initial_segments(df, max_gap_distance=None):
    """
    合并桩号连续的相邻子段

    Args:
        df: 包含线段数据的GeoDataFrame
        max_gap_distance: 最大允许的间隙距离（度），如果为None则无距离限制

    合并条件：
    1. 管理处相同 (SHP_FIELD_OWNER_UNIT)
    2. 路线相同 (SHP_FIELD_ROUTE_CODE)
    3. 养护路段相同 (SHP_FIELD_MAINTENANCE_SECTION)
    4. 行政区划相同 (normalized_xzqh)
    5. 桩号连续 (前一段终点桩号 = 后一段起点桩号)
    6. 如果设置了max_gap_distance，还需要几何间隙不超过该距离
    """
    if df.empty:
        return df

    distance_info = f"（距离限制: {max_gap_distance}度）" if max_gap_distance else "（无距离限制）"
    print(f"   ‣ 合并桩号连续的相邻子段{distance_info}...")

    # 按关键字段排序
    df_sorted = (df.sort_values(
        [SHP_FIELD_OWNER_UNIT, SHP_FIELD_ROUTE_CODE,
         SHP_FIELD_MAINTENANCE_SECTION, 'normalized_xzqh', SHP_FIELD_PILE_START])
        .reset_index(drop=True))

    results = []
    current_group = []

    def flush_group(group):
        """处理当前组的线段"""
        if not group:
            return
        if len(group) == 1:
            results.extend(group)
        else:
            results.extend(_try_merge_group_to_single_line(group, max_gap_distance))

    # 遍历所有行，按组收集
    for _, row in df_sorted.iterrows():
        if not current_group:
            current_group.append(row)
            continue

        prev = current_group[-1]

        # 检查是否属于同一组（属性相同）
        same_attr = (
            row[SHP_FIELD_OWNER_UNIT] == prev[SHP_FIELD_OWNER_UNIT] and
            row[SHP_FIELD_ROUTE_CODE] == prev[SHP_FIELD_ROUTE_CODE] and
            row[SHP_FIELD_MAINTENANCE_SECTION] == prev[SHP_FIELD_MAINTENANCE_SECTION] and
            row['normalized_xzqh'] == prev['normalized_xzqh']
        )

        # 检查桩号是否连续（这是唯一的连接条件）
        pile_continuous = (row[SHP_FIELD_PILE_START] == prev[SHP_FIELD_PILE_END])

        # 如果属性相同且桩号连续，则加入当前组
        if same_attr and pile_continuous:
            current_group.append(row)
        else:
            # 否则处理当前组，开始新组
            flush_group(current_group)
            current_group = [row]

    # 处理最后一组
    flush_group(current_group)

    print(f"   ‣ 合并完成：{len(df)} -> {len(results)} 个线段")
    return gpd.GeoDataFrame(results, crs=df.crs).reset_index(drop=True)
# ==========================================================

def get_dept_mapping():
    """
    从MySQL数据库查询部门映射关系
    返回 dept_name -> dept_id 的字典
    """
    try:
        mysql_engine = create_engine(MYSQL_URL)
        query = "SELECT dept_name, dept_id FROM sys_dept"
        df = pd.read_sql(query, mysql_engine)
        mysql_engine.dispose()

        # 创建映射字典
        mapping = dict(zip(df['dept_name'], df['dept_id']))
        print(f"成功获取 {len(mapping)} 个部门映射关系")
        return mapping
    except Exception as e:
        print(f"查询部门映射关系失败: {e}")
        return {}


def get_maintenance_section_mapping():
    """
    从MySQL数据库查询养护路段映射关系
    返回 maintenance_section_name -> maintenance_section_id 的字典
    """
    try:
        mysql_engine = create_engine(MYSQL_URL)
        query = "SELECT maintenance_section_name, maintenance_section_id FROM base_maintenance_section"
        df = pd.read_sql(query, mysql_engine)
        mysql_engine.dispose()

        # 创建映射字典
        mapping = dict(zip(df['maintenance_section_name'], df['maintenance_section_id']))
        print(f"成功获取 {len(mapping)} 个养护路段映射关系")
        return mapping
    except Exception as e:
        print(f"查询养护路段映射关系失败: {e}")
        return {}


def get_owner_type_mapping():
    """
    从MySQL数据库查询所有者类型映射关系
    返回 dict_label -> dict_value 的字典
    """
    try:
        mysql_engine = create_engine(MYSQL_URL)
        query = "SELECT dict_label, dict_value FROM sys_dict_data WHERE dict_type = 'sys_dept_types'"
        df = pd.read_sql(query, mysql_engine)
        mysql_engine.dispose()

        # 去重，保留dict_value小的那个
        df_sorted = df.sort_values('dict_value')
        df_unique = df_sorted.drop_duplicates(subset=['dict_label'], keep='first')

        # 创建映射字典
        mapping = dict(zip(df_unique['dict_label'], df_unique['dict_value']))
        print(f"成功获取 {len(mapping)} 个所有者类型映射关系")
        return mapping
    except Exception as e:
        print(f"查询所有者类型映射关系失败: {e}")
        return {}

# 1.1 数值转换
routes_gdf[SHP_FIELD_PILE_START] = pd.to_numeric(
        routes_gdf[SHP_FIELD_PILE_START], errors='coerce')*1000
routes_gdf[SHP_FIELD_PILE_END]   = pd.to_numeric(
        routes_gdf[SHP_FIELD_PILE_END],   errors='coerce')*1000
routes_gdf.dropna(subset=[SHP_FIELD_PILE_START,SHP_FIELD_PILE_END], inplace=True)

# 1.2 处理行政区划代码
print("处理行政区划代码...")
if SHP_FIELD_XZQH in routes_gdf.columns:
    # 标准化行政区划代码
    routes_gdf['normalized_xzqh'] = routes_gdf[SHP_FIELD_XZQH].apply(normalize_xzqh_code)
    print(f"   ‣ 处理了 {len(routes_gdf)} 条记录的行政区划代码，目标位数: {XZQH_TARGET_DIGITS}")

    # 显示一些处理示例
    sample_data = routes_gdf[[SHP_FIELD_XZQH, 'normalized_xzqh']].dropna().head(10)
    if not sample_data.empty:
        print("   ‣ 行政区划代码处理示例:")
        for _, row in sample_data.iterrows():
            original = row[SHP_FIELD_XZQH]
            normalized = row['normalized_xzqh']
            print(f"      {original} -> {normalized}")
else:
    print(f"   ⚠ 未找到行政区划字段 '{SHP_FIELD_XZQH}'，将使用空值")
    routes_gdf['normalized_xzqh'] = None

# 1.3 合并子段
# 从配置文件读取距离限制参数
config = load_yaml_config()
max_gap_distance = config.get('processing', {}).get('merge_segments', {}).get('max_gap_distance')
routes_gdf = _merge_initial_segments(routes_gdf, max_gap_distance)

# 1.4 获取映射关系
print("获取部门、养护路段、所有者类型和行政区划映射关系...")
dept_mapping = get_dept_mapping()
maintenance_mapping = get_maintenance_section_mapping()
owner_type_mapping = get_owner_type_mapping()
region_mapping = get_region_mapping()

# 1.5 生成内部字段
routes_gdf["pile_start"] = routes_gdf[SHP_FIELD_PILE_START]
routes_gdf["pile_end"]   = routes_gdf[SHP_FIELD_PILE_END]
routes_gdf["span_abs"]   = (routes_gdf["pile_end"]-routes_gdf["pile_start"]).abs()
routes_gdf["dir_sign"]   = (routes_gdf["pile_end"]>=routes_gdf["pile_start"]).astype(int)*2-1

# 1.6 添加行政区划代码和名称
routes_gdf["region_code"] = routes_gdf["normalized_xzqh"]
routes_gdf["region_name"] = routes_gdf["normalized_xzqh"].map(region_mapping)

# 对于没有匹配到名称的，使用代码作为名称
routes_gdf["region_name"] = routes_gdf["region_name"].fillna(routes_gdf["region_code"])

print(f"   ‣ 行政区划映射完成，匹配到名称的记录数: {routes_gdf['region_name'].notna().sum()}")

# 1.5 添加部门ID、养护路段ID和所有者类型
print("添加部门ID、养护路段ID和所有者类型映射...")
# 假设shp文件中的管理单位字段名为 "tbdw"
if "tbdw" in routes_gdf.columns:
    routes_gdf["management_office_id"] = routes_gdf["tbdw"].map(dept_mapping)
    mapped_dept_count = routes_gdf["management_office_id"].notna().sum()
    print(f"部门ID映射成功: {mapped_dept_count}/{len(routes_gdf)} 条")
else:
    routes_gdf["management_office_id"] = None
    print("警告: 未找到管理单位字段 'tbdw'")

# 养护路段ID映射
if SHP_FIELD_MAINTENANCE_SECTION in routes_gdf.columns:
    routes_gdf["maintenance_section_id"] = routes_gdf[SHP_FIELD_MAINTENANCE_SECTION].map(maintenance_mapping)
    mapped_maintenance_count = routes_gdf["maintenance_section_id"].notna().sum()
    print(f"养护路段ID映射成功: {mapped_maintenance_count}/{len(routes_gdf)} 条")
else:
    routes_gdf["maintenance_section_id"] = None
    print(f"警告: 未找到养护路段字段 '{SHP_FIELD_MAINTENANCE_SECTION}'")

# 所有者类型映射
if SHP_FIELD_OWNER_UNIT in routes_gdf.columns:
    routes_gdf["owner_type"] = routes_gdf[SHP_FIELD_OWNER_UNIT].map(owner_type_mapping)
    mapped_owner_count = routes_gdf["owner_type"].notna().sum()
    print(f"所有者类型映射成功: {mapped_owner_count}/{len(routes_gdf)} 条")
else:
    routes_gdf["owner_type"] = None
    print(f"警告: 未找到所有者单位字段 '{SHP_FIELD_OWNER_UNIT}'")

# ---------- 2. 准备数据库连接并检查表锁定状态 ----------
print("\n2) 准备数据库连接...")
engine = create_engine(get_postgres_url().replace("postgresql://", "postgresql+psycopg2://"))

def check_and_terminate_locks():
    """检查并终止锁定表的连接"""
    with engine.connect().execution_options(isolation_level="AUTOCOMMIT") as conn:
        # 查询锁定表的进程
        result = conn.execute(text("""
            SELECT pid, usename, application_name, state, query
            FROM pg_stat_activity
            WHERE datname = current_database()
            AND pid != pg_backend_pid()
            AND (query ILIKE '%weather_routes%' OR query ILIKE '%weather_grid%' OR query ILIKE '%weather_cell_route_lut%')
        """))

        locked_pids = []
        for row in result:
            print(f"   ‣ 发现锁定进程: PID={row.pid}, 用户={row.usename}, 应用={row.application_name}, 状态={row.state}")
            locked_pids.append(row.pid)

        # 终止锁定的进程
        for pid in locked_pids:
            try:
                conn.execute(text(f"SELECT pg_terminate_backend({pid});"))
                print(f"   ‣ 已终止进程 PID={pid}")
            except Exception as e:
                print(f"   ‣ 终止进程 PID={pid} 失败: {e}")

print("   ‣ 检查表锁定状态...")
check_and_terminate_locks()
print("   ‣ 数据库连接已建立")

# ---------- 3. 使用 GeoPandas 导入路线数据到 PostGIS ----------
print("\n3) 使用 GeoPandas 导入路线数据到 PostGIS ...")

# 3.1 准备要保存的字段（只保存重要信息）
print("   ‣ 准备路线数据...")
routes_to_save = routes_gdf.copy()

# 确保坐标系为WGS84
if routes_to_save.crs != 'EPSG:4326':
    print(f"   ‣ 转换坐标系从 {routes_to_save.crs} 到 WGS84...")
    routes_to_save = routes_to_save.to_crs('EPSG:4326')

# 选择要保存的重要字段
important_fields = [
    'geometry',  # 几何字段
    'pile_start', 'pile_end', 'span_abs', 'dir_sign',  # 桩号相关
    'management_office_id', 'maintenance_section_id', 'owner_type',  # ID映射
    'region_code', 'region_name',  # 行政区划
]

# 添加一些原始字段用于参考
if SHP_FIELD_ROUTE_CODE in routes_to_save.columns:
    important_fields.append(SHP_FIELD_ROUTE_CODE)
    routes_to_save = routes_to_save.rename(columns={SHP_FIELD_ROUTE_CODE: 'route_code'})

if SHP_FIELD_ROUTE_NAME in routes_to_save.columns:
    important_fields.append(SHP_FIELD_ROUTE_NAME)
    routes_to_save = routes_to_save.rename(columns={SHP_FIELD_ROUTE_NAME: 'route_name'})

if 'tbdw' in routes_to_save.columns:
    important_fields.append('tbdw')
    routes_to_save = routes_to_save.rename(columns={'tbdw': 'management_office'})

if SHP_FIELD_MAINTENANCE_SECTION in routes_to_save.columns:
    important_fields.append(SHP_FIELD_MAINTENANCE_SECTION)
    routes_to_save = routes_to_save.rename(columns={SHP_FIELD_MAINTENANCE_SECTION: 'maintenance_section'})

# 注意：不保存 owner_unit 原始字段到数据库，只保存映射后的 owner_type

# 更新字段列表以使用新的列名
final_fields = [
    'geometry', 'pile_start', 'pile_end', 'span_abs', 'dir_sign',
    'management_office_id', 'maintenance_section_id', 'owner_type',
    'region_code', 'region_name'
]

# 添加可选字段
optional_fields = ['route_code', 'route_name', 'management_office', 'maintenance_section']
for field in optional_fields:
    if field in routes_to_save.columns:
        final_fields.append(field)

# 选择最终要保存的字段
routes_final = routes_to_save[final_fields].copy()

# 3.2 定义数据类型映射
print("   ‣ 定义数据类型映射...")
dtype_map = {
    "geometry": Geometry("LINESTRING", srid=4326),
    "pile_start": Numeric(10, 2),
    "pile_end": Numeric(10, 2),
    "span_abs": Numeric(10, 2),
    "dir_sign": Integer,
    "management_office_id": String,
    "maintenance_section_id": String,
    "owner_type": String,
    "region_code": String,
    "region_name": String,
}

# 添加可选字段的类型
if 'route_code' in routes_final.columns:
    dtype_map['route_code'] = String
if 'route_name' in routes_final.columns:
    dtype_map['route_name'] = String
if 'management_office' in routes_final.columns:
    dtype_map['management_office'] = String
if 'maintenance_section' in routes_final.columns:
    dtype_map['maintenance_section'] = String

# 3.3 使用 to_postgis 写入数据库
print(f"   ‣ 正在将 {len(routes_final)} 条路线记录写入到表 {ROUTE_TB}...")

# 使用 replace 模式清除旧表并创建新表
routes_final.to_postgis(
    name=ROUTE_TB,
    con=engine,
    if_exists="replace",  # 每次都清除后再导入
    index=False,
    dtype=dtype_map
)

# 3.4 创建几何索引# 3.4 创建几何索引 + 其它索引 + 自增主键  --------------------------
with engine.begin() as conn:
    # 几何索引
    conn.execute(text(
        f"CREATE INDEX idx_{ROUTE_TB}_geom ON {ROUTE_TB} USING gist(geometry);"))

    # 普通索引
    if 'management_office_id' in routes_final.columns:
        conn.execute(text(
            f"CREATE INDEX idx_{ROUTE_TB}_mgmt_office "
            f"ON {ROUTE_TB}(management_office_id);"))
    if 'maintenance_section_id' in routes_final.columns:
        conn.execute(text(
            f"CREATE INDEX idx_{ROUTE_TB}_maint_section "
            f"ON {ROUTE_TB}(maintenance_section_id);"))

    # --------- 关键：补 route_id 主键 ---------
    # 1) 如果列不存在就加；类型用 BIGSERIAL = big integer + sequence
    conn.execute(text(
        f"ALTER TABLE {ROUTE_TB} "
        f"ADD COLUMN IF NOT EXISTS route_id BIGSERIAL;"))

    # 2) 让旧数据（刚写进去的这批）拿到序列号
    conn.execute(text(
        f"UPDATE {ROUTE_TB} "
        f"SET route_id = nextval(pg_get_serial_sequence('{ROUTE_TB}','route_id')) "
        f"WHERE route_id IS NULL;"))

    # 3) 把它设成主键（重复执行时先删旧主键）
    conn.execute(text(
        f"ALTER TABLE {ROUTE_TB} "
        f"DROP CONSTRAINT IF EXISTS {ROUTE_TB}_pkey;"))
    conn.execute(text(
        f"ALTER TABLE {ROUTE_TB} "
        f"ADD CONSTRAINT {ROUTE_TB}_pkey PRIMARY KEY (route_id);"))

print("   √ 路线表已写入 PostGIS，并补齐 route_id 主键")


# ---------- 4. 用 SQLAlchemy 执行后续 SQL ----------
print("\n4) 执行建格网 / 生成 cell_route_lut 的 SQL ...")

grid_sql = f"""
DROP TABLE IF EXISTS {GRID_TB} CASCADE;

-- 创建标准格网编码/解码函数
CREATE OR REPLACE FUNCTION cell_encode(lon double precision, lat double precision)
RETURNS integer AS $$
DECLARE
    ix integer := floor((lon + 180)::numeric / 0.01);
    iy integer := floor((lat +  90)::numeric / 0.01);
BEGIN
    RETURN (iy << 16) | ix;
END;
$$ LANGUAGE plpgsql IMMUTABLE;

CREATE OR REPLACE FUNCTION cell_decode_x(cell_id integer)
RETURNS double precision AS $$
BEGIN
    RETURN ((cell_id & 65535) * 0.01) - 180;
END;
$$ LANGUAGE plpgsql IMMUTABLE;

CREATE OR REPLACE FUNCTION cell_decode_y(cell_id integer)
RETURNS double precision AS $$
BEGIN
    RETURN ((cell_id >> 16) * 0.01) - 90;
END;
$$ LANGUAGE plpgsql IMMUTABLE;

-- 使用标准格网ID方案创建格网表
CREATE TABLE {GRID_TB} AS
WITH bounds AS (
    SELECT {GRID_X_MIN}::numeric AS xmin ,
           {GRID_Y_MIN}::numeric AS ymin ,
           {GRID_X_MAX}::numeric AS xmax ,
           {GRID_Y_MAX}::numeric AS ymax ,
           {GRID_SIZE_DEG}::numeric AS cs )
SELECT cell_encode(x, y) AS cell_id,
       ST_SetSRID(
         ST_MakePolygon(
           ST_MakeLine(ARRAY[
             ST_MakePoint(x    , y    ),
             ST_MakePoint(x+cs , y    ),
             ST_MakePoint(x+cs , y+cs ),
             ST_MakePoint(x    , y+cs ),
             ST_MakePoint(x    , y    )])),4326) AS geom
FROM bounds,
     generate_series(xmin , xmax - cs , cs) AS x,
     generate_series(ymin , ymax - cs , cs) AS y;

CREATE INDEX idx_{GRID_TB}_geom ON {GRID_TB} USING gist(geom);
ALTER TABLE {GRID_TB} ADD PRIMARY KEY (cell_id);
"""

lut_sql = f"""
DROP TABLE IF EXISTS {LUT_TB} CASCADE;

CREATE TABLE {LUT_TB} AS
WITH seg AS (
    SELECT  g.cell_id,
            r.route_id,
            (ST_Dump(
               ST_LineMerge(
                 ST_Intersection(
                     r.geometry,
                     ST_Buffer(g.geom, {GRID_BUFFER_DEG})
                 )
            ))).geom                           AS seg_geom,
            r.geometry                         AS route_geom,
            r.pile_start,
            r.span_abs,
            r.dir_sign,
            r.management_office_id,
            r.maintenance_section_id,
            r.owner_type,
            r.route_code,
            r.route_name,
            r.management_office,
            r.maintenance_section,
            r.region_code,
            r.region_name
    FROM   {GRID_TB} g
    JOIN   {ROUTE_TB} r
      ON   ST_DWithin(g.geom, r.geometry, {GRID_BUFFER_DEG})  -- 使用ST_DWithin替代ST_Intersects+Buffer
)
, meas AS (
    SELECT cell_id, route_id, seg_geom,
           pile_start, span_abs, dir_sign,
           management_office_id, maintenance_section_id, owner_type,
           route_code, route_name, management_office, maintenance_section,
           region_code, region_name,
           ST_LineLocatePoint(route_geom, ST_StartPoint(seg_geom)) AS f1,
           ST_LineLocatePoint(route_geom, ST_EndPoint  (seg_geom)) AS f2
    FROM   seg
    WHERE  GeometryType(seg_geom) LIKE 'LINE%'
)
SELECT cell_id,
       route_id,
       ROUND( LEAST( pile_start + dir_sign*f1*span_abs ,
                     pile_start + dir_sign*f2*span_abs )::numeric, 2) AS pk_from,
       ROUND( GREATEST( pile_start + dir_sign*f1*span_abs ,
                        pile_start + dir_sign*f2*span_abs )::numeric, 2) AS pk_to,
       management_office_id,
       maintenance_section_id,
       owner_type,
       route_code,
       route_name,
       management_office,
       maintenance_section,
       region_code,
       region_name,
       ST_SetSRID(seg_geom,4326) AS geom
FROM meas;
 
ALTER TABLE {LUT_TB}
  ADD PRIMARY KEY (cell_id, route_id, pk_from, pk_to);

-- 行列索引 + GiST 几何索引
CREATE INDEX idx_{LUT_TB}_cell            ON {LUT_TB}(cell_id);
CREATE INDEX idx_{LUT_TB}_route           ON {LUT_TB}(route_id);
CREATE INDEX idx_{LUT_TB}_mgmt_office     ON {LUT_TB}(management_office_id);
CREATE INDEX idx_{LUT_TB}_maint_section   ON {LUT_TB}(maintenance_section_id);
CREATE INDEX idx_{LUT_TB}_owner_type      ON {LUT_TB}(owner_type);
CREATE INDEX idx_{LUT_TB}_geom            ON {LUT_TB} USING gist(geom);   -- ★ 保留几何索引用于空间分析

-- 添加表和字段注释
COMMENT ON TABLE {LUT_TB} IS '天气格网与路线查找表（含缓冲区影响范围）';
COMMENT ON COLUMN {LUT_TB}.cell_id IS '格网ID（标准编码）';
COMMENT ON COLUMN {LUT_TB}.route_id IS '路线ID';
COMMENT ON COLUMN {LUT_TB}.pk_from IS '起始桩号（米）';
COMMENT ON COLUMN {LUT_TB}.pk_to IS '结束桩号（米）';
COMMENT ON COLUMN {LUT_TB}.geom IS '路线与格网缓冲区的交线几何（保留用于制图和二次分析）';
COMMENT ON COLUMN {LUT_TB}.management_office_id IS '管理单位ID';
COMMENT ON COLUMN {LUT_TB}.maintenance_section_id IS '养护路段ID';
COMMENT ON COLUMN {LUT_TB}.owner_type IS '所有者类型编码';
"""


# 线性映射查找表SQL（不使用缓冲，实际通过的受影响段）
lut_line_sql = f"""
SET work_mem='1GB';
SET synchronous_commit=off;

DROP TABLE IF EXISTS {LUT_LINE_TB} CASCADE;

WITH
route_ext AS (
  SELECT r.route_id, r.geometry route_geom,
         ST_XMin(r.geometry) x_min, ST_XMax(r.geometry) x_max,
         ST_YMin(r.geometry) y_min, ST_YMax(r.geometry) y_max,
         r.pile_start, r.span_abs, r.dir_sign,
         r.management_office_id, r.maintenance_section_id,
         r.owner_type, r.route_code,
         r.route_name,
         r.management_office, r.maintenance_section,
         r.region_code, r.region_name
  FROM {ROUTE_TB} r
  WHERE ST_Intersects(
        r.geometry,
        ST_MakeEnvelope({GRID_X_MIN},{GRID_Y_MIN},{GRID_X_MAX},{GRID_Y_MAX},4326)
  )
),
cells AS (
  SELECT re.route_id,
         ((iy<<16)|ix) cell_id
  FROM route_ext re
  CROSS JOIN LATERAL (
       SELECT floor((re.x_min+180)/{GRID_SIZE_DEG})::int ix_min,
              floor((re.x_max+180)/{GRID_SIZE_DEG})::int ix_max,
              floor((re.y_min+ 90)/{GRID_SIZE_DEG})::int iy_min,
              floor((re.y_max+ 90)/{GRID_SIZE_DEG})::int iy_max
  ) b
  CROSS JOIN LATERAL generate_series(b.ix_min,b.ix_max) ix
  CROSS JOIN LATERAL generate_series(b.iy_min,b.iy_max) iy
  WHERE ST_Intersects(
        ST_MakeEnvelope(ix*{GRID_SIZE_DEG}-180, iy*{GRID_SIZE_DEG}-90,
                        (ix+1)*{GRID_SIZE_DEG}-180,(iy+1)*{GRID_SIZE_DEG}-90,4326),
        re.route_geom)
),
seg AS (
  SELECT c.cell_id, re.route_id,
         (ST_Dump(ST_LineMerge(
             ST_Intersection(re.route_geom,
                 ST_MakeEnvelope( (c.cell_id & 65535 )*{GRID_SIZE_DEG}-180,
                                  ((c.cell_id>>16))*{GRID_SIZE_DEG}-90,
                                  ((c.cell_id & 65535)+1)*{GRID_SIZE_DEG}-180,
                                  ((c.cell_id>>16)+1)*{GRID_SIZE_DEG}-90,4326))
         ))).geom seg_geom,
         re.route_geom, re.pile_start, re.span_abs, re.dir_sign,
         re.management_office_id, re.maintenance_section_id,
         re.owner_type, re.route_code, re.route_name,
         re.management_office, re.maintenance_section,
         re.region_code, re.region_name
  FROM cells c
  JOIN route_ext re USING(route_id)
  WHERE GeometryType(re.route_geom)='LINESTRING'
),
locs AS (
  SELECT *, 
         ST_LineLocatePoint(route_geom,ST_StartPoint(seg_geom)) s_loc,
         ST_LineLocatePoint(route_geom,ST_EndPoint(seg_geom))   e_loc
  FROM seg
)
SELECT cell_id, route_id,
       LEAST(s_loc,e_loc) s_loc,
       GREATEST(s_loc,e_loc) e_loc,
       ROUND(LEAST(pile_start+dir_sign*span_abs*LEAST(s_loc,e_loc),
                   pile_start+dir_sign*span_abs*GREATEST(s_loc,e_loc))::numeric,2) pk_from,
       ROUND(GREATEST(pile_start+dir_sign*span_abs*LEAST(s_loc,e_loc),
                      pile_start+dir_sign*span_abs*GREATEST(s_loc,e_loc))::numeric,2) pk_to,
       management_office_id, maintenance_section_id,
       owner_type, route_code, route_name,
       management_office, maintenance_section,
       region_code, region_name,
       ROUND(ST_Length(seg_geom::geography)::numeric,2) seg_len_m,
       ST_SetSRID(seg_geom,4326) geom
INTO {LUT_LINE_TB}
FROM locs
WHERE GeometryType(seg_geom) LIKE 'LINE%';

ALTER TABLE {LUT_LINE_TB}
  ADD PRIMARY KEY(cell_id,route_id,pk_from,pk_to);
CREATE INDEX idx_{LUT_LINE_TB}_cell ON {LUT_LINE_TB}(cell_id);
CREATE INDEX idx_{LUT_LINE_TB}_geom ON {LUT_LINE_TB} USING gist(geom);

-- 添加表和字段注释
COMMENT ON TABLE {LUT_LINE_TB} IS '天气格网与路线线性查找表（实际通过段，无缓冲）';
COMMENT ON COLUMN {LUT_LINE_TB}.cell_id IS '格网ID（标准编码）';
COMMENT ON COLUMN {LUT_LINE_TB}.route_id IS '路线ID';
COMMENT ON COLUMN {LUT_LINE_TB}.s_loc IS '起始位置比例（0-1）';
COMMENT ON COLUMN {LUT_LINE_TB}.e_loc IS '结束位置比例（0-1）';
COMMENT ON COLUMN {LUT_LINE_TB}.pk_from IS '起始桩号（米）';
COMMENT ON COLUMN {LUT_LINE_TB}.pk_to IS '结束桩号（米）';
COMMENT ON COLUMN {LUT_LINE_TB}.seg_len_m IS '路段长度（米）';
COMMENT ON COLUMN {LUT_LINE_TB}.geom IS '实际通过的路线段几何';
"""


with engine.begin() as conn:
    print("   ‣ 创建格网表...")
    conn.execute(text(grid_sql))
    print("   ‣ 生成 cell_route_lut 表...")
    conn.execute(text(lut_sql))
    print("   ‣ 生成 cell_route_lut_line 表（线通过，无缓冲）...")
    conn.execute(text(lut_line_sql))
print("   √ 全部 SQL 执行完毕")

fix_zero_sql = f"""
-- ε：判定“0 长度”的阈值，单位 = 米
DO $$
DECLARE
    eps numeric := 1;          -- <1 米都当作 0
BEGIN
    -- 1) 把符合条件的 LUT 行挑出来，连同原路线 / 格网几何带进来
    WITH z AS (
        SELECT lut.cell_id, lut.route_id,
               r.geometry AS route_geom,
               g.geom     AS grid_geom,
               r.pile_start, r.span_abs, r.dir_sign
        FROM   {LUT_TB} lut
        JOIN   {ROUTE_TB}         r ON r.route_id = lut.route_id
        JOIN   {GRID_TB}           g ON g.cell_id  = lut.cell_id
        WHERE  abs(lut.pk_to - lut.pk_from) < eps
    )
    -- 2) 对每一条“疑似 0 长度”交线做 DumpPoints → 求 min/max locate
    , rng AS (
        SELECT cell_id, route_id, pile_start, span_abs, dir_sign,
               MIN(loc) AS loc_min,
               MAX(loc) AS loc_max
        FROM (
            SELECT z.*,
                   ST_LineLocatePoint(z.route_geom, (dp).geom) AS loc
            FROM   z
            CROSS  JOIN LATERAL ST_DumpPoints(
                     ST_LineMerge(
                       ST_Intersection(
                         z.route_geom,
                         ST_Buffer(z.grid_geom, 0.02)   -- ← 与建 LUT 时保持一致
                       )
                     )
                   ) AS dp
        ) q
        GROUP BY cell_id, route_id, pile_start, span_abs, dir_sign
    )
    -- 3) 把重新算出的 pk_from / pk_to 回写 LUT
    UPDATE {LUT_TB} lut
    SET    pk_from = ROUND((pile_start + dir_sign*loc_min*span_abs)::numeric, 2),
           pk_to   = ROUND((pile_start + dir_sign*loc_max*span_abs)::numeric, 2)
    FROM   rng
    WHERE  lut.cell_id  = rng.cell_id
      AND  lut.route_id = rng.route_id
      AND  abs(lut.pk_to - lut.pk_from) < eps;

    -- ========== 修复 weather_cell_route_lut_line 表 ==========
    -- 1) 把符合条件的 LUT_LINE 行挑出来，连同原路线 / 格网几何带进来
    WITH z_line AS (
        SELECT lut.cell_id, lut.route_id,
               r.geometry AS route_geom,
               g.geom     AS grid_geom,
               r.pile_start, r.span_abs, r.dir_sign
        FROM   {LUT_LINE_TB} lut
        JOIN   {ROUTE_TB}         r ON r.route_id = lut.route_id
        JOIN   {GRID_TB}           g ON g.cell_id  = lut.cell_id
        WHERE  abs(lut.pk_to - lut.pk_from) < eps
    )
    -- 2) 对每一条"疑似 0 长度"交线做 DumpPoints → 求 min/max locate（无缓冲）
    , rng_line AS (
        SELECT cell_id, route_id, pile_start, span_abs, dir_sign,
               MIN(loc) AS loc_min,
               MAX(loc) AS loc_max
        FROM (
            SELECT z_line.*,
                   ST_LineLocatePoint(z_line.route_geom, (dp).geom) AS loc
            FROM   z_line
            CROSS  JOIN LATERAL ST_DumpPoints(
                     ST_LineMerge(
                       ST_Intersection(
                         z_line.route_geom,
                         z_line.grid_geom   -- ← 线性表无缓冲
                       )
                     )
                   ) AS dp
        ) q
        GROUP BY cell_id, route_id, pile_start, span_abs, dir_sign
    )
    -- 3) 把重新算出的 pk_from / pk_to 回写 LUT_LINE
    UPDATE {LUT_LINE_TB} lut
    SET    pk_from = ROUND((pile_start + dir_sign*loc_min*span_abs)::numeric, 2),
           pk_to   = ROUND((pile_start + dir_sign*loc_max*span_abs)::numeric, 2)
    FROM   rng_line
    WHERE  lut.cell_id  = rng_line.cell_id
      AND  lut.route_id = rng_line.route_id
      AND  abs(lut.pk_to - lut.pk_from) < eps;
END $$;
"""
 
print("   ‣ 修复0长度问题（包括线性LUT表）...")
with engine.begin() as conn:
    conn.execute(text(fix_zero_sql))

# 添加表所有权设置
print("   ‣ 设置表所有权...")
with engine.begin() as conn:
    conn.execute(text(f"ALTER TABLE {ROUTE_TB} OWNER TO root;"))
    conn.execute(text(f"ALTER TABLE {GRID_TB} OWNER TO root;"))
    conn.execute(text(f"ALTER TABLE {LUT_TB} OWNER TO root;"))
    conn.execute(text(f"ALTER TABLE {LUT_LINE_TB} OWNER TO root;"))

# ---------- 5. 验证和统计 ----------
print("\n5) 验证数据和统计信息...")
with engine.begin() as conn:
    # 统计路线表记录数
    route_count = conn.execute(text(f"SELECT COUNT(*) FROM {ROUTE_TB}")).scalar()
    print(f"   ‣ 路线表记录数: {route_count}")

    # 统计格网表记录数
    grid_count = conn.execute(text(f"SELECT COUNT(*) FROM {GRID_TB}")).scalar()
    print(f"   ‣ 格网表记录数: {grid_count}")

    # 统计LUT表记录数
    lut_count = conn.execute(text(f"SELECT COUNT(*) FROM {LUT_TB}")).scalar()
    print(f"   ‣ LUT表记录数: {lut_count}")

    # 统计LUT线性表记录数
    lut_line_count = conn.execute(text(f"SELECT COUNT(*) FROM {LUT_LINE_TB}")).scalar()
    print(f"   ‣ LUT线性表记录数: {lut_line_count}")

    # 统计有部门ID的记录数
    dept_mapped_count = conn.execute(text(f"SELECT COUNT(*) FROM {ROUTE_TB} WHERE management_office_id IS NOT NULL")).scalar()
    print(f"   ‣ 有部门ID的路线记录数: {dept_mapped_count}/{route_count}")

    # 统计有养护路段ID的记录数
    maint_mapped_count = conn.execute(text(f"SELECT COUNT(*) FROM {ROUTE_TB} WHERE maintenance_section_id IS NOT NULL")).scalar()
    print(f"   ‣ 有养护路段ID的路线记录数: {maint_mapped_count}/{route_count}")

    # 统计有所有者类型的记录数
    owner_mapped_count = conn.execute(text(f"SELECT COUNT(*) FROM {ROUTE_TB} WHERE owner_type IS NOT NULL")).scalar()
    print(f"   ‣ 有所有者类型的路线记录数: {owner_mapped_count}/{route_count}")

    # 统计有行政区划代码的记录数
    region_code_count = conn.execute(text(f"SELECT COUNT(*) FROM {ROUTE_TB} WHERE region_code IS NOT NULL")).scalar()
    print(f"   ‣ 有行政区划代码的路线记录数: {region_code_count}/{route_count}")

    # 统计有行政区划名称的记录数
    region_name_count = conn.execute(text(f"SELECT COUNT(*) FROM {ROUTE_TB} WHERE region_name IS NOT NULL")).scalar()
    print(f"   ‣ 有行政区划名称的路线记录数: {region_name_count}/{route_count}")

# ---------- 6. 收尾 ----------
print("\n★ 天气数据路线预处理完成 ★")
print(f"   - 天气路线表: {ROUTE_TB} (包含部门ID、养护路段ID和所有者类型)")
print(f"   - 天气格网表: {GRID_TB}")
print(f"   - 天气LUT表 : {LUT_TB} (包含部门ID、养护路段ID和所有者类型，使用缓冲)")
print(f"   - 天气LUT线性表: {LUT_LINE_TB} (线通过，无缓冲，实际受影响段)")
print("\n示例查询：")
print(f"-- 查询指定格网的路线信息（包含路线编码、名称、行政区划等）")
print(f"SELECT route_id, pk_from, pk_to, route_code, route_name,")
print(f"       management_office, maintenance_section,")
print(f"       management_office_id, maintenance_section_id, owner_type,")
print(f"       region_code, region_name")
print(f"FROM {LUT_TB} WHERE cell_id = 12345;")
print(f"\n-- 查询指定格网实际通过的路线段（无缓冲）")
print(f"SELECT route_id, pk_from, pk_to, route_code, route_name,")
print(f"       management_office, maintenance_section, seg_len_m,")
print(f"       management_office_id, maintenance_section_id, owner_type,")
print(f"       region_code, region_name")
print(f"FROM {LUT_LINE_TB} WHERE cell_id = 12345;")
print(f"\n-- 批量查询多个格网（推荐用法）")
print(f"SELECT cell_id, route_id, pk_from, pk_to, route_code, route_name, region_name")
print(f"FROM {LUT_TB} WHERE cell_id = ANY(ARRAY[12345, 12346, 12347]);")
print(f"\n-- 查询指定部门的路线")
print(f"SELECT DISTINCT route_id, route_code, route_name, management_office, region_name")
print(f"FROM {LUT_TB} WHERE management_office_id = 'your_dept_id';")
print(f"\n-- 查询指定所有者类型的路线")
print(f"SELECT DISTINCT route_id, route_code, route_name, management_office, region_name")
print(f"FROM {LUT_TB} WHERE owner_type = 'your_owner_type';")
print(f"\n-- 查询指定行政区划的路线")
print(f"SELECT DISTINCT route_id, route_code, route_name, region_code, region_name")
print(f"FROM {LUT_TB} WHERE region_code = '530102';")
print(f"\n-- 查询指定行政区划名称的路线")
print(f"SELECT DISTINCT route_id, route_code, route_name, region_code, region_name")
print(f"FROM {LUT_TB} WHERE region_name LIKE '%昆明%';")

# 注意：由于不再使用临时文件，无需清理临时目录
print("\n   ‣ 数据处理完成，无需清理临时文件")
