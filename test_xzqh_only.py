#!/usr/bin/env python3
"""
仅测试行政区划代码标准化函数
"""

import sys
import os
sys.path.append('src')

# 直接复制normalize_xzqh_code函数进行测试
def normalize_xzqh_code(xzqh_str, target_digits=6):
    """
    标准化行政区划代码
    先根据target_digits截取前N位，然后根据去除末尾0后的实际位数，自动判断应该属于哪个行政层级，然后补充到对应的标准位数
    """
    import pandas as pd
    
    if not xzqh_str or pd.isna(xzqh_str):
        return None

    # 转换为字符串并去除空格
    code_str = str(xzqh_str).strip()
    if not code_str:
        return None

    # 预处理：先截取前target_digits位
    if len(code_str) > target_digits:
        code_str = code_str[:target_digits]

    # 去除末尾的0，但要小心不要把有效的10、20等去掉
    code_str = code_str.rstrip('0')

    # 如果全部都是0或者为空，返回None
    if not code_str:
        return None

    # 根据当前长度确定应该补充到哪个标准长度
    current_len = len(code_str)
    if current_len <= 2:
        target_len = 2  # 省级
    elif current_len <= 4:
        target_len = 4  # 市级
    elif current_len <= 6:
        target_len = 6  # 县级
    elif current_len <= 9:
        target_len = 9  # 乡镇级
    elif current_len <= 12:
        target_len = 12 # 村级
    else:
        # 超过12位，截取到12位
        target_len = 12

    # 补充0到目标位数
    if current_len < target_len:
        code_str = code_str + '0' * (target_len - current_len)
    elif current_len > target_len:
        # 如果超过目标位数，截取到目标位数
        code_str = code_str[:target_len]

    return code_str

# 测试几个关键用例
tests = [
    ('532622000000', '532622'),  # 12位截取到6位，去0后保持6位
    ('530722206000', '530722'),  # 12位截取到6位，去0后保持6位
    ('530102000', '530102'),     # 9位截取到6位，去0后保持6位
    ('530102001', '530102'),     # 9位截取到6位，去0后保持6位
    ('530000', '53'),            # 6位去0后变2位省级
    ('53', '53'),                # 2位保持2位
    ('5300', '53'),              # 4位去0后变2位省级
    ('5301', '5301'),            # 4位市级保持4位
]

print("=== 测试行政区划代码标准化（修改后的逻辑）===")
for inp, exp in tests:
    result = normalize_xzqh_code(inp)
    status = "✓" if result == exp else "✗"
    print(f"{status} {inp} -> {result} (期望: {exp})")
    if result != exp:
        print(f"    实际结果与期望不符")
