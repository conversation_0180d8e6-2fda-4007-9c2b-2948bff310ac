#!/usr/bin/env python3
# coding: utf-8
"""
测试分区表创建脚本
验证按照分区改造指导原则改造后的表结构是否正确
"""

from sqlalchemy import create_engine, text
from src.config import PG_URL

def test_partition_tables():
    """
    测试分区表的创建和基本功能
    """
    engine = create_engine(PG_URL)
    
    print("开始测试分区表创建...")
    
    # 测试表列表
    test_tables = [
        # 方案A: weather_cell_* 系列表
        {
            'name': 'weather_cell_6m',
            'partition_key': 'pre_time',
            'primary_key': '(pre_time, cell_id)',
            'type': 'weather_cell'
        },
        {
            'name': 'weather_cell_1h', 
            'partition_key': 'pre_time',
            'primary_key': '(pre_time, cell_id)',
            'type': 'weather_cell'
        },
        
        # 方案B: forecast_precipitation_* 系列表
        {
            'name': 'forecast_precipitation_6min_line',
            'partition_key': 'pre_time',
            'primary_key': '(pre_time, id)',
            'type': 'forecast'
        },
        {
            'name': 'forecast_precipitation_6min_polygon',
            'partition_key': 'pre_time', 
            'primary_key': '(pre_time, id)',
            'type': 'forecast'
        },
        {
            'name': 'forecast_precipitation_6min_relation',
            'partition_key': 'pre_time',
            'primary_key': '(pre_time, id)', 
            'type': 'forecast'
        },
        {
            'name': 'forecast_precipitation_hourly_line',
            'partition_key': 'pre_time',
            'primary_key': '(pre_time, id)',
            'type': 'forecast'
        },
        {
            'name': 'forecast_precipitation_hourly_polygon',
            'partition_key': 'pre_time',
            'primary_key': '(pre_time, id)',
            'type': 'forecast'
        },
        {
            'name': 'forecast_precipitation_hourly_relation',
            'partition_key': 'pre_time',
            'primary_key': '(pre_time, id)',
            'type': 'forecast'
        },
        {
            'name': 'forecast_precipitation_summary_line',
            'partition_key': 'pre_time',
            'primary_key': '(pre_time, id)',
            'type': 'forecast'
        },
        {
            'name': 'forecast_precipitation_summary_polygon',
            'partition_key': 'pre_time',
            'primary_key': '(pre_time, id)',
            'type': 'forecast'
        },
        {
            'name': 'forecast_precipitation_summary_relation',
            'partition_key': 'pre_time',
            'primary_key': '(pre_time, id)',
            'type': 'forecast'
        },
        
        # 方案C: weather_alarm 表
        {
            'name': 'weather_alarm',
            'partition_key': 'publish_time',
            'primary_key': '(publish_time, id)',
            'type': 'alarm'
        }
    ]
    
    try:
        with engine.begin() as conn:
            for table in test_tables:
                table_name = table['name']
                print(f"\n检查表: {table_name}")
                
                # 1. 检查表是否为分区表
                partition_check_sql = """
                SELECT
                    n.nspname as schemaname,
                    c.relname as tablename,
                    'RANGE' as partitiontype,
                    pg_get_partkeydef(c.oid) as partitionkey
                FROM pg_class c
                JOIN pg_namespace n ON n.oid = c.relnamespace
                WHERE n.nspname = 'public'
                  AND c.relname = :table_name
                  AND c.relkind = 'p'
                """
                
                result = conn.execute(text(partition_check_sql), {"table_name": table_name}).fetchall()
                
                if result:
                    print(f"  ✓ {table_name} 是分区表")
                    for row in result:
                        print(f"    分区类型: {row[2]}")
                        print(f"    分区键: {row[3]}")
                else:
                    print(f"  ⚠️ {table_name} 不是分区表或不存在")
                
                # 2. 检查主键约束
                pk_check_sql = """
                SELECT 
                    constraint_name,
                    string_agg(column_name, ', ' ORDER BY ordinal_position) as columns
                FROM information_schema.key_column_usage
                WHERE table_schema = 'public' 
                  AND table_name = :table_name
                  AND constraint_name LIKE '%_pkey'
                GROUP BY constraint_name
                """
                
                pk_result = conn.execute(text(pk_check_sql), {"table_name": table_name}).fetchall()
                
                if pk_result:
                    for row in pk_result:
                        print(f"  ✓ 主键约束: {row[0]}")
                        print(f"    主键列: ({row[1]})")
                        
                        # 验证主键是否包含分区键
                        if table['partition_key'] in row[1]:
                            print(f"    ✓ 主键包含分区键 {table['partition_key']}")
                        else:
                            print(f"    ⚠️ 主键不包含分区键 {table['partition_key']}")
                else:
                    print(f"  ⚠️ {table_name} 没有主键约束")
                
                # 3. 检查分区键列是否为NOT NULL
                null_check_sql = """
                SELECT column_name, is_nullable
                FROM information_schema.columns
                WHERE table_schema = 'public' 
                  AND table_name = :table_name
                  AND column_name = :partition_key
                """
                
                null_result = conn.execute(text(null_check_sql), {
                    "table_name": table_name,
                    "partition_key": table['partition_key']
                }).fetchall()
                
                if null_result:
                    for row in null_result:
                        if row[1] == 'NO':
                            print(f"    ✓ 分区键 {row[0]} 设置为 NOT NULL")
                        else:
                            print(f"    ⚠️ 分区键 {row[0]} 允许 NULL 值")
                
                # 4. 对于 weather_alarm 表，检查唯一索引
                if table_name == 'weather_alarm':
                    unique_idx_sql = """
                    SELECT 
                        indexname,
                        indexdef
                    FROM pg_indexes
                    WHERE schemaname = 'public' 
                      AND tablename = :table_name
                      AND indexdef LIKE '%UNIQUE%'
                    """
                    
                    unique_result = conn.execute(text(unique_idx_sql), {"table_name": table_name}).fetchall()
                    
                    if unique_result:
                        for row in unique_result:
                            print(f"    ✓ 唯一索引: {row[0]}")
                            if 'publish_time' in row[1]:
                                print(f"      ✓ 唯一索引包含分区键 publish_time")
                            else:
                                print(f"      ⚠️ 唯一索引不包含分区键 publish_time")
                    else:
                        print(f"    ⚠️ {table_name} 没有唯一索引")
        
        print(f"\n✓ 分区表测试完成")
        
    except Exception as e:
        print(f"测试过程中发生错误: {e}")
        raise
    finally:
        engine.dispose()

if __name__ == '__main__':
    print("开始测试分区表改造结果...")
    test_partition_tables()
