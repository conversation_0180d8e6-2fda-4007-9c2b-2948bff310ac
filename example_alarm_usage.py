#!/usr/bin/env python3
# coding: utf-8
"""
预警内容解析功能使用示例
"""

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

import asyncio
from src.weather_alarm import WeatherAlarmProcessor

async def example_usage():
    """演示预警内容解析功能的使用"""
    
    processor = WeatherAlarmProcessor()
    
    try:
        # 初始化处理器
        await processor.initialize()
        
        print("预警内容解析功能演示")
        print("=" * 50)
        
        # 示例预警内容
        example_contents = [
            "墨江县气象台2025年7月18日07时05分发布大雾黄色预警信号：未来12小时墨江县大部乡镇将出现浓雾，请做好防护。",
            "会泽县气象台2025年7月17日16时25分发布高温黄色预警信号：未来3天娜姑镇、纸厂乡的低矮河谷地区最高气温将在35℃以上，请注意防范。",
            "彝良县气象台2025年7月18日9时00分继续发布高温橙色预警信号：未来24小时，彝良县洛旺、柳溪、牛街、角奎、发界、海子、洛泽河、两河等乡镇（街道）海拔1200米以下低矮河谷区域日最高气温将升至37℃以上，午后请减少户外活动。",
            "绥江县气象台2025年7月18日08时34分发布高温橙色预警信号：未来24小时，我县南岸镇、板栗镇、中城镇、新滩镇、会仪镇最高气温将升至37℃以上，午后请减少户外活动。"
        ]
        
        # 假设的geo_code（实际使用时应该从数据库获取）
        example_geo_codes = ["530800", "530300", "530600", "530600"]
        
        # 批量获取区域数据
        region_data = processor.content_parser.get_region_data_by_geo_codes(example_geo_codes)
        
        for i, (content, geo_code) in enumerate(zip(example_contents, example_geo_codes), 1):
            print(f"\n示例 {i}:")
            print("-" * 40)
            print(f"预警内容: {content[:80]}...")
            print(f"区域代码: {geo_code}")
            
            # 解析预警内容
            results = processor.content_parser.parse_alarm_content(content, geo_code, region_data)
            
            print(f"解析结果: 匹配到 {len(results)} 个子区域")
            for j, result in enumerate(results, 1):
                print(f"  {j}. {result['data_name']} (代码: {result['data_code']})")
        
        print("\n" + "=" * 50)
        print("演示完成")
        
    except Exception as e:
        print(f"演示失败: {e}")
        import traceback
        traceback.print_exc()
    finally:
        await processor.close()

if __name__ == "__main__":
    asyncio.run(example_usage())
