#!/usr/bin/env python3
# coding: utf-8
"""
测试时区修复
验证在Windows服务环境中时间是否正确
"""

import sys
from pathlib import Path
import os

# 添加src目录到Python路径
src_path = Path(__file__).parent / "src"
sys.path.insert(0, str(src_path))

from datetime import datetime
from time_utils import get_local_now, check_timezone_setting, format_time_for_log, get_uptime_format

def test_timezone_fix():
    """测试时区修复效果"""
    print("=" * 60)
    print("时区修复测试")
    print("=" * 60)
    
    # 1. 显示环境变量
    print(f"TZ环境变量: {os.environ.get('TZ', '未设置')}")
    
    # 2. 比较系统时间和中国时区时间
    system_time = datetime.now()
    china_time = get_local_now()
    
    print(f"系统时间 (datetime.now()): {system_time.strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"中国时区时间 (get_local_now()): {china_time.strftime('%Y-%m-%d %H:%M:%S')}")
    
    # 3. 计算时差
    time_diff = (china_time - system_time).total_seconds()
    print(f"时间差: {time_diff:.1f} 秒")
    
    # 4. 检查时区设置
    print("\n时区检查结果:")
    timezone_check = check_timezone_setting()
    for key, value in timezone_check.items():
        print(f"  {key}: {value}")
    
    # 5. 测试格式化函数
    print(f"\n格式化时间 (日志用): {format_time_for_log()}")
    print(f"uptime格式: {get_uptime_format()}")
    
    # 6. 判断修复是否成功
    print("\n" + "=" * 60)
    if timezone_check['timezone_correct']:
        print("✅ 时区设置正确！")
        print("在Windows服务环境中应该能正确显示中国时区时间。")
    else:
        print("❌ 时区设置有问题！")
        print("建议检查：")
        print("1. 确保service.xml中设置了 <env name=\"TZ\" value=\"Asia/Shanghai\"/>")
        print("2. 确保所有代码都使用 get_local_now() 而不是 datetime.now()")
        print("3. 重启Windows服务")
    
    print("=" * 60)

if __name__ == "__main__":
    test_timezone_fix()
