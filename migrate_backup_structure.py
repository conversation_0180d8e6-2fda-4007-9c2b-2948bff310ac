#!/usr/bin/env python3
# coding: utf-8
"""
备份文件迁移脚本
将旧格式的备份文件迁移到新的年/月/日目录结构中
"""

import os
import sys
import shutil
from pathlib import Path
from datetime import datetime
import re

# 添加src目录到Python路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from weather_download import WeatherDownloader
import logging

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


def extract_date_from_filename(filename: str) -> datetime:
    """
    从文件名中提取日期信息
    
    Args:
        filename: 文件名
        
    Returns:
        datetime: 提取的日期，如果无法提取则返回文件修改时间对应的日期
    """
    # 尝试从文件名中提取日期 (格式如: YYYYMMDDHHMM)
    date_patterns = [
        r'(\d{12})',  # YYYYMMDDHHMM
        r'(\d{10})',  # YYYYMMDDHH
        r'(\d{8})',   # YYYYMMDD
    ]
    
    for pattern in date_patterns:
        match = re.search(pattern, filename)
        if match:
            date_str = match.group(1)
            try:
                if len(date_str) == 12:  # YYYYMMDDHHMM
                    return datetime.strptime(date_str, '%Y%m%d%H%M')
                elif len(date_str) == 10:  # YYYYMMDDHH
                    return datetime.strptime(date_str, '%Y%m%d%H')
                elif len(date_str) == 8:   # YYYYMMDD
                    return datetime.strptime(date_str, '%Y%m%d')
            except ValueError:
                continue
    
    return None


def migrate_backup_files(data_type: str, dry_run: bool = True) -> dict:
    """
    迁移指定数据类型的备份文件
    
    Args:
        data_type: 数据类型
        dry_run: 是否为试运行（不实际移动文件）
        
    Returns:
        dict: 迁移结果统计
    """
    try:
        downloader = WeatherDownloader(data_type)
        backup_dir = downloader.backup_dir
        
        if not backup_dir.exists():
            logger.warning(f"[{data_type}] 备份目录不存在: {backup_dir}")
            return {"total": 0, "migrated": 0, "failed": 0, "skipped": 0}
        
        # 查找旧格式的备份文件（直接在backup目录下的.nc文件）
        old_backup_files = [f for f in backup_dir.glob("*.nc") if f.is_file()]
        
        if not old_backup_files:
            logger.info(f"[{data_type}] 没有找到需要迁移的旧格式备份文件")
            return {"total": 0, "migrated": 0, "failed": 0, "skipped": 0}
        
        logger.info(f"[{data_type}] 找到 {len(old_backup_files)} 个旧格式备份文件")
        
        stats = {"total": len(old_backup_files), "migrated": 0, "failed": 0, "skipped": 0}
        
        for old_file in old_backup_files:
            try:
                # 尝试从文件名提取日期
                file_date = extract_date_from_filename(old_file.name)
                
                # 如果无法从文件名提取日期，使用文件修改时间
                if file_date is None:
                    file_date = datetime.fromtimestamp(old_file.stat().st_mtime)
                    logger.info(f"[{data_type}] 使用文件修改时间作为日期: {old_file.name} -> {file_date.strftime('%Y-%m-%d')}")
                
                # 创建新的目录结构
                year = file_date.strftime("%Y")
                month = file_date.strftime("%m")
                day = file_date.strftime("%d")
                
                new_dir = backup_dir / year / month / day
                new_file_path = new_dir / old_file.name
                
                # 检查目标文件是否已存在
                if new_file_path.exists():
                    logger.warning(f"[{data_type}] 目标文件已存在，跳过: {new_file_path}")
                    stats["skipped"] += 1
                    continue
                
                if dry_run:
                    logger.info(f"[{data_type}] [试运行] 将移动: {old_file} -> {new_file_path}")
                else:
                    # 创建目标目录
                    new_dir.mkdir(parents=True, exist_ok=True)
                    
                    # 移动文件
                    shutil.move(str(old_file), str(new_file_path))
                    logger.info(f"[{data_type}] 已移动: {old_file.name} -> {new_file_path}")
                
                stats["migrated"] += 1
                
            except Exception as e:
                logger.error(f"[{data_type}] 迁移文件失败 {old_file}: {e}")
                stats["failed"] += 1
        
        return stats
        
    except Exception as e:
        logger.error(f"[{data_type}] 迁移过程中发生错误: {e}")
        return {"total": 0, "migrated": 0, "failed": 0, "skipped": 0}


def migrate_all_backup_files(dry_run: bool = True):
    """
    迁移所有数据类型的备份文件
    
    Args:
        dry_run: 是否为试运行
    """
    data_types = [
        "gz_mpfv3", 
        "gz_didiforecast1hTEM", 
        "gz_didiforecast1hPRE",
        "gz_didiforecast1hWEATHER", 
        "gz_didiforecast1hVIS"
    ]
    
    total_stats = {"total": 0, "migrated": 0, "failed": 0, "skipped": 0}
    
    print("=" * 80)
    print(f"备份文件迁移{'（试运行）' if dry_run else ''}开始")
    print("=" * 80)
    
    for data_type in data_types:
        print(f"\n处理数据类型: {data_type}")
        print("-" * 40)
        
        stats = migrate_backup_files(data_type, dry_run)
        
        # 累计统计
        for key in total_stats:
            total_stats[key] += stats[key]
        
        print(f"  总文件数: {stats['total']}")
        print(f"  {'计划迁移' if dry_run else '已迁移'}: {stats['migrated']}")
        print(f"  失败: {stats['failed']}")
        print(f"  跳过: {stats['skipped']}")
    
    print("\n" + "=" * 80)
    print("迁移汇总")
    print("=" * 80)
    print(f"总文件数: {total_stats['total']}")
    print(f"{'计划迁移' if dry_run else '已迁移'}: {total_stats['migrated']}")
    print(f"失败: {total_stats['failed']}")
    print(f"跳过: {total_stats['skipped']}")
    
    if dry_run:
        print("\n注意: 这是试运行，没有实际移动文件。")
        print("如果结果看起来正确，请使用 --execute 参数执行实际迁移。")
    else:
        print("\n迁移完成！")
        print("新的备份文件将使用年/月/日的目录结构。")


def show_migration_preview():
    """显示迁移预览"""
    print("=" * 80)
    print("迁移预览")
    print("=" * 80)
    
    print("\n迁移前后对比:")
    print("迁移前 (旧格式):")
    print("  backup/")
    print("  ├── weather_data_20250115142530.nc")
    print("  ├── weather_data_20250116083000.nc")
    print("  ├── weather_data_20250117120000.nc")
    print("  └── ...")
    
    print("\n迁移后 (新格式):")
    print("  backup/")
    print("  ├── 2025/")
    print("  │   └── 01/")
    print("  │       ├── 15/")
    print("  │       │   └── weather_data_20250115142530.nc")
    print("  │       ├── 16/")
    print("  │       │   └── weather_data_20250116083000.nc")
    print("  │       └── 17/")
    print("  │           └── weather_data_20250117120000.nc")
    print("  └── ...")
    
    print("\n迁移规则:")
    print("1. 尝试从文件名中提取日期信息")
    print("2. 如果无法提取，使用文件的修改时间")
    print("3. 按提取的日期创建年/月/日目录结构")
    print("4. 将文件移动到对应的日期目录中")
    print("5. 如果目标文件已存在，则跳过该文件")


if __name__ == "__main__":
    import argparse
    
    parser = argparse.ArgumentParser(description="备份文件迁移脚本")
    parser.add_argument("--execute", action="store_true", 
                       help="执行实际迁移（默认为试运行）")
    parser.add_argument("--preview", action="store_true",
                       help="显示迁移预览")
    
    args = parser.parse_args()
    
    if args.preview:
        show_migration_preview()
    else:
        dry_run = not args.execute
        migrate_all_backup_files(dry_run)
        
        if dry_run:
            print("\n要执行实际迁移，请运行:")
            print("uv run python migrate_backup_structure.py --execute")
