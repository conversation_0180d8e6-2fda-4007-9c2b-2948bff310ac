# 配置文件合并迁移指南

## 概述

本文档说明了如何将调度器配置从独立的 `scheduler_config.yml` 文件合并到主配置文件 `config.yml` 中，实现统一配置管理。

## 迁移内容

### 原配置结构

**之前**：两个独立的配置文件
- `config.yml` - 主配置（数据库、下载、处理等）
- `scheduler_config.yml` - 调度器专用配置

**现在**：统一配置文件
- `config.yml` - 包含所有模块的配置

### 合并的配置项

以下配置项已从 `scheduler_config.yml` 合并到 `config.yml` 的 `scheduler` 节点下：

```yaml
# 新增的调度器配置节点
scheduler:
  # 基本调度配置
  delay_minutes: 4
  
  # 数据新鲜度检查配置
  uptime_check:
    max_uptime_minutes: 5
    retry_interval_seconds: 30
    max_retry_duration_minutes: 60

  # 任务配置
  tasks:
    hourly:
      data_types: ["PRE", "TEM", "WEATHER", "VIS"]
      timeout_minutes: 30
      schedule_pattern: "0 4 * * *"
    six_minute:
      data_type: "gz_mpfv3"
      timeout_minutes: 10
      schedule_pattern: "4,10,16,22,28,34,40,46,52,58 * * * *"

  # 资源池配置
  resources:
    database:
      max_connections: 20
      min_connections: 5
    thread_pool:
      max_workers: 32
    process_pool:
      max_workers: null

  # 监控配置
  monitoring:
    log_level: "INFO"
    log_file: "weather_scheduler.log"
    health_check_interval: 30

  # Web服务配置
  web:
    host: "0.0.0.0"
    port: 8000
    reload: false
```

## 代码更新

### 配置模块更新 (`src/config.py`)

新增了以下配置常量和函数：

```python
# 调度器配置常量
SCHEDULER_DELAY_MINUTES
SCHEDULER_MAX_UPTIME_MINUTES
SCHEDULER_RETRY_INTERVAL_SECONDS
SCHEDULER_MAX_RETRY_DURATION_MINUTES
SCHEDULER_HOURLY_DATA_TYPES
SCHEDULER_SIX_MINUTE_DATA_TYPE
SCHEDULER_HOURLY_TIMEOUT_MINUTES
SCHEDULER_SIX_MINUTE_TIMEOUT_MINUTES
SCHEDULER_DB_MAX_CONNECTIONS
SCHEDULER_DB_MIN_CONNECTIONS
SCHEDULER_THREAD_MAX_WORKERS
SCHEDULER_PROCESS_MAX_WORKERS
SCHEDULER_LOG_LEVEL
SCHEDULER_LOG_FILE
SCHEDULER_HEALTH_CHECK_INTERVAL
SCHEDULER_WEB_HOST
SCHEDULER_WEB_PORT
SCHEDULER_WEB_RELOAD

# 配置获取函数
def get_scheduler_config():
    """获取调度器完整配置"""
```

### 模块更新

以下模块已更新以使用统一配置：

1. **`src/resource_manager.py`**
   - 使用 `SCHEDULER_DB_MAX_CONNECTIONS` 等配置
   - 支持可配置的资源池大小

2. **`src/uptime_checker.py`**
   - 使用 `SCHEDULER_MAX_UPTIME_MINUTES` 等配置
   - 支持可配置的重试参数

3. **`src/scheduler.py`**
   - 使用 `SCHEDULER_HOURLY_DATA_TYPES` 等配置
   - 支持可配置的任务类型和超时时间

4. **`src/weather_scheduler_app.py`**
   - 使用 `SCHEDULER_WEB_HOST` 等配置
   - 支持可配置的Web服务参数

## 验证工具

### 配置验证脚本 (`validate_config.py`)

新增了配置验证脚本，用于验证所有配置项的正确性：

```bash
# 运行配置验证
uv run python validate_config.py
```

验证内容包括：
- 数据库连接配置
- 天气数据下载配置
- 调度器配置
- 天气数据处理配置

### 测试脚本更新

现有测试脚本已更新以验证新的配置结构：
- `test_scheduler.py` - 调度器系统测试
- `demo_scheduler.py` - 调度器功能演示

## 迁移步骤

如果您有自定义的 `scheduler_config.yml` 文件，请按以下步骤迁移：

### 1. 备份现有配置

```bash
cp config.yml config.yml.backup
cp scheduler_config.yml scheduler_config.yml.backup  # 如果存在
```

### 2. 合并配置

将您的 `scheduler_config.yml` 内容合并到 `config.yml` 的 `scheduler` 节点下。

### 3. 验证配置

```bash
uv run python validate_config.py
```

### 4. 测试系统

```bash
uv run python test_scheduler.py
uv run python demo_scheduler.py
```

### 5. 删除旧配置文件

确认系统正常工作后，可以删除 `scheduler_config.yml`：

```bash
rm scheduler_config.yml  # 可选
```

## 配置优势

### 统一管理
- 所有配置集中在一个文件中
- 减少配置文件数量
- 便于维护和部署

### 类型安全
- 配置项有明确的类型定义
- 支持配置验证
- 减少配置错误

### 灵活配置
- 支持环境变量覆盖
- 支持默认值
- 支持可选配置项

## 默认配置

如果您不需要自定义调度器配置，系统将使用以下默认值：

```yaml
scheduler:
  delay_minutes: 4
  uptime_check:
    max_uptime_minutes: 5
    retry_interval_seconds: 30
    max_retry_duration_minutes: 60
  tasks:
    hourly:
      data_types: ["PRE", "TEM", "WEATHER", "VIS"]
      timeout_minutes: 30
    six_minute:
      data_type: "gz_mpfv3"
      timeout_minutes: 10
  resources:
    database:
      max_connections: 20
      min_connections: 5
    thread_pool:
      max_workers: 32
    process_pool:
      max_workers: null
  monitoring:
    log_level: "INFO"
    log_file: "weather_scheduler.log"
    health_check_interval: 30
  web:
    host: "0.0.0.0"
    port: 8000
    reload: false
```

## 故障排除

### 配置验证失败

如果 `validate_config.py` 报告错误：

1. 检查 `config.yml` 语法是否正确
2. 确认所有必需的配置项都存在
3. 验证配置值的类型和范围
4. 检查数据库连接参数

### 调度器启动失败

如果调度器无法启动：

1. 运行配置验证：`uv run python validate_config.py`
2. 检查日志文件：`tail -f weather_scheduler.log`
3. 验证网络连接和数据库访问
4. 确认端口未被占用

## 相关文档

- [快速启动指南](QUICKSTART.md)
- [调度器使用指南](docs/SCHEDULER_GUIDE.md)
- [项目README](README.md)
