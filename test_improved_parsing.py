#!/usr/bin/env python3
# coding: utf-8
"""
测试改进后的预警内容解析功能
"""

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

import pymysql
from src.weather_alarm import AlarmContentParser
from src.config import MYSQL_CONFIG

def test_improved_parsing():
    """测试改进后的解析功能"""
    
    try:
        # 连接MySQL数据库
        mysql_conn = pymysql.connect(**MYSQL_CONFIG, charset='utf8mb4')
        
        print("=" * 80)
        print("测试改进后的预警内容解析功能")
        print("=" * 80)
        
        # 创建解析器
        parser = AlarmContentParser(mysql_conn)
        
        # 测试用例 - 使用您提供的真实预警内容
        test_cases = [
            {
                "content": "墨江县气象台2025年7月18日07时05分发布大雾黄色预警信号：未来12小时墨江县大部乡镇将出现浓雾，请做好防护。（预警信息来源：国家预警信息发布中心）",
                "description": "墨江县大雾预警",
                "expected_admin": "墨江县"
            },
            {
                "content": "会泽县气象台2025年7月17日16时25分发布高温黄色预警信号：未来3天娜姑镇、纸厂乡的低矮河谷地区最高气温将在35℃以上，请注意防范。（预警信息来源：国家预警信息发布中心）",
                "description": "会泽县高温预警",
                "expected_admin": "会泽县"
            },
            {
                "content": "彝良县气象台2025年7月18日9时00分继续发布高温橙色预警信号：未来24小时，彝良县洛旺、柳溪、牛街、角奎、发界、海子、洛泽河、两河等乡镇（街道）海拔1200米以下低矮河谷区域日最高气温将升至37℃以上，午后请减少户外活动。（预警信息来源：国家预警信息发布中心）",
                "description": "彝良县高温预警",
                "expected_admin": "彝良县"
            },
            {
                "content": "绥江县气象台2025年7月18日08时34分发布高温橙色预警信号：未来24小时，我县南岸镇、板栗镇、中城镇、新滩镇、会仪镇最高气温将升至37℃以上，午后请减少户外活动。（预警信息来源：国家预警信息发布中心）",
                "description": "绥江县高温预警",
                "expected_admin": "绥江县"
            }
        ]
        
        for i, test_case in enumerate(test_cases, 1):
            print(f"\n🧪 测试用例 {i}: {test_case['description']}")
            print("-" * 60)
            
            content = test_case["content"]
            expected_admin = test_case["expected_admin"]
            
            print(f"预警内容: {content[:100]}...")
            
            # 1. 测试行政区名称提取
            admin_region = parser.extract_admin_region_name(content)
            print(f"提取的行政区: '{admin_region}'")
            print(f"行政区提取: {'✅' if admin_region == expected_admin else '❌'}")
            
            # 2. 测试geo_code查找
            if admin_region:
                geo_code = parser.find_geo_code_by_admin_name(admin_region)
                print(f"找到的geo_code: {geo_code}")
                
                if geo_code:
                    # 3. 获取区域数据
                    region_data = parser.get_region_data_by_geo_codes([geo_code])
                    
                    if geo_code in region_data:
                        sub_regions = region_data[geo_code]
                        print(f"子区域数量: {len(sub_regions)}")
                        
                        # 显示前5个子区域
                        print("子区域示例:")
                        for j, region in enumerate(sub_regions[:5]):
                            print(f"  {j+1}. {region['data_name']} ({region['data_code']})")
                        if len(sub_regions) > 5:
                            print(f"  ... 还有{len(sub_regions)-5}个")
                        
                        # 4. 完整解析测试
                        print("\n完整解析测试:")
                        results = parser.parse_alarm_content(content, "dummy_code", region_data)
                        
                        print(f"解析结果: {len(results)}个匹配")
                        for j, result in enumerate(results, 1):
                            print(f"  ✅ {j}. {result['data_name']} ({result['data_code']})")
                        
                        if not results:
                            print("  ❌ 未找到匹配的子区域")
                            
                            # 调试信息
                            print("\n  调试信息:")
                            content_after_colon = parser.extract_content_after_colon(content)
                            print(f"    冒号后内容: {content_after_colon[:80]}...")
                            
                            location_text = parser.extract_time_and_location(content_after_colon)
                            print(f"    提取的地点: '{location_text}'")
                            
                            cleaned_location = parser.clean_location_prefix(location_text, admin_region)
                            print(f"    清理后地点: '{cleaned_location}'")
                            
                            locations = parser.split_locations(cleaned_location)
                            print(f"    分割后地点: {locations}")
                    else:
                        print("❌ 未获取到区域数据")
                else:
                    print("❌ 未找到对应的geo_code")
            else:
                print("❌ 未提取到行政区名称")
        
        # 5. 测试一些已知存在的地名
        print(f"\n🔍 测试已知地名匹配:")
        print("-" * 60)
        
        # 先找一个有数据的geo_code
        with mysql_conn.cursor() as cursor:
            cursor.execute("""
                SELECT sup_code, COUNT(*) as sub_count
                FROM sys_region_code 
                WHERE sup_code IS NOT NULL 
                AND sup_code != '%'
                AND sup_code != ''
                GROUP BY sup_code
                HAVING sub_count BETWEEN 5 AND 15
                ORDER BY sub_count DESC
                LIMIT 1
            """)
            
            result = cursor.fetchone()
            if result:
                test_geo_code, sub_count = result
                print(f"使用测试geo_code: {test_geo_code} ({sub_count}个子区域)")
                
                # 获取该geo_code的子区域
                cursor.execute("""
                    SELECT data_name, data_code
                    FROM sys_region_code 
                    WHERE sup_code = %s
                    ORDER BY data_name
                    LIMIT 3
                """, (test_geo_code,))
                
                test_regions = cursor.fetchall()
                if len(test_regions) >= 2:
                    region1_name = test_regions[0][0]
                    region2_name = test_regions[1][0]
                    
                    # 构造测试内容
                    test_content = f"测试县气象台发布预警信号：未来24小时{region1_name}、{region2_name}将出现恶劣天气，请注意防范。"
                    
                    print(f"构造的测试内容: {test_content}")
                    
                    # 获取区域数据并解析
                    region_data = parser.get_region_data_by_geo_codes([test_geo_code])
                    results = parser.parse_alarm_content(test_content, test_geo_code, region_data)
                    
                    print(f"解析结果: {len(results)}个匹配")
                    for result in results:
                        print(f"  ✅ {result['data_name']} ({result['data_code']})")
                    
                    # 验证匹配准确性
                    expected_names = [region1_name, region2_name]
                    matched_names = [r['data_name'] for r in results]
                    
                    print(f"预期匹配: {expected_names}")
                    print(f"实际匹配: {matched_names}")
                    
                    if set(expected_names).issubset(set(matched_names)):
                        print("✅ 匹配验证成功！")
                    else:
                        print("❌ 匹配验证失败")
        
        mysql_conn.close()
        
        print("\n" + "=" * 80)
        print("测试完成")
        print("=" * 80)
        
    except Exception as e:
        print(f"测试失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_improved_parsing()
