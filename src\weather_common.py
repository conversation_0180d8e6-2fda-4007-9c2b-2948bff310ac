#!/usr/bin/env python3
# coding: utf-8
"""
天气数据处理公共模块
包含两个主要脚本的公共函数和类
"""

import os
import math
import logging
import asyncio
import asyncpg
import numpy as np
import pandas as pd
import xarray as xr
from typing import Dict, List, Tuple, Optional, Any
from datetime import datetime

# 导入统一配置
from config import (
    PG_URL, GRID_X_MIN, GRID_Y_MIN, GRID_X_MAX, GRID_Y_MAX, GRID_SIZE,
    STEP, LON_OFF, LAT_OFF, PROCESSING_CONFIG, WEATHER_DOWNLOAD_CONFIG, SCRIPT_DIR
)

# 导入天气数据下载模块
try:
    from weather_download import download_and_get_latest_weather_data, process_and_backup_weather_file
    WEATHER_DOWNLOAD_AVAILABLE = True
except ImportError:
    WEATHER_DOWNLOAD_AVAILABLE = False

# 配置日志
logger = logging.getLogger(__name__)

# ─────────────── 全局配置 ───────────────
# 格网生成顺序：先Y后X，所以行数是Y方向，列数是X方向
NC_NROWS = int(round((GRID_Y_MAX-GRID_Y_MIN)/GRID_SIZE))   # 820 (Y方向)
NC_NCOLS = int(round((GRID_X_MAX-GRID_X_MIN)/GRID_SIZE))   # 875 (X方向)

# 协程配置 - 优化为高性能模式
MAX_CONCURRENT_DB_OPERATIONS = 20  # 增加并发数据库操作数
MAX_CONCURRENT_PROCEDURES = 10     # 提高存储过程并发数到10个


# ========== 标准格网编码/解码函数 ==========
# 0.01° 等纬经标准格网的 ID 方案

def encode_cell(lon: float, lat: float) -> int:
    """经纬 → cell_id（32bit 整数）"""
    ix = int(math.floor((lon + LON_OFF) / STEP))
    iy = int(math.floor((lat + LAT_OFF) / STEP))
    return (iy << 16) | ix          # 行主序打包


def decode_cell(cell_id: int) -> Tuple[float, float]:
    """cell_id → 左下角 (lon_min, lat_min)"""
    ix = cell_id & 0xFFFF
    iy = cell_id >> 16
    lon_min = ix * STEP - LON_OFF
    lat_min = iy * STEP - LAT_OFF
    return lon_min, lat_min


def get_netcdf_indices_from_coords(lon: float, lat: float,
                                   nc_lon_min: float, nc_lat_min: float) -> Tuple[int, int]:
    """根据经纬度坐标直接计算NetCDF索引"""
    lon_idx = int(round((lon - nc_lon_min) / STEP))
    lat_idx = int(round((lat - nc_lat_min) / STEP))
    return lat_idx, lon_idx


# ========== NetCDF文件处理公共函数 ==========

def get_nc_file_path_single(data_type: str = None) -> Tuple[str, bool, str]:
    """
    获取单个NC文件路径（用于6分钟数据）
    如果启用下载功能，先尝试下载最新数据

    Args:
        data_type: 数据类型，如 'gz_mpfv3', 'gz_didiforecast1hTEM' 等

    Returns:
        tuple: (nc_file_path, is_downloaded_file, data_type)
    """
    from config import NC_PATH  # 动态导入避免循环依赖
    
    # 使用传入的数据类型或配置的默认类型
    data_type = data_type or WEATHER_DOWNLOAD_CONFIG.get("default_data_type", "gz_mpfv3")

    # 检查是否启用下载功能
    if WEATHER_DOWNLOAD_CONFIG.get("enable_download", False) and WEATHER_DOWNLOAD_AVAILABLE:
        logger.info(f"启用下载功能，正在获取数据类型 [{data_type}] 的最新数据")
        try:
            latest_file = download_and_get_latest_weather_data(data_type)
            if latest_file and os.path.exists(latest_file):
                logger.info(f"成功获取最新文件: {latest_file}")
                return latest_file, True, data_type
            else:
                logger.warning("下载失败，使用配置的默认文件")
        except Exception as e:
            logger.error(f"下载过程中发生错误: {e}")
            logger.warning("下载失败，使用配置的默认文件")

    # 使用配置的默认文件
    if os.path.exists(NC_PATH):
        logger.info(f"使用配置的默认文件: {NC_PATH}")
        return NC_PATH, False, data_type
    else:
        logger.error(f"默认NC文件不存在: {NC_PATH}")
        raise FileNotFoundError(f"NC文件不存在: {NC_PATH}")


def get_nc_files_path_multiple(data_types: List[str] = None) -> Tuple[Dict[str, str], Dict[str, Any], List[str]]:
    """
    获取多个NC文件路径（用于1小时数据）
    如果启用下载功能，先尝试下载最新数据
    优先处理PRE数据（因为比较小，下载快）

    Args:
        data_types: 数据类型列表，如 ['gz_didiforecast1hPRE', 'gz_didiforecast1hTEM'] 等

    Returns:
        tuple: (nc_files_dict, downloaded_files_info, data_types)
    """
    # 1小时天气数据类型配置
    WEATHER_1H_TYPES = {
        'PRE': {
            'var_name': 'PRE',
            'data_type': 'float',
            'scale_factor': 1.0,
            'description': '降水量'
        },
        'WEATHER': {
            'var_name': 'WEATHER',
            'data_type': 'int',
            'scale_factor': 1.0,
            'description': '天气现象'
        },
        'VIS': {
            'var_name': 'VIS',
            'data_type': 'int',
            'scale_factor': 1.0,
            'description': '能见度'
        },
        'TEM': {
            'var_name': 'TEM',
            'data_type': 'float',
            'scale_factor': 1.0,
            'description': '温度'
        }
    }
    
    # 如果没有指定数据类型，使用所有支持的类型
    if data_types is None:
        data_types = list(WEATHER_1H_TYPES.keys())
    
    nc_files_dict = {}
    downloaded_files_info = {}
    
    # 检查是否启用下载功能
    if WEATHER_DOWNLOAD_CONFIG.get("enable_download", False) and WEATHER_DOWNLOAD_AVAILABLE:
        logger.info("启用下载功能，正在获取最新数据")
        
        # 优先处理PRE数据（文件小，下载快）
        priority_order = ['PRE'] + [dt for dt in data_types if dt != 'PRE']
        
        for data_type in priority_order:
            if data_type not in data_types:
                continue
                
            download_data_type = f"gz_didiforecast1h{data_type}"
            logger.info(f"正在下载数据类型: {download_data_type}")
            
            try:
                latest_file = download_and_get_latest_weather_data(download_data_type)
                if latest_file and os.path.exists(latest_file):
                    nc_files_dict[data_type] = latest_file
                    downloaded_files_info[data_type] = {
                        'file_path': latest_file,
                        'download_time': datetime.now(),
                        'data_type': download_data_type
                    }
                    logger.info(f"成功获取 {data_type} 数据: {latest_file}")
                else:
                    logger.warning(f"下载 {data_type} 数据失败，将查找本地文件")
            except Exception as e:
                logger.error(f"下载 {data_type} 数据时发生错误: {e}")
                logger.warning(f"下载失败，将查找 {data_type} 的本地文件")
    
    # 对于没有下载成功的数据类型，查找本地文件
    for data_type in data_types:
        if data_type not in nc_files_dict:
            # 尝试查找本地NC文件
            local_pattern = f"*{data_type}*.nc"
            import glob
            from config import DATA_PATHS
            
            search_dirs = [
                DATA_PATHS.get("weather_dir", "./data/weather"),
                DATA_PATHS.get("weather_mpf_dir", "./data/weather/MPF"),
                str(SCRIPT_DIR / "data"),
                str(SCRIPT_DIR)
            ]
            
            found_file = None
            for search_dir in search_dirs:
                if os.path.exists(search_dir):
                    files = glob.glob(os.path.join(search_dir, local_pattern))
                    if files:
                        # 选择最新的文件
                        found_file = max(files, key=os.path.getmtime)
                        break
            
            if found_file:
                nc_files_dict[data_type] = found_file
                logger.info(f"找到 {data_type} 本地文件: {found_file}")
            else:
                logger.warning(f"未找到 {data_type} 类型的本地NC文件")

    return nc_files_dict, downloaded_files_info, data_types


def load_valid_cells() -> np.ndarray:
    """
    加载有效格网单元
    从数据库或缓存文件中加载有效的cell_id列表
    """
    try:
        # 尝试从数据库加载
        from sqlalchemy import create_engine, text
        engine = create_engine(PG_URL)
        
        with engine.connect() as conn:
            result = conn.execute(text("SELECT DISTINCT cell_id FROM weather_grid ORDER BY cell_id"))
            valid_cells = np.array([row[0] for row in result], dtype=np.int32)
            
        logger.info(f"从数据库加载了 {len(valid_cells)} 个有效格网单元")
        return valid_cells
        
    except Exception as e:
        logger.warning(f"从数据库加载有效格网失败: {e}")
        
        # 生成默认的格网范围
        logger.info("生成默认格网范围")
        cell_ids = []
        
        for lat in np.arange(GRID_Y_MIN, GRID_Y_MAX, STEP):
            for lon in np.arange(GRID_X_MIN, GRID_X_MAX, STEP):
                cell_id = encode_cell(lon, lat)
                cell_ids.append(cell_id)
        
        valid_cells = np.array(cell_ids, dtype=np.int32)
        logger.info(f"生成了 {len(valid_cells)} 个默认格网单元")
        return valid_cells


# ========== 异步数据库管理器 ==========

class AsyncDatabaseManager:
    """异步数据库管理器"""

    def __init__(self, pg_url: str, max_connections: int = 50):
        self.pg_url = pg_url
        self.max_connections = max_connections
        self.pool = None

    async def __aenter__(self):
        """异步上下文管理器入口"""
        await self.connect()
        return self

    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """异步上下文管理器出口"""
        await self.close()

    async def connect(self):
        """建立连接池"""
        try:
            self.pool = await asyncpg.create_pool(
                self.pg_url,
                min_size=10,
                max_size=self.max_connections,
                command_timeout=60,
                server_settings={
                    'jit': 'off',
                    'application_name': 'weather_processor'
                }
            )
            logger.info(f"数据库连接池创建成功，最大连接数: {self.max_connections}")
        except Exception as e:
            logger.error(f"创建数据库连接池失败: {e}")
            raise

    async def close(self):
        """关闭连接池"""
        if self.pool:
            await self.pool.close()
            logger.info("数据库连接池已关闭")

    async def execute_batch_upsert(self, table_name: str, data_list: List[Dict], 
                                   conflict_columns: List[str]) -> bool:
        """
        批量执行upsert操作
        
        Args:
            table_name: 表名
            data_list: 数据列表
            conflict_columns: 冲突列名列表
            
        Returns:
            bool: 操作是否成功
        """
        if not data_list:
            return True

        try:
            async with self.pool.acquire() as conn:
                # 构建upsert SQL
                columns = list(data_list[0].keys())
                placeholders = ', '.join([f'${i+1}' for i in range(len(columns))])
                
                conflict_clause = ', '.join(conflict_columns)
                update_clause = ', '.join([f'{col} = EXCLUDED.{col}' 
                                         for col in columns if col not in conflict_columns])
                
                sql = f"""
                INSERT INTO {table_name} ({', '.join(columns)})
                VALUES ({placeholders})
                ON CONFLICT ({conflict_clause})
                DO UPDATE SET {update_clause}
                """
                
                # 准备数据
                values_list = [[row[col] for col in columns] for row in data_list]
                
                # 执行批量插入
                await conn.executemany(sql, values_list)
                
            return True
            
        except Exception as e:
            logger.error(f"批量upsert操作失败: {e}")
            return False

    async def call_stored_procedure(self, procedure_name: str, *args) -> bool:
        """
        调用存储过程
        
        Args:
            procedure_name: 存储过程名称
            *args: 存储过程参数
            
        Returns:
            bool: 调用是否成功
        """
        try:
            async with self.pool.acquire() as conn:
                await conn.execute(f"CALL {procedure_name}({', '.join(['$' + str(i+1) for i in range(len(args))])})", *args)
            return True
            
        except Exception as e:
            logger.error(f"调用存储过程 {procedure_name} 失败: {e}")
            return False


# ========== 数据处理辅助函数 ==========

def backup_processed_file(file_path: str, data_type: str = None) -> bool:
    """
    备份已处理的文件
    
    Args:
        file_path: 文件路径
        data_type: 数据类型
        
    Returns:
        bool: 备份是否成功
    """
    if not WEATHER_DOWNLOAD_AVAILABLE:
        return False
        
    try:
        return process_and_backup_weather_file(file_path, data_type)
    except Exception as e:
        logger.error(f"备份文件失败: {e}")
        return False


def setup_logging(level: int = logging.INFO, format_str: str = None) -> None:
    """
    设置日志配置

    Args:
        level: 日志级别
        format_str: 日志格式字符串
    """
    from logging.handlers import TimedRotatingFileHandler
    from pathlib import Path

    if format_str is None:
        format_str = '%(asctime)s - %(name)s - %(levelname)s - %(message)s'

    # 创建日志目录
    log_dir = Path('logs')
    log_dir.mkdir(parents=True, exist_ok=True)

    # 创建按天轮转的文件处理器
    file_handler = TimedRotatingFileHandler(
        filename='logs/weather_processing.log',
        when='midnight',  # 每天午夜轮转
        interval=1,       # 每1天轮转一次
        backupCount=30,   # 保留30天的日志文件
        encoding='utf-8',
        delay=False,
        utc=False
    )
    file_handler.suffix = "%Y-%m-%d.log"

    logging.basicConfig(
        level=level,
        format=format_str,
        handlers=[
            logging.StreamHandler(),
            file_handler
        ]
    )
