#!/usr/bin/env python3
# coding: utf-8
"""
依赖测试脚本
验证所有已安装的依赖是否能正常工作
"""

import sys
import traceback
from typing import List, Tuple
from pathlib import Path

# 添加项目根目录和src目录到Python路径
project_root = Path(__file__).parent.parent
src_path = project_root / "src"
sys.path.insert(0, str(project_root))
sys.path.insert(0, str(src_path))

def test_import(module_name: str, description: str = "") -> Tuple[bool, str]:
    """测试模块导入"""
    try:
        __import__(module_name)
        return True, f"✓ {module_name} - {description}"
    except ImportError as e:
        return False, f"✗ {module_name} - {description} (错误: {e})"
    except Exception as e:
        return False, f"✗ {module_name} - {description} (未知错误: {e})"

def test_basic_functionality():
    """测试基本功能"""
    results = []
    
    # 测试基础库
    basic_tests = [
        ("os", "操作系统接口"),
        ("sys", "系统特定参数"),
        ("pathlib", "路径处理"),
        ("json", "JSON处理"),
        ("logging", "日志记录"),
        ("datetime", "日期时间"),
        ("urllib.parse", "URL解析"),
        ("multiprocessing", "多进程"),
        ("asyncio", "异步编程"),
        ("concurrent.futures", "并发执行"),
    ]
    
    for module, desc in basic_tests:
        success, msg = test_import(module, desc)
        results.append((success, msg))
    
    # 测试第三方库
    third_party_tests = [
        ("numpy", "数值计算库"),
        ("pandas", "数据分析库"),
        ("requests", "HTTP请求库"),
        ("yaml", "YAML解析库"),
        ("sqlalchemy", "SQL工具包"),
        ("asyncpg", "PostgreSQL异步驱动"),
        ("pymysql", "MySQL驱动"),
        ("xarray", "多维数组处理"),
    ]
    
    for module, desc in third_party_tests:
        success, msg = test_import(module, desc)
        results.append((success, msg))
    
    # 测试地理空间库
    geo_tests = [
        ("shapely", "几何对象处理"),
        ("geopandas", "地理空间数据处理"),
        ("geoalchemy2", "地理空间数据库扩展"),
        ("pyproj", "投影转换"),
        ("pyogrio", "地理空间I/O"),
    ]
    
    for module, desc in geo_tests:
        success, msg = test_import(module, desc)
        results.append((success, msg))
    
    return results

def test_project_modules():
    """测试项目模块"""
    results = []
    
    project_tests = [
        ("config", "项目配置模块"),
        ("weather_download", "天气数据下载模块"),
    ]
    
    for module, desc in project_tests:
        success, msg = test_import(module, desc)
        results.append((success, msg))
    
    return results

def test_advanced_functionality():
    """测试高级功能"""
    results = []
    
    # 测试数据库连接字符串生成
    try:
        from config import get_postgres_url, MYSQL_URL
        postgres_url = get_postgres_url()
        if postgres_url and MYSQL_URL:
            results.append((True, "✓ 数据库连接配置正常"))
        else:
            results.append((False, "✗ 数据库连接配置缺失"))
    except Exception as e:
        results.append((False, f"✗ 数据库连接配置测试失败: {e}"))
    
    # 测试YAML配置加载
    try:
        from config import _yaml_config
        if _yaml_config:
            results.append((True, "✓ YAML配置文件加载成功"))
        else:
            results.append((True, "✓ YAML配置文件未找到（使用默认配置）"))
    except Exception as e:
        results.append((False, f"✗ YAML配置加载失败: {e}"))
    
    # 测试天气下载配置
    try:
        from config import WEATHER_DOWNLOAD_CONFIG
        if WEATHER_DOWNLOAD_CONFIG:
            results.append((True, "✓ 天气下载配置正常"))
        else:
            results.append((False, "✗ 天气下载配置缺失"))
    except Exception as e:
        results.append((False, f"✗ 天气下载配置测试失败: {e}"))
    
    return results

def main():
    """主函数"""
    print("=" * 60)
    print("Weather Script 依赖测试")
    print("=" * 60)
    print(f"Python版本: {sys.version}")
    print(f"Python路径: {sys.executable}")
    print("=" * 60)
    
    all_results = []
    
    # 测试基本功能
    print("\n📦 基础库和第三方库测试:")
    print("-" * 40)
    basic_results = test_basic_functionality()
    all_results.extend(basic_results)
    for success, msg in basic_results:
        print(msg)
    
    # 测试项目模块
    print("\n🏗️ 项目模块测试:")
    print("-" * 40)
    project_results = test_project_modules()
    all_results.extend(project_results)
    for success, msg in project_results:
        print(msg)
    
    # 测试高级功能
    print("\n⚙️ 高级功能测试:")
    print("-" * 40)
    advanced_results = test_advanced_functionality()
    all_results.extend(advanced_results)
    for success, msg in advanced_results:
        print(msg)
    
    # 统计结果
    print("\n" + "=" * 60)
    print("测试结果统计:")
    print("=" * 60)
    
    total_tests = len(all_results)
    passed_tests = sum(1 for success, _ in all_results if success)
    failed_tests = total_tests - passed_tests
    
    print(f"总测试数: {total_tests}")
    print(f"通过测试: {passed_tests}")
    print(f"失败测试: {failed_tests}")
    print(f"成功率: {passed_tests/total_tests*100:.1f}%")
    
    if failed_tests == 0:
        print("\n🎉 所有测试通过！项目依赖配置完成。")
        return 0
    else:
        print(f"\n⚠️ 有 {failed_tests} 个测试失败，请检查相关依赖。")
        return 1

if __name__ == "__main__":
    try:
        exit_code = main()
        sys.exit(exit_code)
    except KeyboardInterrupt:
        print("\n\n测试被用户中断。")
        sys.exit(1)
    except Exception as e:
        print(f"\n\n测试过程中发生未预期的错误: {e}")
        traceback.print_exc()
        sys.exit(1)
