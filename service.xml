<service>
  <id>weather-scheduler</id>
  <name>Weather Data Scheduler Service</name>
  <description>This service runs the weather data processing scheduler that automatically downloads and processes weather data at scheduled intervals.</description>
  
  <executable>D:\script\weather_script\.venv\Scripts\python.exe</executable>
  <arguments>-u D:\script\weather_script\run_weather_scheduler.py</arguments>
  
  <workingdirectory>D:\script\weather_script</workingdirectory>
  
  <logpath>D:\script\weather_script\logs</logpath>
  <log mode="append">
    <sizeThreshold>10240</sizeThreshold>
    <keepFiles>8</keepFiles>
    <pattern>weather-scheduler.log</pattern>
  </log>
  <logmode>rotate</logmode>
  <outfiledisabled>false</outfiledisabled>
  <errfiledisabled>false</errfiledisabled>

  <!-- 强制刷新输出到日志文件 -->
  <outfilepattern>weather-scheduler-out.log</outfilepattern>
  <errfilepattern>weather-scheduler-err.log</errfilepattern>
  
  <startmode>Automatic</startmode>
  
  <onfailure action="restart" delay="10 sec"/>
  <onfailure action="restart" delay="30 sec"/>
  <onfailure action="restart" delay="60 sec"/>
  
  <!-- CHANGE HERE: Corrected environment variable syntax -->
  <env name="PYTHONPATH" value="D:\script\weather_script\src"/>
  <env name="PYTHONIOENCODING" value="utf-8"/>
  <env name="PYTHONDONTWRITEBYTECODE" value="1"/>
  <env name="PYTHONUNBUFFERED" value="1"/>
  <env name="PYTHONLEGACYWINDOWSSTDIO" value="1"/>
  <env name="TZ" value="Asia/Shanghai"/>
  
  <depend>Tcpip</depend>
  <depend>Dnscache</depend>
  
  <serviceaccount>
    <domain>NT AUTHORITY</domain>
    <user>SYSTEM</user>
  </serviceaccount>
  
  <priority>Normal</priority>
  <!-- 减少停止超时时间，加快关闭速度 -->
  <stoptimeout>10 sec</stoptimeout>
  <stopparentprocessfirst>true</stopparentprocessfirst>

  <!-- 使用专用停止脚本快速关闭 -->
  <stoparguments>D:\script\weather_script\stop_weather_scheduler.py</stoparguments>
  <stopexecutable>D:\script\weather_script\.venv\Scripts\python.exe</stopexecutable>
</service>
