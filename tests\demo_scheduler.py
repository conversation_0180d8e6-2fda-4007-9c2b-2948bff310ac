#!/usr/bin/env python3
# coding: utf-8
"""
天气数据定时任务调度器演示脚本
展示调度器的主要功能，不实际执行数据处理任务
"""

import asyncio
import sys
from pathlib import Path
import logging
from datetime import datetime, timedelta

# 添加src目录到Python路径
src_path = Path(__file__).parent / "src"
sys.path.insert(0, str(src_path))

# 导入时间工具
from time_utils import get_local_now

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

logger = logging.getLogger(__name__)


async def demo_uptime_checker():
    """演示uptime检查功能"""
    logger.info("=== 演示数据时效性检查 ===")
    
    try:
        from uptime_checker import UptimeChecker, is_data_fresh
        
        # 检查6分钟数据
        logger.info("检查6分钟数据新鲜度...")
        is_fresh = await is_data_fresh("gz_mpfv3")
        logger.info(f"6分钟数据是否新鲜: {is_fresh}")
        
        # 检查1小时数据
        for data_type in ["PRE", "TEM", "WEATHER", "VIS"]:
            download_type = f"gz_didiforecast1h{data_type}"
            logger.info(f"检查1小时{data_type}数据新鲜度...")
            is_fresh = await is_data_fresh(download_type)
            logger.info(f"1小时{data_type}数据是否新鲜: {is_fresh}")
        
    except Exception as e:
        logger.error(f"uptime检查演示失败: {e}")


async def demo_task_manager():
    """演示任务管理功能"""
    logger.info("=== 演示任务管理功能 ===")
    
    try:
        from task_manager import task_manager
        
        # 启动任务管理器
        await task_manager.start_processing()
        logger.info("任务管理器已启动")
        
        # 创建演示任务
        async def demo_task(task_name: str, duration: int):
            logger.info(f"开始执行演示任务: {task_name}")
            await asyncio.sleep(duration)
            logger.info(f"演示任务完成: {task_name}")
            return f"任务 {task_name} 执行结果"
        
        # 提交多个任务
        tasks = []
        for i in range(3):
            task_name = f"demo_task_{i+1}"
            scheduled_time = datetime.now() + timedelta(seconds=i*2)
            
            task_id = await task_manager.submit_task(
                task_type="demo",
                data_type=task_name,
                scheduled_time=scheduled_time,
                task_func=lambda name=task_name: demo_task(name, 2)
            )
            tasks.append(task_id)
            logger.info(f"提交演示任务: {task_id}")
        
        # 等待任务执行
        logger.info("等待任务执行...")
        await asyncio.sleep(10)
        
        # 检查任务状态
        for task_id in tasks:
            task_info = task_manager.get_task_status(task_id)
            if task_info:
                logger.info(f"任务 {task_id} 状态: {task_info.status.value}")
        
        # 获取统计信息
        queue_stats = task_manager.get_queue_stats()
        logger.info(f"队列统计: {queue_stats}")
        
        # 停止任务管理器
        await task_manager.stop_processing()
        logger.info("任务管理器已停止")
        
    except Exception as e:
        logger.error(f"任务管理演示失败: {e}")


async def demo_scheduler():
    """演示调度器功能"""
    logger.info("=== 演示调度器功能 ===")
    
    try:
        from scheduler import WeatherTaskScheduler
        
        scheduler = WeatherTaskScheduler()
        
        # 显示调度时间
        current_time = get_local_now()
        next_hourly = scheduler._get_next_hourly_schedule(current_time)
        next_six_minute = scheduler._get_next_six_minute_schedule(current_time)
        
        logger.info(f"当前时间: {current_time}")
        logger.info(f"下一个1小时任务调度时间: {next_hourly}")
        logger.info(f"下一个6分钟任务调度时间: {next_six_minute}")
        
        # 计算等待时间
        wait_hourly = (next_hourly - current_time).total_seconds()
        wait_six_minute = (next_six_minute - current_time).total_seconds()
        
        logger.info(f"距离下一个1小时任务: {wait_hourly:.1f} 秒")
        logger.info(f"距离下一个6分钟任务: {wait_six_minute:.1f} 秒")
        
        # 显示调度器状态
        status = scheduler.get_status()
        logger.info(f"调度器状态: {status}")
        
    except Exception as e:
        logger.error(f"调度器演示失败: {e}")


async def demo_resource_manager():
    """演示资源管理功能"""
    logger.info("=== 演示资源管理功能 ===")
    
    try:
        from resource_manager import resource_manager
        
        # 初始化资源管理器
        await resource_manager.initialize()
        logger.info("资源管理器已初始化")
        
        # 获取统计信息
        stats = resource_manager.get_stats()
        logger.info(f"资源统计: {stats}")
        
        # 测试数据库连接
        async with resource_manager.get_db_connection() as conn:
            result = await conn.fetchval("SELECT version()")
            logger.info(f"数据库版本: {result}")
        
        # 关闭资源管理器
        await resource_manager.close()
        logger.info("资源管理器已关闭")
        
    except Exception as e:
        logger.error(f"资源管理演示失败: {e}")


async def main():
    """主演示函数"""
    logger.info("🚀 开始天气数据定时任务调度器演示")
    logger.info("=" * 60)
    
    try:
        # 演示各个组件
        await demo_resource_manager()
        await asyncio.sleep(1)
        
        await demo_uptime_checker()
        await asyncio.sleep(1)
        
        await demo_task_manager()
        await asyncio.sleep(1)
        
        await demo_scheduler()
        
        logger.info("=" * 60)
        logger.info("✅ 演示完成！")
        logger.info("")
        logger.info("🎯 要启动完整的调度器系统，请运行：")
        logger.info("   uv run python run_weather_scheduler.py")
        logger.info("")
        logger.info("🌐 然后访问 http://localhost:8000 查看Web界面")
        
    except Exception as e:
        logger.error(f"演示过程中发生错误: {e}")
        sys.exit(1)


if __name__ == "__main__":
    asyncio.run(main())
