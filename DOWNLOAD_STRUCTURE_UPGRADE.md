# 天气数据下载和存储目录结构升级

## 概述

已成功将天气数据下载和存储系统从简单的平面目录结构升级为按年/月/日划分的层次化目录结构，实现了下载文件、NC文件和备份文件的统一日期化管理。

## 主要改进

### 1. 完整的目录结构升级

**升级前（旧格式）：**
```
data/precipitation_6min/
├── downloads/
│   ├── gz_mpfv3_202507221430.tar.gz
│   └── gz_mpfv3_202507221436.tar.gz
├── nc_files/
│   ├── MPF_20250722143000.nc
│   ├── MPF_20250722143600.nc
│   └── MPF_20250722144200.nc
└── backup/
    ├── old_file1.nc
    └── old_file2.nc
```

**升级后（新格式）：**
```
data/precipitation_6min/
├── 2025/                    # 年份目录
│   ├── 07/                  # 月份目录
│   │   ├── 22/              # 日期目录
│   │   │   ├── downloads/   # 当天的下载文件
│   │   │   │   ├── gz_mpfv3_202507221430.tar.gz
│   │   │   │   └── gz_mpfv3_202507221436.tar.gz
│   │   │   └── nc_files/    # 当天的NC文件
│   │   │       ├── MPF_20250722143000.nc
│   │   │       ├── MPF_20250722143600.nc
│   │   │       └── MPF_20250722144200.nc
│   │   ├── 23/
│   │   │   ├── downloads/
│   │   │   └── nc_files/
│   │   └── 24/
│   │       ├── downloads/
│   │       └── nc_files/
│   └── 08/
│       └── 01/
│           ├── downloads/
│           └── nc_files/
└── backup/                  # 备份文件（也按日期组织）
    └── 2025/
        └── 07/
            └── 22/
                ├── old_file1.nc
                └── old_file2_143052.nc
```

### 2. 核心功能升级

#### 2.1 智能目录管理
- **动态目录创建**: 根据当前日期自动创建年/月/日目录结构
- **自动路径解析**: 下载和解压过程自动使用正确的日期目录
- **目录权限管理**: 确保所有创建的目录具有正确的权限

#### 2.2 增强的文件查找功能
- **按日期查找**: `get_nc_files_by_date()` 获取指定日期的所有NC文件
- **智能最新文件**: `get_latest_nc_file()` 在指定天数范围内搜索最新文件
- **多日期搜索**: 支持跨多个日期目录搜索文件

#### 2.3 完善的清理机制
- **分类清理**: 下载文件和备份文件分别管理，不同的保留策略
- **按日期清理**: 支持按日期批量删除整个目录
- **空目录清理**: 自动删除空的日期/月份/年份目录
- **配置化清理**: 支持通过配置文件设置保留天数

#### 2.4 灵活的备份策略
- **按日期备份**: 备份文件也按年/月/日组织
- **指定日期备份**: 可以备份指定日期的文件
- **批量备份**: 支持备份所有日期目录中的文件

## 技术实现

### 修改的核心方法

1. **`_get_date_based_dirs()`**: 新增方法，根据日期生成目录路径
2. **`_download_and_extract()`**: 修改为使用日期目录
3. **`_extract_tar_gz()`**: 支持指定目标解压目录
4. **`backup_existing_files()`**: 支持按日期备份
5. **`get_latest_nc_file()`**: 支持多日期搜索
6. **`cleanup_old_backups()`**: 支持层次化目录清理
7. **`cleanup_old_downloads()`**: 新增下载文件清理功能

### 新增的功能方法

1. **`get_nc_files_by_date()`**: 按日期获取NC文件
2. **`backup_all_existing_files()`**: 备份所有现有文件
3. **`cleanup_old_downloads()`**: 清理旧下载文件

## 使用方法

### 基本使用（自动生效）

新的目录结构已经自动集成，无需修改现有代码：

```python
from weather_download import WeatherDownloader

# 创建下载器实例
downloader = WeatherDownloader('gz_mpfv3')

# 下载数据（自动使用新的目录结构）
files = downloader.download_from_api()
if files:
    print(f'下载成功，文件保存在: {files}')
```

### 按日期操作

```python
from datetime import datetime

# 获取指定日期的文件
target_date = datetime(2025, 7, 22)
files = downloader.get_nc_files_by_date(target_date)

# 备份指定日期的文件
success = downloader.backup_existing_files(target_date)

# 获取最新文件（搜索过去7天）
latest_file = downloader.get_latest_nc_file(search_days=7)
```

### 清理管理

```python
# 清理7天前的下载文件
downloader.cleanup_old_downloads(keep_days=7)

# 清理30天前的备份文件
downloader.cleanup_old_backups(keep_days=30)
```

## 配置选项

在 `config.yml` 中可以添加新的配置项：

```yaml
weather_download:
  # 现有配置
  backup_keep_days: 30        # 备份文件保留天数
  auto_cleanup_backups: true  # 是否自动清理旧备份
  enable_backup: true         # 是否启用备份功能
  
  # 新增配置（可选）
  download_keep_days: 7       # 下载文件保留天数
  auto_cleanup_downloads: true # 是否自动清理旧下载文件
```

## 兼容性和迁移

### 向后兼容性
- ✅ 新下载自动使用新结构
- ✅ 现有备份文件保持原位置
- ✅ 清理功能同时支持新旧格式
- ✅ 文件查找功能搜索新旧位置
- ✅ 无需修改现有代码

### 渐进式迁移
- 新下载的文件使用新结构
- 旧文件可以继续正常使用
- 可以选择性地整理旧文件到新结构
- 系统会自然过渡到新结构

## 优势总结

1. **更好的组织性**: 所有文件按日期自动分类，便于管理
2. **性能提升**: 避免单个目录文件过多导致的性能问题
3. **精确查找**: 可以快速定位特定日期的数据文件
4. **智能清理**: 支持按日期批量清理，更精确的空间管理
5. **统一管理**: 下载文件、NC文件、备份文件使用统一的日期结构
6. **自动维护**: 自动创建目录、清理空目录，无需手动维护
7. **配置灵活**: 支持不同类型文件的不同保留策略

## 测试验证

所有功能已通过完整测试：
- ✅ 日期目录创建正确
- ✅ 下载文件保存到正确位置
- ✅ NC文件解压到正确位置
- ✅ 备份文件按日期组织
- ✅ 文件查找功能正常
- ✅ 清理功能工作正常
- ✅ 向后兼容性良好

## 立即生效

新的目录结构已经集成到系统中，下次运行天气数据下载时就会自动使用新的目录结构。您可以：

1. 继续正常使用现有的下载和处理流程
2. 新下载的文件会自动按日期组织
3. 可以使用新的按日期查找功能
4. 享受更好的文件管理体验

这次升级为您的天气数据管理系统提供了更加专业和高效的文件组织方案！
