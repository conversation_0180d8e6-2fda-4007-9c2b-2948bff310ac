# 预警内容解析功能说明

## 概述

预警内容解析功能用于从天气预警的文本内容中自动提取和匹配受影响的子区域信息，并将解析结果存储到数据库中。该功能每10分钟执行一次，处理几十条预警记录，支持并行处理以提高效率。

## 功能特性

- ✅ 自动提取行政区名称（气象台前的内容）
- ✅ 解析预警内容中的时间和地点信息
- ✅ 智能匹配子区域名称
- ✅ 支持多种匹配策略（精确匹配、去后缀匹配、逐字匹配）
- ✅ 批量查询数据库提高效率
- ✅ 并行处理多个预警记录
- ✅ 完整的错误处理和日志记录

## 解析流程

### 1. 提取行政区名称
从预警内容开头提取到"气象台"之前的内容作为行政区名称。

**示例：**
```
输入：墨江县气象台2025年7月18日07时05分发布...
输出：墨江县
```

### 2. 提取冒号后内容
获取第一个冒号（：或:）后的所有内容。

**示例：**
```
输入：...发布大雾黄色预警信号：未来12小时墨江县大部乡镇将出现浓雾...
输出：未来12小时墨江县大部乡镇将出现浓雾...
```

### 3. 提取时间后的地点信息
查找"天"或"小时"后的内容，并取第一个逗号之前的部分。

**示例：**
```
输入：未来12小时墨江县大部乡镇将出现浓雾，请做好防护
输出：墨江县大部乡镇
```

### 4. 清理地点前缀
去除常见前缀（我县、我区、我州、我市）和行政区名称前缀。

**示例：**
```
输入：我县南岸镇、板栗镇、中城镇
输出：南岸镇、板栗镇、中城镇
```

### 5. 分割地点
使用顿号（、）分割多个地点。

**示例：**
```
输入：南岸镇、板栗镇、中城镇
输出：["南岸镇", "板栗镇", "中城镇"]
```

### 6. 匹配区域名称

#### 情况一：处理除最后一个之外的所有项
- 直接匹配：在数据库中查找以该地名开头的记录
- 如果唯一匹配，则记录该结果
- 如果无匹配，去除后缀（市、乡、镇、县、村、区、街、街道）后再匹配
- 如果多匹配或仍无匹配，则跳过该项

#### 情况二：处理最后一个项（特殊处理）
- 逐字增加匹配：从1个字开始，逐步增加字符数进行匹配
- 单字匹配验证：如果是单字匹配，检查前两个字是否相同
- 唯一匹配确认：找到唯一匹配时返回结果
- 无匹配放弃：如果没有任何匹配，说明不是地名

## 数据库结构

### sys_region_code表（MySQL）
用于存储区域代码映射关系：
- `data_name`: 区域名称
- `data_code`: 区域代码  
- `sup_code`: 上级区域代码（对应预警的geo_code）

### weather_alarm表（PostgreSQL）
存储预警信息，新增字段：
- `sub_code`: 子区域代码（逗号分隔）
- `sub_name`: 子区域名称（逗号分隔）

## 使用示例

### 基本使用
```python
import asyncio
from src.weather_alarm import WeatherAlarmProcessor

async def main():
    processor = WeatherAlarmProcessor()
    await processor.initialize()
    
    # 执行一次预警信息获取和处理
    await processor.run_once()
    
    await processor.close()

asyncio.run(main())
```

### 单独使用解析器
```python
import pymysql
from src.weather_alarm import AlarmContentParser
from src.config import MYSQL_CONFIG

# 连接数据库
mysql_conn = pymysql.connect(**MYSQL_CONFIG)

# 创建解析器
parser = AlarmContentParser(mysql_conn)

# 解析预警内容
content = "墨江县气象台2025年7月18日07时05分发布大雾黄色预警信号：未来12小时墨江县大部乡镇将出现浓雾，请做好防护。"
geo_code = "530800"

# 获取区域数据
region_data = parser.get_region_data_by_geo_codes([geo_code])

# 解析内容
results = parser.parse_alarm_content(content, geo_code, region_data)

for result in results:
    print(f"匹配区域: {result['data_name']} ({result['data_code']})")
```

## 性能优化

1. **批量查询**: 一次性获取所有需要的区域数据，避免重复查询
2. **并行处理**: 使用线程池并行处理CPU密集型的内容解析任务
3. **缓存机制**: 区域数据支持缓存，减少数据库访问
4. **高效匹配**: 使用优化的字符串匹配算法

## 测试

运行测试脚本验证功能：

```bash
# 测试解析器功能
python test_alarm_parser.py

# 测试完整处理流程
python test_complete_alarm_processing.py

# 使用示例
python example_alarm_usage.py
```

## 日志和监控

- 所有解析过程都有详细的日志记录
- 支持监控匹配成功率和处理时间
- 错误处理机制确保单个解析失败不影响整体处理

## 注意事项

1. 确保sys_region_code表中的数据完整且准确
2. 预警内容格式应符合标准格式
3. 定期检查匹配结果的准确性
4. 监控处理性能，必要时调整并行度
