# WeatherApi 项目配置文件
# 修改此文件中的配置项，然后重新运行程序即可生效

# 数据库配置
database:
  # PostgreSQL / PostGIS 配置
  postgres:
    host: "***************"
    port: 5432
    database: "middle-data-dev"
    username: "root"
    password: "Ylzx@9008*12-3*"
    schema: "public"
  
  # MySQL 配置（用于查询映射关系）
  mysql:
    host: "***************"
    port: 13306
    username: "root"
    password: "Ylzx@2000+!#-2"
    database: "ylzx-system-dev"

# 文件路径配置
paths:
  # 数据目录
  weather_dir: "./data/weather"
  weather_mpf_dir: "./data/weather/MPF"
  weather_backup_dir: "./data/weather/backup"
  gis_route_shape: "./data/gis_route_shape/gis_route_shape.shp"
  
  # NetCDF 文件配置
  netcdf:
    default_file: "./data/weather/MPF/MPF_20250627172400.nc"
    var_name: "PRE"
    phase_var: "phase"

# 格网参数配置
grid:
  # 格网范围（云南地区）
  x_min: 97.50
  y_min: 21.10
  x_max: 106.25
  y_max: 29.30
  size: 0.01  # 格网大小（度）
  buffer: 0.02  # 生成LUT时的缓冲距离（度）
  
  # 标准格网编码参数
  encoding:
    step: 0.01
    lon_offset: 180.0
    lat_offset: 90.0

# 数据库表名配置
tables:
  # 天气相关表名
  weather:
    weather_cell_6m: "weather_cell_6m"
    weather_routes: "weather_routes"
    weather_grid: "weather_grid"
    weather_cell_route_lut: "weather_cell_route_lut"
    weather_cell_route_lut_line: "weather_cell_route_lut_line"
  
  # 预报表名
  forecast:
    # 6分钟预报表
    forecast_6min_line: "forecast_precipitation_6min_line"
    forecast_6min_polygon: "forecast_precipitation_6min_polygon"
    forecast_6min_relation: "forecast_precipitation_6min_relation"
    
    # 1小时预报表
    forecast_hourly_line: "forecast_precipitation_hourly_line"
    forecast_hourly_polygon: "forecast_precipitation_hourly_polygon"
    forecast_hourly_relation: "forecast_precipitation_hourly_relation"
    
    # 日预报表
    forecast_daily_line: "precipitation_daily_line"
    forecast_daily_polygon: "precipitation_daily_polygon"
    forecast_daily_relation: "precipitation_daily_relation"

# Shapefile 字段映射配置
shapefile:
  fields:
    pile_start: "qdzh"  # 起点桩号（公里）
    pile_end: "zdzh"    # 终点桩号（公里）
    owner_unit: "owner_unit"  # 所有者单位
    route_code: "lxbh"  # 路线编号
    route_name: "lxmc"  # 路线名称
    maintenance_section: "maintenanc"  # 养护路段
    management_office: "tbdw"  # 管理单位

# 处理参数配置
processing:
  # 多进程配置 - 正式环境优化
  max_processes: null  # null表示使用 max(1, cpu_count() - 1)，正式环境建议设置为具体数值如16或32
  chunk_size: 2  # 每个进程一次处理的时间步数，正式环境增加到2提高效率

  # 几何处理参数
  snap_tolerance: 0.00001  # 几何对齐容差
  connection_tolerance_m: 10.0  # 连接容差（米）
  connection_tolerance_deg: 0.0001  # 连接容差（度）

# 存储过程配置 - 正式环境优化
stored_procedures:
  default_calc_func: "get_rainfall_type"
  default_arg_fields: ["rainfall", "phase"]
  default_source_table: "weather_cell_6m"
  default_value_field: "rainfall"

  # 正式环境并发控制
  max_concurrent_procedures: 20     # 最大并发存储过程数，正式环境增加到20
  procedure_timeout_seconds: 1200   # 存储过程超时时间，正式环境设置为20分钟
  max_concurrent_db_operations: 50  # 最大并发数据库操作数，正式环境增加到50

# 天气数据处理配置
weather_processing:
  # 6分钟天气数据处理配置
  weather_6m:
    # 数据源配置
    data_source:
      data_type: "gz_mpfv3"
      variable_name: "WEATHER"
      description: "6分钟天气现象数据"

    # 处理参数 - 正式环境优化
    processing:
      batch_size: 2000          # 批处理大小，正式环境增加到2000
      max_concurrent_db: 50     # 最大并发数据库连接，正式环境增加到50
      timeout_seconds: 600      # 处理超时时间，正式环境增加到10分钟
      enable_backup: true       # 是否备份处理后的文件

    # 数据库表配置
    database:
      target_table: "weather_cell_6m"
      conflict_columns: ["cell_id", "timestamp"]
      stored_procedure: "process_weather_6m_data"

    # 数据验证
    validation:
      required_variables: ["WEATHER"]
      min_valid_cells: 1000     # 最少有效格网数量
      value_range: [0, 100]     # 数据值范围

  # 1小时天气数据处理配置
  weather_1h:
    # 数据源配置
    data_source:
      data_types:
        PRE: "gz_didiforecast1hPRE"      # 降水
        TEM: "gz_didiforecast1hTEM"      # 温度
        WEATHER: "gz_didiforecast1hWEATHER"  # 天气现象
        VIS: "gz_didiforecast1hVIS"      # 能见度
      description: "1小时综合天气数据"

    # 处理参数 - 正式环境优化
    processing:
      batch_size: 1000          # 批处理大小，正式环境增加到1000
      max_concurrent_db: 40     # 最大并发数据库连接，正式环境增加到40
      timeout_seconds: 900      # 处理超时时间，正式环境增加到15分钟
      enable_backup: true       # 是否备份处理后的文件
      priority_order: ["PRE", "TEM", "WEATHER", "VIS"]  # 处理优先级

    # 数据库表配置
    database:
      target_table: "weather_cell_1h"
      conflict_columns: ["cell_id", "timestamp"]
      stored_procedure: "process_weather_1h_data"

    # 数据验证
    validation:
      required_variables: ["PRE", "TEM"]  # 必需的变量
      optional_variables: ["WEATHER", "VIS"]  # 可选的变量
      min_valid_cells: 1000     # 最少有效格网数量
      value_ranges:             # 各变量的值范围
        PRE: [0, 200]          # 降水量 mm
        TEM: [-50, 60]         # 温度 °C
        WEATHER: [0, 100]      # 天气现象代码
        VIS: [0, 50000]        # 能见度 m

# 天气数据下载配置
weather_download:
  # API配置
  api_base_url: "http://way.weatherdt.com/apimall/basic/ncfile.htm"
  api_key: "d23bf68181a1a038a0dfb0deaa04232f"

  # 数据类型配置
  data_types:
    gz_mpfv3:
      name: "6分钟降水"
      folder: "precipitation_6min"
      description: "6分钟级降水预报数据"
    gz_didiforecast1hWEATHER:
      name: "1小时天气现象"
      folder: "weather_1h"
      description: "1小时天气现象预报数据"
    gz_didiforecast1hTEM:
      name: "1小时温度"
      folder: "temperature_1h"
      description: "1小时温度预报数据"
    gz_didiforecast1hPRE:
      name: "1小时降水"
      folder: "precipitation_1h"
      description: "1小时降水预报数据"
    gz_didiforecast1hVIS:
      name: "1小时能见度"
      folder: "visibility_1h"
      description: "1小时能见度预报数据"

  # 下载参数
  download_timeout: 300  # 下载超时时间（秒）
  max_retries: 3         # 最大重试次数
  chunk_size: 8192       # 下载块大小（字节）
  enable_resume: true    # 是否启用断点续传
  enable_md5_check: true # 是否启用MD5校验
  retry_backoff_factor: 1 # 重试退避因子（指数退避）
  api_timeout: 30        # API调用超时时间（秒）

  # 备份配置
  backup_keep_days: 30   # 备份文件保留天数
  auto_cleanup_backups: true  # 是否自动清理旧备份

  # 功能开关
  enable_download: true  # 是否启用下载功能
  enable_backup: true    # 是否启用备份功能

  # 默认数据类型
  default_data_type: "gz_mpfv3"  # 默认使用6分钟降水数据

# 定时任务调度器配置
scheduler:
  # 数据新鲜度检查配置
  uptime_check:
    retry_interval_seconds: 30  # 重试间隔（秒）
    max_retry_duration_minutes: 60  # 最大重试时长（分钟）

  # 任务配置
  tasks:
    # 1小时任务配置
    hourly:
      data_types: ["PRE", "TEM", "WEATHER", "VIS"]
      timeout_minutes: 30
      schedule_pattern: "0 4 * * *"  # 每小时的4分钟执行

    # 6分钟任务配置
    six_minute:
      data_type: "gz_mpfv3"
      timeout_minutes: 10
      schedule_pattern: "4,10,16,22,28,34,40,46,52,58 * * * *"  # 每6分钟+4分钟执行

  # 资源池配置 - 正式环境优化
  resources:
    database:
      max_connections: 100      # 正式环境增加到100个连接
      min_connections: 20       # 正式环境最小连接数增加到20

    thread_pool:
      max_workers: 64           # 正式环境线程池增加到64

    process_pool:
      max_workers: null         # 自动检测CPU核心数，建议正式环境设置为具体数值如32

  # 监控配置
  monitoring:
    log_level: "INFO"
    log_file: "weather_scheduler.log"
    health_check_interval: 30  # 秒

  # Web服务配置
  web:
    host: "0.0.0.0"
    port: 8000
    reload: false

# ================================
# 正式环境性能优化配置
# ================================
production_optimization:
  # 数据库连接池优化
  database_pools:
    # 主连接池（用于常规操作）
    main_pool:
      max_connections: 150      # 主连接池最大连接数
      min_connections: 30       # 主连接池最小连接数
      connection_timeout: 30    # 连接超时时间（秒）
      command_timeout: 120      # 命令超时时间（秒）

    # 存储过程专用连接池
    procedure_pool:
      max_connections: 50       # 存储过程专用连接池
      min_connections: 10       # 存储过程最小连接数
      connection_timeout: 60    # 存储过程连接超时
      command_timeout: 1800     # 存储过程命令超时（30分钟）

  # 并发控制优化
  concurrency:
    max_concurrent_tasks: 10        # 最大并发任务数
    max_concurrent_downloads: 5     # 最大并发下载数
    max_concurrent_processing: 8    # 最大并发数据处理数
    max_concurrent_procedures: 25   # 最大并发存储过程数

  # 资源限制
  resource_limits:
    memory_limit_gb: 16         # 内存限制（GB）
    disk_space_limit_gb: 500    # 磁盘空间限制（GB）
    cpu_usage_limit_percent: 80 # CPU使用率限制（%）

  # 监控和告警
  monitoring:
    enable_performance_monitoring: true
    performance_log_interval: 60    # 性能日志记录间隔（秒）
    alert_thresholds:
      cpu_usage_percent: 85         # CPU使用率告警阈值
      memory_usage_percent: 85      # 内存使用率告警阈值
      disk_usage_percent: 90        # 磁盘使用率告警阈值
      connection_pool_usage_percent: 90  # 连接池使用率告警阈值
