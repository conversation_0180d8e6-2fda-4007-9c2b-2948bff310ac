# Uptime重试功能说明

## 功能概述

新增了两个重要改进：

1. **智能重试机制**：如果API返回的uptime时间不是新的，系统会每隔30秒重新获取API，直到获取到新的uptime数据为止
2. **立即状态更新**：在下载任务开始时就更新uptime状态，而不是完成后才更新，避免重复处理和并发问题

## 工作原理

### 之前的逻辑（有问题）
1. 调度器定时执行任务
2. 调用API获取数据信息
3. 检查uptime是否为新的
4. 如果不是新的，直接跳过任务
5. 如果是新的，开始下载和处理
6. **处理完成后才更新uptime状态**
7. 如果处理失败，uptime状态没有更新，下次还会重复处理

❌ **问题**：
- 同一个uptime可能被重复处理
- 并发场景下可能出现竞态条件
- 处理失败时会重复下载相同数据

### 现在的逻辑（改进后）
1. 调度器定时执行任务
2. 调用API获取数据信息
3. 检查uptime是否为新的
4. **如果不是新的，等待30秒后重新调用API**
5. **重复步骤2-4，直到获取到新的uptime或超时（60分钟）**
6. **获取到新uptime后，立即更新uptime状态**
7. 开始下载和处理
8. 处理完成后记录完成状态

✅ **改进**：
- 避免同一uptime被重复处理
- 解决并发场景下的竞态条件
- 确保数据处理的一致性和可靠性

## 配置选项

在 `config.yml` 中新增了配置选项：

```yaml
scheduler:
  uptime_check:
    retry_interval_seconds: 30  # 重试间隔（秒）
    max_retry_duration_minutes: 60  # 最大重试时长（分钟）
    retry_until_fresh: true  # 如果uptime不是新的，是否每隔30秒重新获取API直到获取到新数据
```

### 配置说明

- `retry_interval_seconds`: 重试间隔，默认30秒
- `max_retry_duration_minutes`: 最大重试时长，默认60分钟
- `retry_until_fresh`: 是否启用新的重试逻辑，默认true

## 使用场景

这个功能特别适用于以下场景：

1. **数据更新延迟**: 当气象数据源的更新时间不固定时
2. **网络不稳定**: 当网络连接不稳定，可能错过数据更新时
3. **确保数据完整性**: 确保不会因为时间差异而错过重要的气象数据
4. **并发处理**: 当多个任务可能同时执行时，避免重复处理
5. **故障恢复**: 当处理过程中出现故障时，避免重复下载相同数据

## 代码变更

### 主要变更文件

1. `src/uptime_checker.py`: 新增重试逻辑
2. `src/scheduler.py`: 修改任务执行逻辑，添加立即状态更新
3. `src/task_state_manager.py`: 新增 `mark_task_started` 方法
4. `src/config.py`: 新增配置选项
5. `config.yml`: 新增配置参数

### 关键函数

#### `check_and_get_fresh_data()`

```python
async def check_and_get_fresh_data(data_type: str, task_type: str = None,
                                  last_processed_uptime: str = None) -> Optional[Dict[str, Any]]:
```

新增了两个参数：
- `task_type`: 任务类型 ("1h" 或 "6m")
- `last_processed_uptime`: 上次处理的uptime

#### `mark_task_started()`

```python
def mark_task_started(task_type: str, data_type: str, uptime: str):
```

新增的函数，用于在任务开始时立即更新uptime状态：
- 防止同一uptime被重复处理
- 解决并发场景下的竞态条件

## 日志输出示例

启用新功能后，日志会显示重试过程：

```
2025-07-13 13:04:00 - [gz_mpfv3] 开始获取新鲜数据，最大重试时间: 60分钟
2025-07-13 13:04:00 - [gz_mpfv3] 第 1 次尝试获取新鲜数据...
2025-07-13 13:04:01 - [gz_mpfv3] uptime 202507131300 已处理，30秒后重试...
2025-07-13 13:04:31 - [gz_mpfv3] 第 2 次尝试获取新鲜数据...
2025-07-13 13:04:32 - [gz_mpfv3] uptime 202507131300 已处理，30秒后重试...
2025-07-13 13:05:02 - [gz_mpfv3] 第 3 次尝试获取新鲜数据...
2025-07-13 13:05:03 - [gz_mpfv3] 获取到新鲜数据，uptime: 202507131306，总等待时间: 63.2秒
```

## 测试

### 功能测试

运行测试脚本验证功能：

```bash
python test_uptime_retry.py
```

测试脚本会验证：
1. API基本功能
2. 首次运行逻辑
3. 重试逻辑（会实际等待和重试）
4. 新的状态更新逻辑
5. 禁用功能时的行为

### 演示脚本

运行演示脚本了解新逻辑：

```bash
python demo_new_uptime_logic.py
```

演示脚本会展示：
1. 旧逻辑 vs 新逻辑的对比
2. 并发场景下的改进
3. 重试逻辑的工作流程
4. 配置选项说明

## 性能考虑

1. **资源占用**: 重试过程中会占用一个异步任务，但不会阻塞其他任务
2. **网络请求**: 会增加API调用次数，但有最大重试时间限制
3. **内存使用**: 内存占用很小，主要是任务状态信息

## 兼容性

- **向后兼容**: 如果不提供新参数，函数行为与之前完全相同
- **配置兼容**: 如果配置文件中没有新选项，使用默认值
- **禁用功能**: 可以通过设置 `retry_until_fresh: false` 禁用新功能

## 注意事项

1. **超时设置**: 建议根据数据更新频率调整最大重试时间
2. **重试间隔**: 30秒的重试间隔是平衡及时性和API压力的结果
3. **监控**: 建议监控重试次数和成功率，以优化配置参数
