#!/usr/bin/env python3
"""
行政区数据加载脚本测试

测试行政区数据加载功能，包括：
1. 创建测试用的geojson文件
2. 测试数据加载流程
3. 验证数据库中的数据

作者：Weather Script Team
日期：2025-01-18
"""

import json
import logging
import tempfile
import unittest
from pathlib import Path
from unittest.mock import patch, MagicMock

import geopandas as gpd
import pandas as pd
from shapely.geometry import Polygon

# 设置路径以导入src模块
import sys
sys.path.insert(0, str(Path(__file__).parent.parent / "src"))

from load_administrative_regions import AdministrativeRegionLoader

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class TestAdministrativeRegionLoader(unittest.TestCase):
    """行政区数据加载器测试类"""
    
    def setUp(self):
        """测试前准备"""
        self.temp_dir = Path(tempfile.mkdtemp())
        self.city_file = self.temp_dir / "city_test.geojson"
        self.county_file = self.temp_dir / "county_test.geojson"
        
        # 创建测试用的geojson数据
        self._create_test_geojson_files()
    
    def tearDown(self):
        """测试后清理"""
        # 清理临时文件
        import shutil
        shutil.rmtree(self.temp_dir, ignore_errors=True)
    
    def _create_test_geojson_files(self):
        """创建测试用的geojson文件"""
        # 创建市级测试数据
        city_data = {
            "type": "FeatureCollection",
            "features": [
                {
                    "type": "Feature",
                    "properties": {
                        "name": "昆明市",
                        "gb": "53001000100"  # 测试：去掉前3个(530)和后2个(00)，结果应该是010001
                    },
                    "geometry": {
                        "type": "Polygon",
                        "coordinates": [[
                            [102.0, 25.0],
                            [103.0, 25.0],
                            [103.0, 26.0],
                            [102.0, 26.0],
                            [102.0, 25.0]
                        ]]
                    }
                },
                {
                    "type": "Feature",
                    "properties": {
                        "name": "大理市",
                        "gb": "53002000200"  # 测试：去掉前3个(530)和后2个(00)，结果应该是020002
                    },
                    "geometry": {
                        "type": "Polygon",
                        "coordinates": [[
                            [100.0, 25.5],
                            [101.0, 25.5],
                            [101.0, 26.5],
                            [100.0, 26.5],
                            [100.0, 25.5]
                        ]]
                    }
                }
            ]
        }
        
        # 创建县级测试数据
        county_data = {
            "type": "FeatureCollection",
            "features": [
                {
                    "type": "Feature",
                    "properties": {
                        "name": "五华区",
                        "gb": "530010101"  # 测试：去掉前3个(530)，结果应该是010101
                    },
                    "geometry": {
                        "type": "Polygon",
                        "coordinates": [[
                            [102.1, 25.1],
                            [102.5, 25.1],
                            [102.5, 25.5],
                            [102.1, 25.5],
                            [102.1, 25.1]
                        ]]
                    }
                },
                {
                    "type": "Feature",
                    "properties": {
                        "name": "盘龙区",
                        "gb": "530010102"  # 测试：去掉前3个(530)，结果应该是010102
                    },
                    "geometry": {
                        "type": "Polygon",
                        "coordinates": [[
                            [102.5, 25.1],
                            [102.9, 25.1],
                            [102.9, 25.5],
                            [102.5, 25.5],
                            [102.5, 25.1]
                        ]]
                    }
                }
            ]
        }
        
        # 写入文件
        with open(self.city_file, 'w', encoding='utf-8') as f:
            json.dump(city_data, f, ensure_ascii=False, indent=2)
        
        with open(self.county_file, 'w', encoding='utf-8') as f:
            json.dump(county_data, f, ensure_ascii=False, indent=2)
        
        logger.info(f"创建测试文件: {self.city_file}")
        logger.info(f"创建测试文件: {self.county_file}")
    
    def test_process_geo_code(self):
        """测试geo_code处理逻辑"""
        loader = AdministrativeRegionLoader(str(self.city_file), str(self.county_file))
        
        # 测试市级处理
        self.assertEqual(loader._process_geo_code("53001000100", "city"), "010001")
        self.assertEqual(loader._process_geo_code("53002000200", "city"), "020002")
        
        # 测试县级处理
        self.assertEqual(loader._process_geo_code("530010101", "county"), "010101")
        self.assertEqual(loader._process_geo_code("530010102", "county"), "010102")
        
        # 测试边界情况
        self.assertEqual(loader._process_geo_code("12345", "city"), "45")   # 长度刚好5，去掉前3个字符
        self.assertEqual(loader._process_geo_code("123", "county"), "123")  # 长度不足，返回原值
        self.assertEqual(loader._process_geo_code("", "city"), "")          # 空字符串
        self.assertEqual(loader._process_geo_code(None, "county"), "")      # None值
    
    def test_read_and_process_geojson(self):
        """测试geojson文件读取和处理"""
        loader = AdministrativeRegionLoader(str(self.city_file), str(self.county_file))
        
        # 测试市级数据读取
        city_gdf = loader._read_and_process_geojson(self.city_file, "city")
        self.assertEqual(len(city_gdf), 2)
        self.assertIn("昆明市", city_gdf['name'].values)
        self.assertIn("大理市", city_gdf['name'].values)
        self.assertIn("010001", city_gdf['geo_code'].values)
        self.assertIn("020002", city_gdf['geo_code'].values)
        self.assertTrue(all(city_gdf['region_type'] == 'city'))
        
        # 测试县级数据读取
        county_gdf = loader._read_and_process_geojson(self.county_file, "county")
        self.assertEqual(len(county_gdf), 2)
        self.assertIn("五华区", county_gdf['name'].values)
        self.assertIn("盘龙区", county_gdf['name'].values)
        self.assertIn("010101", county_gdf['geo_code'].values)
        self.assertIn("010102", county_gdf['geo_code'].values)
        self.assertTrue(all(county_gdf['region_type'] == 'county'))
        
        # 验证坐标系
        self.assertEqual(str(city_gdf.crs), 'EPSG:4326')
        self.assertEqual(str(county_gdf.crs), 'EPSG:4326')
    
    @patch('load_administrative_regions.create_engine')
    def test_database_operations(self, mock_create_engine):
        """测试数据库操作（模拟）"""
        # 模拟数据库连接
        mock_engine = MagicMock()
        mock_create_engine.return_value = mock_engine
        mock_conn = MagicMock()
        mock_engine.connect.return_value.__enter__.return_value = mock_conn
        mock_engine.begin.return_value.__enter__.return_value = mock_conn
        
        loader = AdministrativeRegionLoader(str(self.city_file), str(self.county_file))
        
        # 测试数据库连接
        loader._connect_database()
        self.assertIsNotNone(loader.engine)
        
        # 测试表创建
        loader._create_table_and_indexes()
        mock_conn.execute.assert_called()
    
    def test_file_validation(self):
        """测试文件验证"""
        # 测试正常文件
        loader = AdministrativeRegionLoader(str(self.city_file), str(self.county_file))
        loader._validate_files()  # 应该不抛出异常
        
        # 测试不存在的文件
        loader_invalid = AdministrativeRegionLoader("nonexistent1.geojson", "nonexistent2.geojson")
        with self.assertRaises(FileNotFoundError):
            loader_invalid._validate_files()


def create_sample_data():
    """创建示例数据文件"""
    data_dir = Path(__file__).parent.parent / "data" / "administrative"
    data_dir.mkdir(parents=True, exist_ok=True)
    
    # 创建示例市级数据
    city_sample = {
        "type": "FeatureCollection",
        "features": [
            {
                "type": "Feature",
                "properties": {
                    "name": "昆明市",
                    "gb": "53001000100"
                },
                "geometry": {
                    "type": "Polygon",
                    "coordinates": [[
                        [102.0, 25.0], [103.0, 25.0], [103.0, 26.0], [102.0, 26.0], [102.0, 25.0]
                    ]]
                }
            }
        ]
    }
    
    # 创建示例县级数据
    county_sample = {
        "type": "FeatureCollection",
        "features": [
            {
                "type": "Feature",
                "properties": {
                    "name": "五华区",
                    "gb": "530010101"
                },
                "geometry": {
                    "type": "Polygon",
                    "coordinates": [[
                        [102.1, 25.1], [102.5, 25.1], [102.5, 25.5], [102.1, 25.5], [102.1, 25.1]
                    ]]
                }
            }
        ]
    }
    
    # 写入示例文件
    city_file = data_dir / "city_regions.geojson"
    county_file = data_dir / "county_regions.geojson"
    
    with open(city_file, 'w', encoding='utf-8') as f:
        json.dump(city_sample, f, ensure_ascii=False, indent=2)
    
    with open(county_file, 'w', encoding='utf-8') as f:
        json.dump(county_sample, f, ensure_ascii=False, indent=2)
    
    print(f"✅ 创建示例数据文件:")
    print(f"   市级: {city_file}")
    print(f"   县级: {county_file}")


if __name__ == "__main__":
    import argparse
    
    parser = argparse.ArgumentParser(description="行政区数据加载测试")
    parser.add_argument("--create-sample", action="store_true", help="创建示例数据文件")
    parser.add_argument("--run-tests", action="store_true", help="运行单元测试")
    
    args = parser.parse_args()
    
    if args.create_sample:
        create_sample_data()
    elif args.run_tests:
        unittest.main(argv=[''])
    else:
        print("用法:")
        print("  创建示例数据: python test_administrative_regions.py --create-sample")
        print("  运行测试: python test_administrative_regions.py --run-tests")
