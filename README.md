# Weather Script 项目

天气数据处理和分析项目，支持异步处理、地理空间数据分析和多种数据库操作。

## 快速开始

### 安装依赖
```bash
# 安装uv包管理器
pip install uv

# 安装项目依赖
uv sync

# 验证安装
uv run python tests/test_dependencies.py
```

### GDAL依赖
GDAL及相关地理空间依赖已预配置并可通过uv安装：

```bash
# 验证GDAL及地理空间依赖安装
uv run python tests/test_gdal_dependencies.py

# 如需重新安装GDAL (从本地wheel文件)
uv add wheels/gdal-3.10.2-cp312-cp312-win_amd64.whl
```

**注意**: 项目包含预编译的GDAL wheel文件，适用于Windows x64 + Python 3.12。

## 主要脚本

### 核心模块 (src/)
- `weather_download.py` - 天气数据下载 + NetCDF处理公共函数
- `weather_cell_1h.py` - 1小时天气数据处理 (重命名)
- `weather_cell_6m.py` - 6分钟天气数据处理 (重命名)
- `weather_common.py` - 公共函数和类
- `build_lookup.py` - 构建地理空间查找表
- `create_forecast_tables.py` - 创建预报数据表
- `config.py` - 统一配置管理

### 定时任务调度器 (新增)
- `weather_scheduler_app.py` - 基于FastAPI的定时任务调度器
- `scheduler.py` - 任务调度核心逻辑
- `task_manager.py` - 任务队列和执行管理
- `resource_manager.py` - 统一资源池管理
- `uptime_checker.py` - 数据时效性检查

### 启动脚本 (根目录)
- `run_weather_1h.py` - 1小时数据处理启动脚本
- `run_weather_6m.py` - 6分钟数据处理启动脚本
- `run_download.py` - 数据下载启动脚本
- `run_weather_scheduler.py` - **定时任务调度器启动脚本 (推荐)**

## 使用方法

### 🚀 推荐：使用定时任务调度器（自动化）

```bash
# 1. 验证配置文件（推荐）
uv run python validate_config.py

# 2. 启动定时任务调度器
uv run python run_weather_scheduler.py

# 调度器将自动执行：
# - 1小时任务：每小时第4分钟执行（PRE、TEM、WEATHER、VIS）
# - 6分钟任务：每6分钟+4分钟执行（0,6,12...分钟后的第4分钟）
# - 自动检查数据时效性，支持重试机制
# - Web监控界面：http://localhost:8000
```

### 手动运行单个任务

```bash
# 使用启动脚本运行
uv run python run_weather_1h.py    # 1小时数据处理
uv run python run_weather_6m.py    # 6分钟数据处理
uv run python run_download.py      # 数据下载

# 直接运行模块
uv run python -m src.weather_cell_1h
uv run python -m src.weather_cell_6m
uv run python -m src.weather_download

# 运行测试
uv run python validate_config.py   # 验证配置文件
uv run python tests/test_dependencies.py
uv run python tests/test_weather_common.py
uv run python tests/test_new_config.py
uv run python test_scheduler.py    # 测试调度器系统
uv run python demo_scheduler.py    # 演示调度器功能
uv run python demo_task_state_management.py # 演示任务状态管理
uv run python demo_simplified_system.py # 演示简化后的系统
uv run python test_task_state_integration.py # 测试任务状态管理
```

## 项目结构

```
weather_script/
├── src/                  # 源代码目录
│   ├── __init__.py       # 包初始化
│   ├── config.py         # 配置管理
│   ├── weather_common.py # 公共模块
│   ├── weather_download.py # 数据下载
│   ├── weather_cell_1h.py  # 1小时数据处理
│   ├── weather_cell_6m.py  # 6分钟数据处理
│   ├── build_lookup.py   # 查找表构建
│   ├── create_forecast_tables.py # 预报表创建
│   ├── weather_scheduler_app.py # 定时任务调度器应用
│   ├── scheduler.py      # 任务调度核心逻辑
│   ├── task_manager.py   # 任务队列和执行管理
│   ├── resource_manager.py # 统一资源池管理
│   ├── uptime_checker.py # 数据时效性检查
│   └── task_state_manager.py # 任务状态管理
├── tests/                # 测试文件
│   ├── test_dependencies.py
│   ├── test_weather_common.py
│   ├── test_new_config.py # 新配置测试
│   └── test_gdal_dependencies.py # GDAL依赖测试
├── docs/                 # 文档
│   ├── README.md         # 详细文档
│   ├── DEPENDENCIES.md   # 依赖说明
│   ├── REFACTORING_GUIDE.md # 重构指南
│   └── SCHEDULER_GUIDE.md # 调度器使用指南
├── wheels/               # 本地wheel文件
│   ├── gdal-3.10.2-cp312-cp312-win_amd64.whl
│   └── README.md         # wheels目录说明
├── run_weather_1h.py     # 1小时处理启动脚本
├── run_weather_6m.py     # 6分钟处理启动脚本
├── run_download.py       # 下载启动脚本
├── run_weather_scheduler.py # 定时任务调度器启动脚本
├── test_scheduler.py     # 调度器测试脚本
├── demo_scheduler.py     # 调度器演示脚本
├── demo_task_state_management.py # 任务状态管理演示脚本
├── demo_simplified_system.py # 简化系统演示脚本
├── test_task_state_integration.py # 任务状态管理集成测试
├── validate_config.py    # 配置验证脚本
├── config.yml            # 统一配置文件（包含所有模块配置）
└── pyproject.toml        # 项目配置
```

## 🎯 定时任务调度器特性

### 智能调度
- **1小时任务**：每小时第4分钟执行（01:04, 02:04, 03:04...）
- **6分钟任务**：每6分钟+4分钟执行（04, 10, 16, 22, 28, 34, 40, 46, 52, 58分钟）
- **数据类型**：1h任务支持PRE、TEM、WEATHER、VIS四种；6m任务支持gz_mpfv3

### 数据时效性保障
- **uptime检查**：确保数据在5分钟内更新
- **智能重试**：数据不新鲜时每30秒重试一次
- **最大等待**：最多等待60分钟获取新鲜数据

### 任务管理
- **防重复执行**：相同任务不会重复执行
- **队列管理**：后续任务等待前面任务完成
- **超时控制**：6m任务10分钟超时，1h任务30分钟超时
- **自动杀死**：超时任务自动终止，防止假死
- **状态跟踪**：内存中记录每个任务的最后处理uptime
- **智能跳过**：已处理的uptime数据自动跳过

### 资源优化
- **统一资源池**：连接池、线程池、进程池统一管理
- **高并发支持**：支持多任务并发执行
- **内存优化**：智能资源回收和管理

### 监控和管理
- **Web界面**：http://localhost:8000 提供实时监控
- **REST API**：完整的任务状态查询和管理接口
- **日志记录**：详细的执行日志和错误追踪

## 详细文档

- [完整使用指南](docs/README.md)
- [依赖说明](docs/DEPENDENCIES.md)
- [代码重构指南](docs/REFACTORING_GUIDE.md) - 了解如何使用公共模块
- [调度器使用指南](docs/SCHEDULER_GUIDE.md) - **定时任务调度器详细说明**

## 配置

### 主配置文件: config.yml

项目使用YAML格式的配置文件，支持以下配置：

#### 天气数据处理配置
```yaml
weather_processing:
  # 6分钟天气数据处理
  weather_6m:
    data_source:
      data_type: "gz_mpfv3"
      variable_name: "WEATHER"
    processing:
      batch_size: 1000
      max_concurrent_db: 20
    database:
      target_table: "weather_cell_6m"

  # 1小时天气数据处理
  weather_1h:
    data_source:
      data_types:
        PRE: "gz_didiforecast1hPRE"
        TEM: "gz_didiforecast1hTEM"
        WEATHER: "gz_didiforecast1hWEATHER"
        VIS: "gz_didiforecast1hVIS"
    processing:
      batch_size: 500
      max_concurrent_db: 15
      priority_order: ["PRE", "TEM", "WEATHER", "VIS"]
    database:
      target_table: "weather_cell_1h"
```

编辑 `config.yml` 配置数据库连接、文件路径和处理参数。

## 许可证

[在此添加许可证信息]
