# 行政区数据加载脚本

## 概述

`load_administrative_regions.py` 脚本用于读取市级和县级行政区的GeoJSON文件，处理地理编码，并将数据存储到PostgreSQL数据库中。

## 功能特性

- ✅ 读取市级和县级行政区GeoJSON文件
- ✅ 自动处理geo_code字段（根据区域类型进行不同的编码转换）
- ✅ 支持坐标系转换（自动转换为WGS84）
- ✅ 数据验证和错误处理
- ✅ 数据库表自动创建和索引优化
- ✅ 支持配置文件和命令行参数两种使用方式
- ✅ 完整的日志记录和进度跟踪

## 数据处理规则

### geo_code字段处理

脚本会根据区域类型对原始GB代码进行不同的处理：

- **市级行政区**: 去掉前3个字符和后2个字符
  - 例如: `53001000100` → `01001`
- **县级行政区**: 去掉前3个字符
  - 例如: `530010101` → `010101`

### 数据库表结构

创建的 `administrative_regions` 表包含以下字段：

| 字段名 | 类型 | 说明 |
|--------|------|------|
| id | BIGSERIAL | 自增主键 |
| name | VARCHAR(100) | 行政区名称 |
| geo_code | VARCHAR(20) | 地理编码（唯一） |
| region_type | VARCHAR(10) | 区域类型（city/county） |
| original_gb | VARCHAR(20) | 原始GB代码 |
| shape | GEOMETRY | 几何形状（WGS84坐标系） |
| created_at | TIMESTAMP | 创建时间 |
| updated_at | TIMESTAMP | 更新时间 |

### 索引

脚本会自动创建以下索引以优化查询性能：

- `idx_administrative_regions_geo_code`: geo_code字段索引
- `idx_administrative_regions_region_type`: region_type字段索引
- `idx_administrative_regions_name`: name字段索引
- `idx_administrative_regions_shape`: 空间几何索引（GIST）

## 配置

### 配置文件设置

在 `config.yml` 中添加行政区文件路径配置：

```yaml
paths:
  # 行政区geojson文件配置
  administrative_regions:
    city_geojson: "./data/administrative/city_regions.geojson"
    county_geojson: "./data/administrative/county_regions.geojson"
```

### GeoJSON文件要求

输入的GeoJSON文件必须包含以下字段：

- `name`: 行政区名称
- `gb`: 原始GB代码
- `geometry`: 几何形状（支持Polygon、MultiPolygon等）

示例GeoJSON结构：

```json
{
  "type": "FeatureCollection",
  "features": [
    {
      "type": "Feature",
      "properties": {
        "name": "昆明市",
        "gb": "53001000100"
      },
      "geometry": {
        "type": "Polygon",
        "coordinates": [[[102.0, 25.0], [103.0, 25.0], [103.0, 26.0], [102.0, 26.0], [102.0, 25.0]]]
      }
    }
  ]
}
```

## 使用方法

### 方法1: 使用配置文件默认路径

```bash
# 使用config.yml中配置的默认文件路径
uv run python src/load_administrative_regions.py
```

### 方法2: 指定文件路径

```bash
# 指定具体的GeoJSON文件路径
uv run python src/load_administrative_regions.py ./data/city_regions.geojson ./data/county_regions.geojson
```

## 测试

### 创建示例数据

```bash
# 创建示例GeoJSON文件用于测试
uv run python tests/test_administrative_regions.py --create-sample
```

### 运行单元测试

```bash
# 运行完整的单元测试套件
uv run python tests/test_administrative_regions.py --run-tests
```

### 手动测试流程

1. 创建示例数据：
   ```bash
   uv run python tests/test_administrative_regions.py --create-sample
   ```

2. 运行脚本加载数据：
   ```bash
   uv run python src/load_administrative_regions.py
   ```

3. 验证数据库中的数据：
   ```sql
   -- 查看总记录数
   SELECT COUNT(*) FROM administrative_regions;
   
   -- 按类型统计
   SELECT region_type, COUNT(*) FROM administrative_regions GROUP BY region_type;
   
   -- 查看具体数据
   SELECT name, geo_code, region_type, original_gb FROM administrative_regions LIMIT 10;
   ```

## 日志输出

脚本运行时会输出详细的日志信息：

```
2025-01-18 10:30:00 - load_administrative_regions - INFO - 🚀 开始加载行政区数据...
2025-01-18 10:30:00 - load_administrative_regions - INFO - ✅ 输入文件验证通过
2025-01-18 10:30:01 - load_administrative_regions - INFO - ✅ 数据库连接成功
2025-01-18 10:30:01 - load_administrative_regions - INFO - 📖 读取city级行政区文件: ./data/administrative/city_regions.geojson
2025-01-18 10:30:01 - load_administrative_regions - INFO -    原始数据: 16 条记录
2025-01-18 10:30:01 - load_administrative_regions - INFO -    处理完成: 16 条有效记录
2025-01-18 10:30:01 - load_administrative_regions - INFO - 📖 读取county级行政区文件: ./data/administrative/county_regions.geojson
2025-01-18 10:30:02 - load_administrative_regions - INFO -    原始数据: 129 条记录
2025-01-18 10:30:02 - load_administrative_regions - INFO -    处理完成: 129 条有效记录
2025-01-18 10:30:02 - load_administrative_regions - INFO - 🔄 合并市级和县级数据...
2025-01-18 10:30:02 - load_administrative_regions - INFO -    合并后总计: 145 条记录
2025-01-18 10:30:02 - load_administrative_regions - INFO - 🔧 创建数据库表和索引...
2025-01-18 10:30:03 - load_administrative_regions - INFO - ✅ 表和索引创建成功
2025-01-18 10:30:03 - load_administrative_regions - INFO - 💾 保存 145 条记录到数据库...
2025-01-18 10:30:04 - load_administrative_regions - INFO - ✅ 数据保存成功
2025-01-18 10:30:04 - load_administrative_regions - INFO - 📊 数据库中共有 145 条记录
2025-01-18 10:30:04 - load_administrative_regions - INFO -    city: 16 条
2025-01-18 10:30:04 - load_administrative_regions - INFO -    county: 129 条
2025-01-18 10:30:04 - load_administrative_regions - INFO - 🎉 行政区数据加载完成！
```

## 错误处理

脚本包含完善的错误处理机制：

- **文件不存在**: 检查文件路径是否正确
- **数据格式错误**: 验证GeoJSON文件格式和必需字段
- **数据库连接失败**: 检查数据库配置和网络连接
- **数据处理错误**: 跳过无效记录并记录警告日志

## 性能优化

- 使用批量插入操作提高数据库写入性能
- 创建适当的索引优化查询性能
- 支持大文件处理，内存使用优化
- 使用事务确保数据一致性

## 注意事项

1. **数据替换**: 每次运行脚本都会完全替换表中的数据
2. **坐标系**: 自动转换为WGS84坐标系（EPSG:4326）
3. **编码处理**: 确保GeoJSON文件使用UTF-8编码
4. **数据库权限**: 确保数据库用户有创建表和索引的权限
5. **文件大小**: 对于大文件，建议在服务器上运行以获得更好的性能

## 故障排除

### 常见问题

1. **ImportError**: 确保已安装所有依赖包
   ```bash
   uv sync
   ```

2. **数据库连接失败**: 检查config.yml中的数据库配置

3. **文件读取失败**: 确保GeoJSON文件格式正确且包含必需字段

4. **几何数据无效**: 检查GeoJSON中的几何数据是否有效

### 调试模式

可以通过修改日志级别来获得更详细的调试信息：

```python
logging.basicConfig(level=logging.DEBUG)
```
