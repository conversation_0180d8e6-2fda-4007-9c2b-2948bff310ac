# Weather Script 项目依赖管理

## 概述

本项目已使用 `uv` 包管理器来管理所有 Python 依赖。所有依赖都已在 `pyproject.toml` 文件中定义并成功安装。

## 已安装的依赖

### 核心依赖

以下是项目运行所必需的包：

#### 地理空间数据处理
- **geopandas** (1.1.1) - 地理空间数据处理
- **shapely** (2.1.1) - 几何对象处理
- **pyogrio** (0.11.0) - 高性能地理空间I/O
- **pyproj** (3.7.1) - 投影转换

#### 数据库相关
- **sqlalchemy** (2.0.41) - SQL工具包和ORM
- **geoalchemy2** (0.17.1) - SQLAlchemy的地理空间扩展
- **asyncpg** (0.30.0) - PostgreSQL异步驱动
- **pymysql** (1.1.1) - MySQL驱动

#### 数据处理
- **pandas** (2.3.1) - 数据分析库
- **numpy** (2.3.1) - 数值计算库
- **xarray** (2025.7.1) - 多维数组处理（NetCDF文件）

#### 网络请求和文件处理
- **requests** (2.32.4) - HTTP请求库
- **pyyaml** (6.0.2) - YAML配置文件解析

#### 支持库
- **python-dateutil** (2.9.0.post0) - 日期时间处理
- **pytz** (2025.2) - 时区处理
- **tzdata** (2025.2) - 时区数据
- **typing-extensions** (4.14.1) - 类型提示扩展
- **greenlet** (3.2.3) - 协程支持

### 开发依赖

以下是开发和测试所需的包：

- **ruff** - Python代码检查和格式化
- **pytest** - 测试框架
- **pytest-asyncio** - 异步测试支持

## 手动安装的依赖

以下依赖需要手动安装，因为它们依赖于系统级的GDAL库：

### GDAL相关依赖
- **GDAL** - 地理空间数据抽象库
- **Fiona** - 矢量数据I/O（依赖GDAL）
- **rasterio** - 栅格数据处理（如果需要）

### 安装GDAL的建议方法

1. **使用Conda（推荐）**：
   ```bash
   conda install -c conda-forge gdal fiona
   ```

2. **使用系统包管理器**：
   - Windows: 使用OSGeo4W或GDAL官方安装包
   - Ubuntu/Debian: `sudo apt-get install gdal-bin libgdal-dev`
   - macOS: `brew install gdal`

3. **然后安装Python绑定**：
   ```bash
   uv add gdal fiona
   ```

## 项目文件分析

### Python文件及其依赖

1. **weather_download.py**
   - requests (HTTP请求)
   - pathlib (路径处理)
   - logging (日志记录)
   - tarfile (压缩文件处理)
   - json (JSON处理)

2. **build_lookup.py**
   - geopandas (地理空间数据)
   - pandas (数据处理)
   - shapely (几何处理)
   - sqlalchemy (数据库操作)
   - geoalchemy2 (地理空间数据库)

3. **weather_cell_1h_async_hybrid.py**
   - numpy (数值计算)
   - pandas (数据处理)
   - xarray (NetCDF文件处理)
   - sqlalchemy (数据库)
   - asyncpg (异步PostgreSQL)
   - multiprocessing (多进程)

4. **weather_cell_6m_async_hybrid.py**
   - 类似于1h版本的依赖

5. **create_forecast_tables.py**
   - sqlalchemy (数据库表创建)

6. **config.py**
   - yaml (配置文件解析)
   - urllib.parse (URL处理)
   - pathlib (路径处理)

## 使用说明

### 安装依赖

1. 确保已安装 `uv`：
   ```bash
   pip install uv
   ```

2. 安装项目依赖：
   ```bash
   uv sync
   ```

3. 手动安装GDAL相关依赖（见上文）

### 验证安装

运行以下命令验证主要依赖是否正确安装：

```bash
python -c "import geopandas, sqlalchemy, geoalchemy2, asyncpg, pymysql, pandas, numpy, xarray, requests, yaml; print('所有主要依赖导入成功！')"
```

### 开发环境

激活开发依赖：
```bash
uv sync --group dev
```

## 注意事项

1. **GDAL依赖**：由于GDAL的复杂性，建议使用conda或系统包管理器安装
2. **版本兼容性**：所有版本都经过测试，确保相互兼容
3. **虚拟环境**：uv会自动管理虚拟环境，无需手动创建
4. **锁定文件**：`uv.lock` 文件确保依赖版本的一致性

## 故障排除

如果遇到导入错误：

1. 检查GDAL是否正确安装
2. 确认虚拟环境已激活
3. 重新运行 `uv sync`
4. 检查系统环境变量（特别是GDAL相关）

## 更新依赖

更新所有依赖到最新版本：
```bash
uv sync --upgrade
```

更新特定依赖：
```bash
uv add package_name@latest
```
