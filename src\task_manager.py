#!/usr/bin/env python3
# coding: utf-8
"""
任务管理器
管理任务队列、防重复执行、超时控制等
"""

import asyncio
import logging
import signal
import subprocess
import sys
from datetime import datetime, timedelta
from typing import Dict, Set, Optional, Any, Callable, Awaitable
from enum import Enum
from dataclasses import dataclass, field
import threading
import psutil
import os

logger = logging.getLogger(__name__)


class TaskStatus(Enum):
    """任务状态枚举"""
    PENDING = "pending"      # 等待执行
    RUNNING = "running"      # 正在执行
    COMPLETED = "completed"  # 执行完成
    FAILED = "failed"        # 执行失败
    TIMEOUT = "timeout"      # 执行超时
    CANCELLED = "cancelled"  # 已取消


@dataclass
class TaskInfo:
    """任务信息"""
    task_id: str
    task_type: str  # "1h" 或 "6m"
    data_type: str  # 对于1h任务：PRE/TEM/WEATHER/VIS，对于6m任务：gz_mpfv3
    scheduled_time: datetime
    status: TaskStatus = TaskStatus.PENDING
    start_time: Optional[datetime] = None
    end_time: Optional[datetime] = None
    process_id: Optional[int] = None
    error_message: Optional[str] = None
    retry_count: int = 0
    max_retries: int = 3
    timeout_minutes: int = 30  # 默认超时时间
    
    def __post_init__(self):
        """设置任务特定的超时时间"""
        if self.task_type == "6m":
            self.timeout_minutes = 10  # 6分钟任务10分钟超时
        elif self.task_type == "1h":
            self.timeout_minutes = 30  # 1小时任务30分钟超时
    
    @property
    def duration(self) -> Optional[timedelta]:
        """任务执行时长"""
        if self.start_time and self.end_time:
            return self.end_time - self.start_time
        elif self.start_time:
            return datetime.now() - self.start_time
        return None
    
    @property
    def is_timeout(self) -> bool:
        """是否超时"""
        if self.start_time and self.status == TaskStatus.RUNNING:
            duration = datetime.now() - self.start_time
            return duration.total_seconds() > self.timeout_minutes * 60
        return False


class TaskManager:
    """任务管理器"""
    
    def __init__(self):
        self._tasks: Dict[str, TaskInfo] = {}
        self._running_tasks: Set[str] = set()
        self._task_queues: Dict[str, asyncio.Queue] = {
            "1h_PRE": asyncio.Queue(),
            "1h_TEM": asyncio.Queue(),
            "1h_WEATHER": asyncio.Queue(),
            "1h_VIS": asyncio.Queue(),
            "6m": asyncio.Queue()
        }
        self._lock = asyncio.Lock()
        self._shutdown = False
        
        # 启动监控任务
        self._monitor_task = None
    
    def _generate_task_id(self, task_type: str, data_type: str, scheduled_time: datetime) -> str:
        """生成任务ID"""
        time_str = scheduled_time.strftime("%Y%m%d_%H%M")
        return f"{task_type}_{data_type}_{time_str}"
    
    def _get_queue_key(self, task_type: str, data_type: str) -> str:
        """获取队列键"""
        if task_type == "1h":
            return f"1h_{data_type}"
        else:
            return "6m"
    
    async def submit_task(self, task_type: str, data_type: str, scheduled_time: datetime,
                         task_func: Callable[[], Awaitable[Any]]) -> str:
        """
        提交任务到队列
        
        Args:
            task_type: 任务类型 ("1h" 或 "6m")
            data_type: 数据类型
            scheduled_time: 计划执行时间
            task_func: 任务执行函数
        
        Returns:
            任务ID
        """
        task_id = self._generate_task_id(task_type, data_type, scheduled_time)
        
        async with self._lock:
            # 检查是否已存在相同任务
            if task_id in self._tasks:
                existing_task = self._tasks[task_id]
                if existing_task.status in [TaskStatus.PENDING, TaskStatus.RUNNING]:
                    logger.warning(f"任务 {task_id} 已存在且正在执行，跳过重复提交")
                    return task_id
            
            # 创建任务信息
            task_info = TaskInfo(
                task_id=task_id,
                task_type=task_type,
                data_type=data_type,
                scheduled_time=scheduled_time
            )
            
            self._tasks[task_id] = task_info
            
            # 添加到对应队列
            queue_key = self._get_queue_key(task_type, data_type)
            await self._task_queues[queue_key].put((task_id, task_func))
            
            logger.info(f"任务 {task_id} 已提交到队列 {queue_key}")
            return task_id
    
    async def _execute_task(self, task_id: str, task_func: Callable[[], Awaitable[Any]]):
        """执行单个任务"""
        async with self._lock:
            if task_id not in self._tasks:
                logger.error(f"任务 {task_id} 不存在")
                return
            
            task_info = self._tasks[task_id]
            task_info.status = TaskStatus.RUNNING
            task_info.start_time = datetime.now()
            self._running_tasks.add(task_id)
        
        logger.info(f"开始执行任务 {task_id}")
        
        try:
            # 设置超时
            timeout_seconds = task_info.timeout_minutes * 60
            result = await asyncio.wait_for(task_func(), timeout=timeout_seconds)
            
            async with self._lock:
                task_info.status = TaskStatus.COMPLETED
                task_info.end_time = datetime.now()
                self._running_tasks.discard(task_id)
            
            logger.info(f"任务 {task_id} 执行完成，耗时: {task_info.duration}")
            
        except asyncio.TimeoutError:
            async with self._lock:
                task_info.status = TaskStatus.TIMEOUT
                task_info.end_time = datetime.now()
                task_info.error_message = f"任务超时（{task_info.timeout_minutes}分钟）"
                self._running_tasks.discard(task_id)
            
            logger.error(f"任务 {task_id} 执行超时")
            
        except Exception as e:
            async with self._lock:
                task_info.status = TaskStatus.FAILED
                task_info.end_time = datetime.now()
                task_info.error_message = str(e)
                self._running_tasks.discard(task_id)
            
            logger.error(f"任务 {task_id} 执行失败: {e}")
    
    async def _process_queue(self, queue_key: str):
        """处理指定队列的任务"""
        queue = self._task_queues[queue_key]
        
        while not self._shutdown:
            try:
                # 等待任务，设置超时避免无限等待
                task_id, task_func = await asyncio.wait_for(queue.get(), timeout=1.0)
                
                # 检查是否有相同类型的任务正在运行
                async with self._lock:
                    # 对于同一队列，确保串行执行
                    running_in_queue = any(
                        self._tasks[tid].status == TaskStatus.RUNNING 
                        and self._get_queue_key(self._tasks[tid].task_type, self._tasks[tid].data_type) == queue_key
                        for tid in self._running_tasks
                    )
                
                if running_in_queue:
                    # 如果有相同类型任务正在运行，重新放回队列等待
                    await queue.put((task_id, task_func))
                    await asyncio.sleep(5)  # 等待5秒后重试
                    continue
                
                # 执行任务
                await self._execute_task(task_id, task_func)
                
            except asyncio.TimeoutError:
                # 队列为空，继续等待
                continue
            except Exception as e:
                logger.error(f"处理队列 {queue_key} 时发生错误: {e}")
                await asyncio.sleep(1)
    
    async def start_processing(self):
        """启动任务处理"""
        if self._monitor_task is not None:
            logger.warning("任务处理已经启动")
            return
        
        logger.info("启动任务管理器...")
        
        # 启动各队列的处理协程
        tasks = []
        for queue_key in self._task_queues.keys():
            task = asyncio.create_task(self._process_queue(queue_key))
            tasks.append(task)
        
        # 启动监控任务
        monitor_task = asyncio.create_task(self._monitor_tasks())
        tasks.append(monitor_task)
        
        self._monitor_task = asyncio.gather(*tasks, return_exceptions=True)
        
        logger.info("任务管理器启动完成")
    
    async def _monitor_tasks(self):
        """监控任务状态，处理超时任务"""
        while not self._shutdown:
            try:
                async with self._lock:
                    current_time = datetime.now()
                    timeout_tasks = []
                    
                    for task_id in list(self._running_tasks):
                        task_info = self._tasks.get(task_id)
                        if task_info and task_info.is_timeout:
                            timeout_tasks.append(task_id)
                    
                    # 处理超时任务
                    for task_id in timeout_tasks:
                        task_info = self._tasks[task_id]
                        task_info.status = TaskStatus.TIMEOUT
                        task_info.end_time = current_time
                        task_info.error_message = f"任务超时（{task_info.timeout_minutes}分钟）"
                        self._running_tasks.discard(task_id)
                        
                        logger.warning(f"任务 {task_id} 已超时，已标记为超时状态")
                        
                        # 尝试杀死超时进程
                        if task_info.process_id:
                            try:
                                self._kill_process_tree(task_info.process_id)
                                logger.info(f"已杀死超时任务 {task_id} 的进程 {task_info.process_id}")
                            except Exception as e:
                                logger.error(f"杀死超时进程失败: {e}")
                
                await asyncio.sleep(30)  # 每30秒检查一次
                
            except Exception as e:
                logger.error(f"监控任务时发生错误: {e}")
                await asyncio.sleep(10)
    
    def _kill_process_tree(self, pid: int):
        """杀死进程树"""
        try:
            parent = psutil.Process(pid)
            children = parent.children(recursive=True)
            
            # 先杀死子进程
            for child in children:
                try:
                    child.terminate()
                except psutil.NoSuchProcess:
                    pass
            
            # 等待子进程结束
            gone, alive = psutil.wait_procs(children, timeout=5)
            
            # 强制杀死仍存活的子进程
            for p in alive:
                try:
                    p.kill()
                except psutil.NoSuchProcess:
                    pass
            
            # 最后杀死父进程
            try:
                parent.terminate()
                parent.wait(timeout=5)
            except psutil.TimeoutExpired:
                parent.kill()
                
        except psutil.NoSuchProcess:
            logger.warning(f"进程 {pid} 不存在")
        except Exception as e:
            logger.error(f"杀死进程树失败: {e}")
    
    async def stop_processing(self):
        """停止任务处理"""
        logger.info("停止任务管理器...")
        self._shutdown = True

        # 取消所有正在运行的任务
        async with self._lock:
            running_task_ids = list(self._running_tasks)
            for task_id in running_task_ids:
                task_info = self._tasks.get(task_id)
                if task_info and task_info.process_id:
                    try:
                        self._kill_process_tree(task_info.process_id)
                        logger.info(f"强制停止任务 {task_id} 的进程 {task_info.process_id}")
                    except Exception as e:
                        logger.error(f"停止任务 {task_id} 进程失败: {e}")

        if self._monitor_task:
            self._monitor_task.cancel()
            try:
                # 快速等待监控任务结束
                await asyncio.wait_for(self._monitor_task, timeout=1.0)
            except (asyncio.CancelledError, asyncio.TimeoutError):
                pass

        logger.info("任务管理器已停止")
    
    def get_task_status(self, task_id: str) -> Optional[TaskInfo]:
        """获取任务状态"""
        return self._tasks.get(task_id)
    
    def get_all_tasks(self) -> Dict[str, TaskInfo]:
        """获取所有任务"""
        return self._tasks.copy()
    
    def get_running_tasks(self) -> Dict[str, TaskInfo]:
        """获取正在运行的任务"""
        return {tid: self._tasks[tid] for tid in self._running_tasks if tid in self._tasks}
    
    def get_queue_stats(self) -> Dict[str, int]:
        """获取队列统计"""
        return {key: queue.qsize() for key, queue in self._task_queues.items()}


# 全局任务管理器实例
task_manager = TaskManager()
