# 分区错误解决方案

## 问题描述

在运行天气数据处理脚本时，遇到以下错误：

### 1. weather_cell_1h 表分区错误
```
2025-07-23 16:04:02,989 - weather_cell_1h - ERROR - 处理时间步 2025-07-24 19:00:00 时出错: no partition of relation "weather_cell_1h" found for row
DETAIL:  Partition key of the failing row contains (pre_time) = (2025-07-24 19:00:00).
```

### 2. weather_alarm 表分区错误
```
2025-07-23 16:23:48,256 - weather_alarm - ERROR - 新增预警失败: no partition of relation "weather_alarm" found for row
```

## 错误原因

PostgreSQL 分区表缺少对应日期的分区。虽然系统配置了 pg_partman 自动分区管理，但某些日期的分区可能因为以下原因未能及时创建：

1. pg_partman 维护任务尚未运行
2. 系统时间与数据时间不同步
3. 预创建分区数量不足以覆盖所需的时间范围
4. 不同表使用不同的分区策略：
   - **日分区表**：weather_cell_* 和 forecast_precipitation_* 系列表按天分区
   - **月分区表**：weather_alarm 表按月分区

## 解决方案

### 1. 立即解决方案 - 手动创建缺失分区

使用新创建的脚本手动创建缺失的分区（支持日分区和月分区）：

```bash
# 为特定日期创建分区（包括对应的月分区）
cd src
uv run python create_missing_partitions.py --dates 2025-07-23 2025-07-24 2025-07-25

# 为日期范围创建分区（自动处理跨月情况）
uv run python create_missing_partitions.py --start-date 2025-07-23 --days 10

# 创建未来90天的分区（推荐）
uv run python create_missing_partitions.py --start-date 2025-07-23 --days 90

# 检查缺失的分区（不创建）
uv run python create_missing_partitions.py --dates 2025-07-23 2025-07-24 2025-07-25 --check-only
```

**脚本特性**：
- 自动处理11个日分区表（weather_cell_*, forecast_precipitation_*）
- 自动处理1个月分区表（weather_alarm）
- 智能跨月处理，避免重复创建月分区
- 支持检查模式，可预览缺失的分区

### 2. 长期解决方案 - 改进分区管理

#### A. 集成到表创建流程

修改了 `src/create_forecast_tables.py`，在 `create_all_tables()` 函数中添加了自动创建缺失分区的功能：

```python
def create_missing_partitions(engine, start_date=None, days=7):
    """手动创建缺失的分区"""
    # 自动创建未来7天的分区
```

#### B. 监控和检查工具

创建了 `src/check_partman_status.py` 脚本用于：

- 检查 pg_partman 扩展状态
- 验证分区配置
- 检查自动维护任务
- 查看现有分区状态
- 手动运行维护任务

```bash
cd src
uv run python check_partman_status.py
```

## 当前系统状态

通过检查发现系统配置正常：

### 分区管理配置
- ✅ pg_partman 扩展已安装 (版本 5.2.4)
- ✅ 13个表已配置分区管理
- ✅ 每日分区，预创建7天，保留90天
- ✅ 自动维护任务已设置（每天凌晨2点运行）

### 分区状态
- ✅ 已创建110个分区表
- ✅ 覆盖了从2025-07-23到2025-08-01的日期范围
- ✅ 所有核心表都有对应分区

## 预防措施

### 1. 定期检查
建议定期运行分区状态检查：

```bash
# 每周检查一次分区状态
cd src
uv run python check_partman_status.py
```

### 2. 监控日志
监控应用日志中的分区相关错误，及时发现问题。

### 3. 增加预创建天数
如果经常遇到分区缺失问题，可以考虑增加预创建天数：

```sql
UPDATE partman.part_config 
SET premake = 14  -- 从7天增加到14天
WHERE parent_table LIKE 'public.weather_cell_%';
```

### 4. 应用启动时检查
可以在应用启动时自动检查并创建必要的分区：

```python
# 在主应用启动时调用
from src.create_forecast_tables import create_missing_partitions
from sqlalchemy import create_engine
from config import PG_URL

engine = create_engine(PG_URL)
create_missing_partitions(engine, days=10)  # 创建未来10天的分区
```

## 工具脚本说明

### create_missing_partitions.py
- 手动创建缺失的分区
- 支持指定日期或日期范围
- 支持检查模式（只检查不创建）

### check_partman_status.py
- 全面检查分区管理系统状态
- 验证配置和维护任务
- 可手动触发维护任务

## 总结

问题已解决，系统现在有：

### 分区覆盖情况
1. ✅ **日分区表**：完整覆盖 2025-07-23 至 2025-10-29（90天）
   - weather_cell_6m, weather_cell_1h
   - forecast_precipitation_* 系列表（9个表）

2. ✅ **月分区表**：完整覆盖 2025年7月至10月
   - weather_alarm 表

### 系统状态
3. ✅ 正常工作的自动分区管理（pg_partman）
4. ✅ 手动创建分区的工具（支持日分区和月分区）
5. ✅ 分区状态监控工具
6. ✅ 每天凌晨2点自动维护任务

### 建议
- 定期使用 `check_partman_status.py` 检查系统状态
- 如遇到新的分区错误，使用 `create_missing_partitions.py` 快速修复
- 监控应用日志，及时发现分区相关问题
