# Ubuntu 系统部署指南

## 问题说明

在使用 systemd 启动 Python 程序时，如果配置文件中使用相对路径（如 `./data/weather`），会导致数据被存储在错误的位置（如 `/root` 目录下），因为 systemd 的工作目录可能与预期不同。

## 解决方案

我们已经修改了 `src/config.py` 和 `config.yml` 文件，实现了基于脚本目录的绝对路径解析：

### 1. 配置文件修改

#### `src/config.py` 主要变更：
- 添加了 `SCRIPT_DIR` 变量，指向项目根目录
- 添加了 `resolve_path()` 函数，自动将相对路径转换为绝对路径
- 修改了 `DATA_PATHS` 和 `NETCDF_CONFIG` 的路径解析逻辑

#### `config.yml` 主要变更：
- 将天气数据目录改为独立的 `/data/weather` 目录
- 保持 GIS 数据使用项目内的相对路径（会自动转换为绝对路径）

### 2. 目录结构建议

```
/data/
├── script/
│   └── weather_script/          # 项目代码目录
│       ├── src/
│       ├── config.yml
│       ├── run_weather_scheduler.py
│       └── .venv/
└── weather/                     # 天气数据目录（独立存储）
    ├── MPF/
    ├── backup/
    ├── precipitation_6min/
    ├── precipitation_1h/
    ├── temperature_1h/
    ├── weather_1h/
    └── visibility_1h/
```

### 3. 部署步骤

#### 步骤 1: 创建目录结构
```bash
# 创建数据目录
sudo mkdir -p /data/weather/{MPF,backup,precipitation_6min,precipitation_1h,temperature_1h,weather_1h,visibility_1h}

# 设置权限
sudo chown -R root:root /data/weather
sudo chmod -R 755 /data/weather
```

#### 步骤 2: 复制 systemd 服务文件
```bash
# 复制服务文件
sudo cp /data/script/weather_script/systemd/weather-scheduler.service /etc/systemd/system/

# 重新加载 systemd 配置
sudo systemctl daemon-reload
```

#### 步骤 3: 创建环境变量文件（可选）
```bash
# 创建 .env 文件
sudo tee /data/script/weather_script/.env << EOF
# 数据库配置（如果需要覆盖 config.yml 中的设置）
# POSTGRES_HOST=***************
# POSTGRES_PORT=5432
# POSTGRES_DATABASE=middle-data-dev
# POSTGRES_USERNAME=root
# POSTGRES_PASSWORD=your_password

# 其他环境变量
PYTHONPATH=/data/script/weather_script/src
EOF
```

#### 步骤 4: 启动服务
```bash
# 启用服务（开机自启）
sudo systemctl enable weather-scheduler.service

# 启动服务
sudo systemctl start weather-scheduler.service

# 查看服务状态
sudo systemctl status weather-scheduler.service
```

### 4. 验证部署

#### 检查服务状态
```bash
# 查看服务状态
sudo systemctl status weather-scheduler.service

# 查看服务日志
sudo journalctl -u weather-scheduler.service -f

# 查看最近的日志
sudo journalctl -u weather-scheduler.service --since "1 hour ago"
```

#### 检查数据目录
```bash
# 检查数据是否存储在正确位置
ls -la /data/weather/

# 检查是否有数据文件生成
find /data/weather/ -name "*.nc" -type f
```

#### 测试路径解析
```bash
# 在项目目录下运行测试脚本
cd /data/script/weather_script
.venv/bin/python test_path_resolution.py
```

### 5. 故障排除

#### 常见问题

1. **服务启动失败**
   ```bash
   # 查看详细错误信息
   sudo journalctl -u weather-scheduler.service --no-pager
   
   # 检查 Python 环境
   /data/script/weather_script/.venv/bin/python --version
   ```

2. **数据存储位置错误**
   ```bash
   # 检查配置文件路径解析
   cd /data/script/weather_script
   .venv/bin/python -c "
   import sys
   sys.path.insert(0, 'src')
   from config import DATA_PATHS
   for k, v in DATA_PATHS.items():
       print(f'{k}: {v}')
   "
   ```

3. **权限问题**
   ```bash
   # 检查目录权限
   ls -la /data/weather/
   
   # 修复权限
   sudo chown -R root:root /data/weather
   sudo chmod -R 755 /data/weather
   ```

### 6. 监控和维护

#### 日志管理
```bash
# 查看实时日志
sudo journalctl -u weather-scheduler.service -f

# 查看特定时间段的日志
sudo journalctl -u weather-scheduler.service --since "2024-01-01" --until "2024-01-02"

# 清理旧日志（保留最近7天）
sudo journalctl --vacuum-time=7d
```

#### 服务管理
```bash
# 重启服务
sudo systemctl restart weather-scheduler.service

# 停止服务
sudo systemctl stop weather-scheduler.service

# 禁用服务
sudo systemctl disable weather-scheduler.service
```

### 7. 配置优化建议

1. **生产环境配置调整**：
   - 在 `config.yml` 中增加数据库连接数、进程数等参数
   - 调整超时时间和重试策略

2. **监控配置**：
   - 配置日志轮转
   - 设置磁盘空间监控
   - 配置服务健康检查

3. **安全配置**：
   - 使用专用用户而非 root 用户运行服务
   - 配置防火墙规则
   - 定期更新依赖包
