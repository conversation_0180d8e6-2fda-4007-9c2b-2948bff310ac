# PostgreSQL 分区表改造总结

## 改造概述

根据分区改造指导原则，对 `create_forecast_tables.py` 中的12个表进行了分区改造，使其支持按时间范围分区以提高查询性能和数据管理效率。

## 改造原则应用

### 原则1：添加 `PARTITION BY RANGE` 子句
- 所有表都在 `CREATE TABLE` 语句末尾添加了 `PARTITION BY RANGE (分区键)`
- 大部分表使用 `pre_time` 作为分区键
- `weather_alarm` 表使用 `publish_time` 作为分区键

### 原则2：修改主键（最重要）
- **移除了所有单独的 `id` 主键**
- **添加了包含分区键的复合主键**
- 确保分区表的主键包含分区键列

### 原则3：修改唯一索引
- `weather_alarm` 表的唯一索引 `idx_weather_alarm_unique` 已修改为包含分区键 `publish_time`

### 原则4：确保分区键非空
- 所有分区键列都标记为 `NOT NULL`

## 具体改造详情

### 方案A: `weather_cell_*` 系列表（2个表）

**适用表:**
- `weather_cell_6m`
- `weather_cell_1h`

**改造内容:**
1. **分区键**: `pre_time`
2. **主键改造**: 
   - 原来: `id BIGSERIAL PRIMARY KEY`
   - 现在: `id BIGSERIAL` + `CONSTRAINT weather_cell_6m_pkey PRIMARY KEY (pre_time, cell_id)`
3. **索引优化**: 移除了原来的唯一索引（因为主键已保证唯一性）
4. **分区子句**: `PARTITION BY RANGE (pre_time)`

### 方案B: `forecast_precipitation_*` 系列表（9个表）

**适用表:**
- `forecast_precipitation_6min_line`
- `forecast_precipitation_6min_polygon` 
- `forecast_precipitation_6min_relation`
- `forecast_precipitation_hourly_line`
- `forecast_precipitation_hourly_polygon`
- `forecast_precipitation_hourly_relation`
- `forecast_precipitation_summary_line`
- `forecast_precipitation_summary_polygon`
- `forecast_precipitation_summary_relation`

**改造内容:**
1. **分区键**: `pre_time`
2. **主键改造**:
   - 原来: `CONSTRAINT "表名_pkey" PRIMARY KEY ("id")`
   - 现在: `CONSTRAINT "表名_pkey" PRIMARY KEY (pre_time, id)`
3. **分区键约束**: `pre_time timestamp(6) NOT NULL`
4. **分区子句**: `PARTITION BY RANGE (pre_time)`

### 方案C: `weather_alarm` 表（1个表）

**改造内容:**
1. **分区键**: `publish_time`
2. **主键改造**:
   - 原来: `"id" BIGSERIAL PRIMARY KEY`
   - 现在: `"id" BIGSERIAL` + `CONSTRAINT "weather_alarm_pkey" PRIMARY KEY (publish_time, id)`
3. **唯一索引改造**:
   - 原来: `CREATE UNIQUE INDEX ... ON ... ("weather_id", "alarm_info")`
   - 现在: `CREATE UNIQUE INDEX ... ON ... (publish_time, "weather_id", "alarm_info")`
4. **分区键约束**: `"publish_time" TIMESTAMP NOT NULL`
5. **分区子句**: `PARTITION BY RANGE (publish_time)`

## 关键变更说明

### 1. 主键结构变更
所有表的主键都从单列 `id` 改为复合主键 `(分区键, id)`，这是PostgreSQL分区表的强制要求。

### 2. 分区键非空约束
所有分区键列都添加了 `NOT NULL` 约束，因为PostgreSQL不允许分区键为NULL。

### 3. 唯一索引调整
`weather_alarm` 表的唯一索引必须包含分区键，以符合PostgreSQL分区表的约束要求。

### 4. 保留原有功能
- `id` 列仍然保留为自增序列，只是不再作为主键
- 所有原有的业务逻辑和字段都保持不变
- 索引结构基本保持不变（除了必要的分区键调整）

## 后续工作

### 1. 安装 pg_partman 扩展（推荐）
为了自动管理分区，强烈建议安装 pg_partman 扩展：
```sql
CREATE EXTENSION pg_partman;
```

### 2. 配置自动分区管理
使用提供的脚本配置 pg_partman：

**方法一：使用 Python 脚本（推荐）**
```bash
python setup_partition_management.py
```

**方法二：使用 SQL 脚本**
```bash
psql -d your_database -f setup_partman.sql
```

### 3. 验证分区设置
运行验证脚本确保配置正确：
```bash
python verify_partman_setup.py
```

### 4. 手动创建分区（如不使用 pg_partman）
如果不使用 pg_partman，需要手动创建具体的分区：
```sql
-- 为 weather_cell_6m 创建日度分区
CREATE TABLE weather_cell_6m_p20250723 PARTITION OF weather_cell_6m
FOR VALUES FROM ('2025-07-23') TO ('2025-07-24');
```

### 5. 应用程序调整
检查应用程序中是否有依赖单列主键的逻辑，需要相应调整。

### 6. 性能测试
在生产环境部署前，建议进行充分的性能测试，验证分区表的查询性能提升。

## 测试验证

使用 `test_partition_tables.py` 脚本可以验证：
1. 表是否正确创建为分区表
2. 主键是否包含分区键
3. 分区键是否设置为NOT NULL
4. 唯一索引是否包含分区键（针对weather_alarm表）

运行测试：
```bash
python test_partition_tables.py
```

## pg_partman 分区管理策略

### 核心数据表（11个表）
- **分区粒度**: 每日 (`1 day`)
- **预创建**: 7天
- **保留策略**: 90天
- **维护频率**: 每天凌晨2点自动运行

### 预警表（weather_alarm）
- **分区粒度**: 每月 (`1 month`)
- **预创建**: 4个月
- **保留策略**: 3年
- **维护频率**: 每天凌晨2点自动运行

### 自动维护
pg_partman 将自动：
1. 根据数据插入创建新分区
2. 提前创建未来的分区
3. 删除过期的分区
4. 维护分区统计信息

## 注意事项

1. **数据迁移**: 如果已有数据，需要制定数据迁移策略
2. **应用兼容性**: 确保应用程序能够适应新的主键结构
3. **分区策略**: 已根据数据量和查询模式优化分区粒度
4. **监控**: 部署后需要监控分区表的性能表现和 pg_partman 日志
5. **扩展依赖**: 确保 pg_partman 和 pg_cron 扩展正常工作
