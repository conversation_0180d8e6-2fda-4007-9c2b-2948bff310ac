#!/usr/bin/env python3

def normalize_xzqh_code(xzqh_str):
    if not xzqh_str:
        return None
    
    code_str = str(xzqh_str).strip()
    if not code_str:
        return None
    
    code_str = code_str.rstrip('0')
    
    if not code_str:
        return None
    
    current_len = len(code_str)
    
    if current_len <= 2: 
        target_len = 2
    elif current_len <= 4:
        target_len = 4
    elif current_len <= 6: 
        target_len = 6
    elif current_len <= 9: 
        target_len = 9
    elif current_len <= 12: 
        target_len = 12
    else:
        target_len = 12
    
    if current_len < target_len:
        code_str = code_str + '0' * (target_len - current_len)
    elif current_len > target_len:
        code_str = code_str[:target_len]
    
    return code_str

# 测试用例
test_cases = [
    ('53', '53'),           # 2位省级，保持2位
    ('530000', '5300'),     # 6位去0后变4位市级，保持4位
    ('5300', '5300'),       # 4位市级，保持4位
    ('530100', '5301'),     # 6位去0后变4位市级，保持4位
    ('530102', '530102'),   # 6位县级，保持6位
    ('530110', '530110'),   # 6位去0后变5位，补充到6位县级
    ('530102000', '530102'), # 9位去0后变6位县级，保持6位
    ('530102001', '530102001'), # 9位乡镇级，保持9位
    ('530102001000', '530102001'), # 12位去0后变9位乡镇级，保持9位
    ('530102001001', '530102001001'), # 12位村级，保持12位
]

print('=== 测试行政区划代码标准化 ===')
for input_code, expected in test_cases:
    result = normalize_xzqh_code(input_code)
    status = '✓' if result == expected else '✗'
    print(f'{status} {input_code} -> {result} (期望: {expected})')
