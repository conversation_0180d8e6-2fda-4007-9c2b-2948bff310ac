# 天气预警信息功能说明

## 功能概述

天气预警信息功能用于从API获取天气预警数据，并存储到PostgreSQL数据库中。该功能每10分钟自动执行一次，支持预警信息的新增、更新和解除。

## 配置说明

### 1. 配置文件 (config.yml)

```yaml
# 预警信息API配置
weather_alarm:
  # API配置
  api_base_url: "http://api.weatherdt.com/common/"
  api_key: "a49adb3feddbfe93dee18b5a64dde1f1"
  type: "alarm"
  
  # 请求参数
  max_areas_per_request: 20  # 每次请求最多20个区域
  request_timeout: 30        # 请求超时时间（秒）
  max_retries: 3            # 最大重试次数
  retry_interval: 5         # 重试间隔（秒）
  
  # 调度配置
  schedule_interval_minutes: 10  # 每10分钟执行一次
```

### 2. 数据库表结构

预警信息存储在 `weather_alarm` 表中，包含以下字段：

- `id`: 主键ID
- `weather_id`: 气象ID（来自sys_region_code表）
- `region_code`: 区划代码（来自sys_region_code表）
- `province_name`: 省级名称
- `city_name`: 市级名称
- `county_name`: 县级名称
- `alarm_category_code`: 预警类别编号
- `alarm_category_name`: 预警类别名称
- `alarm_level_code`: 预警级别编号
- `alarm_level_name`: 预警级别名称
- `publish_time`: 预警发布时间
- `alarm_content`: 预警发布内容
- `alarm_info`: 预警信息（用于唯一标识）
- `jump_url`: 天气网跳转地址
- `alarm_title`: 预警标题
- `publish_status`: 发布状态(Alert/Update)
- `cancel_time`: 预警解除时间
- `create_time`: 创建时间
- `update_time`: 更新时间

## API接口说明

### 请求格式

```
GET http://api.weatherdt.com/common/?type=alarm&key={api_key}&area={weather_ids}
```

参数说明：
- `type`: 固定值 "alarm"
- `key`: API密钥
- `area`: weather_id列表，用"|"分隔，最多20个

### 响应格式

```json
{
  "alarm": {
    "101010100": {
      "1001003": [{
        "001": "北京市",           // 省级名称
        "002": "北京市",           // 市级名称
        "003": "",                // 县级名称
        "004": "07",              // 预警类别编号
        "005": "高温",            // 预警类别名称
        "006": "02",              // 预警级别编号
        "007": "黄色",            // 预警级别名称
        "008": "2019-05-21 16:50", // 预警发布时间
        "009": "市气象台2019年5月21日16时30分发布高温黄色预警信号...", // 预警发布内容
        "010": "201905211650545112高温黄色", // 预警信息
        "011": "10101-20190521165029-0702.html", // 天气网跳转地址
        "012": "北京市发布高温黄色预警",        // 预警标题
        "013": "Alert"            // 发布状态
      }]
    }
  }
}
```

## 错误代码说明

| 错误代码 | 错误说明 | 备注 |
|---------|---------|------|
| CC1000 | 参数错误 | 检查参数拼写 |
| CC1003 | 订单过期 | 请重新购买服务 |
| CC1004 | 访问次数超限 | 请合理访问接口 |
| CC1005 | 站点数过多错误 | 单次访问请少于20个站点 |
| CC1006 | 访问接口路径错误 | 访问接口路径错误 |
| CC1100 | 连接超时 | 请稍后访问 |
| CC1101 | 密钥错误 | 检查密钥 |
| CC1102 | 系统无响应 | 请稍后访问 |
| CC1200 | 请求无效站点错误 | 检查请求站点 |
| CC1201 | 请求站点与要素过多错误 | 检查站点数量或要素数量 |
| CC1300 | 请求要素格式错误 | 检查要素请求格式 |
| CC1301 | 请求未定制类型数据 | 检查定制类型数据 |
| CC1302 | 请求类型定制天数超出错误 | 检查请求类型的定制天数 |
| CC1303 | 请求多站点与指数多要素错误 | 检查站点数量或指数要素数量 |

## 使用方法

### 1. 单独运行预警功能

```bash
uv run python src/weather_alarm.py
```

### 2. 在调度器中运行

预警任务已集成到主调度器中，会每10分钟自动执行一次：

```bash
uv run python src/weather_scheduler_app.py
```

### 3. 测试预警调度器

```bash
uv run python test_alarm_scheduler.py
```

## 处理逻辑

1. **获取区域映射**: 从MySQL的`sys_region_code`表获取`weather_id`和`data_code`的映射关系
2. **分组请求**: 将weather_id按每组20个进行分组，并发请求API
3. **数据处理**: 
   - 新增：如果预警信息不存在，则新增记录
   - 更新：如果预警信息已存在，则更新记录
   - 解除：如果数据库中存在但API中不存在的预警，则设置解除时间
4. **错误处理**: 支持重试机制，记录详细的错误日志

## 监控和日志

- 所有操作都有详细的日志记录
- 支持通过Web界面查看调度器状态
- 可以监控预警任务的执行情况和下次执行时间

## 注意事项

1. API密钥需要有效，且有足够的调用次数
2. 每次请求最多包含20个weather_id
3. 预警信息的唯一性通过`weather_id`和`alarm_info`组合保证
4. 系统会自动处理预警的解除，无需手动操作
