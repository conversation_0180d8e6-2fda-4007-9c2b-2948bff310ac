#!/usr/bin/env python3
# coding: utf-8
"""
天气公共模块测试脚本
测试weather_common.py和weather_download.py中的公共函数
"""

import sys
import os
import logging
from pathlib import Path

# 添加项目根目录和src目录到Python路径
project_root = Path(__file__).parent.parent
src_path = project_root / "src"
sys.path.insert(0, str(project_root))
sys.path.insert(0, str(src_path))

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


def test_weather_common_imports():
    """测试weather_common模块的导入"""
    print("=== 测试weather_common模块导入 ===")
    
    try:
        from weather_common import (
            encode_cell, decode_cell, get_netcdf_indices_from_coords,
            AsyncDatabaseManager, setup_logging, NC_NROWS, NC_NCOLS
        )
        print("✓ weather_common核心函数导入成功")
        
        # 测试格网编码/解码
        test_lon, test_lat = 116.0, 39.0
        cell_id = encode_cell(test_lon, test_lat)
        decoded_lon, decoded_lat = decode_cell(cell_id)
        
        print(f"✓ 格网编码测试: ({test_lon}, {test_lat}) -> {cell_id} -> ({decoded_lon:.4f}, {decoded_lat:.4f})")
        
        # 测试NetCDF索引计算
        nc_lon_min, nc_lat_min = 110.0, 35.0
        lat_idx, lon_idx = get_netcdf_indices_from_coords(test_lon, test_lat, nc_lon_min, nc_lat_min)
        print(f"✓ NetCDF索引计算: ({test_lon}, {test_lat}) -> 索引({lat_idx}, {lon_idx})")
        
        # 测试常量
        print(f"✓ NetCDF网格大小: {NC_NROWS} x {NC_NCOLS}")
        
        return True
        
    except Exception as e:
        print(f"✗ weather_common模块导入失败: {e}")
        return False


def test_weather_download_netcdf_functions():
    """测试weather_download模块中的NetCDF函数"""
    print("\n=== 测试weather_download NetCDF函数 ===")
    
    try:
        from weather_download import (
            read_netcdf_data, extract_netcdf_coordinates, 
            get_netcdf_value_at_coords, validate_netcdf_file
        )
        print("✓ weather_download NetCDF函数导入成功")
        
        # 测试文件验证函数（使用不存在的文件）
        fake_file = "non_existent_file.nc"
        is_valid = validate_netcdf_file(fake_file)
        print(f"✓ 文件验证测试: {fake_file} -> {is_valid} (预期为False)")
        
        return True
        
    except Exception as e:
        print(f"✗ weather_download NetCDF函数导入失败: {e}")
        return False


def test_weather_common_file_functions():
    """测试weather_common模块中的文件处理函数"""
    print("\n=== 测试weather_common文件处理函数 ===")
    
    try:
        from weather_common import get_nc_file_path_single, load_valid_cells
        print("✓ weather_common文件处理函数导入成功")
        
        # 测试加载有效格网（这可能会失败，因为没有数据库连接）
        try:
            valid_cells = load_valid_cells()
            print(f"✓ 加载有效格网成功: {len(valid_cells)} 个格网单元")
        except Exception as e:
            print(f"⚠ 加载有效格网失败（预期，因为没有数据库连接）: {e}")
        
        return True
        
    except Exception as e:
        print(f"✗ weather_common文件处理函数导入失败: {e}")
        return False


def test_async_database_manager():
    """测试异步数据库管理器"""
    print("\n=== 测试异步数据库管理器 ===")
    
    try:
        from weather_common import AsyncDatabaseManager
        print("✓ AsyncDatabaseManager导入成功")
        
        # 创建管理器实例（不连接数据库）
        fake_url = "postgresql://user:pass@localhost/db"
        manager = AsyncDatabaseManager(fake_url)
        print(f"✓ 数据库管理器创建成功: 最大连接数 {manager.max_connections}")
        
        return True
        
    except Exception as e:
        print(f"✗ AsyncDatabaseManager测试失败: {e}")
        return False


def test_config_integration():
    """测试配置集成"""
    print("\n=== 测试配置集成 ===")
    
    try:
        from config import STEP, LON_OFF, LAT_OFF, GRID_SIZE
        print("✓ 配置常量导入成功")
        print(f"  - STEP: {STEP}")
        print(f"  - LON_OFF: {LON_OFF}")
        print(f"  - LAT_OFF: {LAT_OFF}")
        print(f"  - GRID_SIZE: {GRID_SIZE}")
        
        return True
        
    except Exception as e:
        print(f"✗ 配置集成测试失败: {e}")
        return False


def test_weather_downloader_integration():
    """测试天气下载器集成"""
    print("\n=== 测试天气下载器集成 ===")
    
    try:
        from weather_download import WeatherDownloader
        print("✓ WeatherDownloader导入成功")
        
        # 创建下载器实例（使用默认配置）
        try:
            downloader = WeatherDownloader()
            print(f"✓ 下载器创建成功: 数据类型 {downloader.data_type}")
            print(f"  - 基础目录: {downloader.base_dir}")
        except Exception as e:
            print(f"⚠ 下载器创建失败（可能是配置问题）: {e}")
        
        return True
        
    except Exception as e:
        print(f"✗ 天气下载器集成测试失败: {e}")
        return False


def main():
    """主测试函数"""
    print("=" * 60)
    print("Weather Common 模块测试")
    print("=" * 60)
    
    tests = [
        test_weather_common_imports,
        test_weather_download_netcdf_functions,
        test_weather_common_file_functions,
        test_async_database_manager,
        test_config_integration,
        test_weather_downloader_integration,
    ]
    
    passed = 0
    total = len(tests)
    
    for test_func in tests:
        try:
            if test_func():
                passed += 1
        except Exception as e:
            print(f"✗ 测试 {test_func.__name__} 发生异常: {e}")
    
    print("\n" + "=" * 60)
    print("测试结果统计:")
    print("=" * 60)
    print(f"总测试数: {total}")
    print(f"通过测试: {passed}")
    print(f"失败测试: {total - passed}")
    print(f"成功率: {passed/total*100:.1f}%")
    
    if passed == total:
        print("\n🎉 所有测试通过！公共模块功能正常。")
        return 0
    else:
        print(f"\n⚠️ 有 {total - passed} 个测试失败，请检查相关模块。")
        return 1


if __name__ == "__main__":
    try:
        exit_code = main()
        sys.exit(exit_code)
    except KeyboardInterrupt:
        print("\n\n测试被用户中断。")
        sys.exit(1)
    except Exception as e:
        print(f"\n\n测试过程中发生未预期的错误: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)
