#!/usr/bin/env python3
# coding: utf-8
"""
检查MySQL中的区域数据结构
"""

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

import pymysql
from src.config import MYSQL_CONFIG

def check_data():
    """检查数据库中的区域数据"""
    
    try:
        mysql_conn = pymysql.connect(**MYSQL_CONFIG, charset='utf8mb4')
        
        print("检查MySQL区域数据")
        print("=" * 50)
        
        with mysql_conn.cursor() as cursor:
            # 1. 查看数据级别分布
            print("1. 数据级别分布:")
            cursor.execute("""
                SELECT data_level, COUNT(*) as count
                FROM sys_region_code 
                GROUP BY data_level 
                ORDER BY data_level
            """)
            for level, count in cursor.fetchall():
                print(f"   级别 {level}: {count} 条记录")
            
            # 2. 查看sup_code的模式
            print("\n2. sup_code模式分析:")
            cursor.execute("""
                SELECT 
                    CASE 
                        WHEN sup_code = '%' THEN '根级别(%)'
                        WHEN LENGTH(sup_code) = 2 THEN '省级(2位)'
                        WHEN LENGTH(sup_code) = 4 THEN '市级(4位)'
                        WHEN LENGTH(sup_code) = 6 THEN '县级(6位)'
                        WHEN LENGTH(sup_code) > 6 THEN '乡镇级(>6位)'
                        ELSE '其他'
                    END as pattern,
                    COUNT(*) as count
                FROM sys_region_code 
                WHERE sup_code IS NOT NULL
                GROUP BY pattern
                ORDER BY count DESC
            """)
            for pattern, count in cursor.fetchall():
                print(f"   {pattern}: {count} 条记录")
            
            # 3. 查找包含目标县名的记录
            print("\n3. 查找目标县:")
            target_counties = ['墨江', '会泽', '彝良', '绥江']
            
            for county in target_counties:
                cursor.execute("""
                    SELECT data_name, data_code, sup_code, data_level
                    FROM sys_region_code 
                    WHERE data_name LIKE %s
                    ORDER BY data_level
                """, (f'%{county}%',))
                
                results = cursor.fetchall()
                print(f"\n   {county}相关记录:")
                for name, code, sup_code, level in results:
                    print(f"     {name} | 代码:{code} | 上级:{sup_code} | 级别:{level}")
                    
                    # 如果是县级，查看其子区域
                    if level >= 3:  # 县级或以下
                        cursor.execute("""
                            SELECT COUNT(*), 
                                   GROUP_CONCAT(data_name ORDER BY data_name LIMIT 5) as sample_names
                            FROM sys_region_code 
                            WHERE sup_code = %s
                        """, (code,))
                        
                        sub_result = cursor.fetchone()
                        if sub_result and sub_result[0] > 0:
                            count, names = sub_result
                            print(f"       └─ {count}个子区域，如: {names}")
            
            # 4. 查找预警中提到的具体地名
            print("\n4. 查找预警中的具体地名:")
            specific_places = ['娜姑镇', '纸厂乡', '洛旺', '柳溪', '牛街', '角奎', '南岸镇', '板栗镇', '中城镇', '新滩镇', '会仪镇']
            
            for place in specific_places:
                cursor.execute("""
                    SELECT data_name, data_code, sup_code
                    FROM sys_region_code 
                    WHERE data_name LIKE %s
                    LIMIT 3
                """, (f'%{place}%',))
                
                results = cursor.fetchall()
                if results:
                    print(f"   {place}:")
                    for name, code, sup_code in results:
                        print(f"     {name} | 代码:{code} | 上级:{sup_code}")
        
        mysql_conn.close()
        print("\n检查完成")
        
    except Exception as e:
        print(f"检查失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    check_data()
