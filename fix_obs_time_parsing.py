#!/usr/bin/env python3
# coding: utf-8
"""
修复实况数据时间解析的脚本
测试正确的时间解析逻辑
"""

import os
import xarray as xr
import pandas as pd
import numpy as np
from pathlib import Path

def correct_time_parsing(nc_file_path):
    """
    正确的时间解析逻辑
    """
    print(f"\n{'='*60}")
    print(f"测试正确的时间解析: {os.path.basename(nc_file_path)}")
    print(f"{'='*60}")
    
    try:
        ds = xr.open_dataset(nc_file_path, engine="netcdf4")
        
        # 当前时间
        current_time = pd.Timestamp.now()
        print(f"当前时间: {current_time}")
        
        # 原始的错误解析方法（只用reftime）
        if 'reftime' in ds.coords:
            reftime_str = str(ds.coords['reftime'].values[0])
            old_ts = pd.to_datetime(reftime_str, format='%Y%m%d%H%M')
            print(f"原始方法 (仅reftime): {old_ts}")
            print(f"  与当前时间差: {current_time - old_ts}")
        
        # 正确的解析方法
        ts = None
        
        # 方法1: 使用time坐标 + reftime坐标 (推荐)
        if 'time' in ds.coords and 'reftime' in ds.coords:
            try:
                reftime_str = str(ds.coords['reftime'].values[0])
                reftime = pd.to_datetime(reftime_str, format='%Y%m%d%H%M')
                
                # time坐标的值是相对于reftime的小时偏移
                time_offset_hours = float(ds.coords['time'].values[0])
                ts = reftime + pd.Timedelta(hours=time_offset_hours)
                
                print(f"正确方法 (time+reftime):")
                print(f"  reftime: {reftime}")
                print(f"  time偏移: {time_offset_hours} 小时")
                print(f"  实际时间: {ts}")
                print(f"  与当前时间差: {current_time - ts}")
                
            except Exception as e:
                print(f"方法1失败: {e}")
        
        # 如果方法1失败，回退到reftime
        if ts is None and 'reftime' in ds.coords:
            reftime_str = str(ds.coords['reftime'].values[0])
            ts = pd.to_datetime(reftime_str, format='%Y%m%d%H%M')
            print(f"回退方法 (仅reftime): {ts}")
        
        # 检查时间是否合理
        if ts is not None:
            time_diff = current_time - ts
            print(f"\n时间合理性检查:")
            print(f"  时间差: {time_diff}")
            
            if time_diff.total_seconds() < 0:
                print(f"  ⚠️  警告: 数据时间在未来!")
            elif time_diff.total_seconds() < 3600:  # 1小时内
                print(f"  ✓ 很新的数据 (1小时内)")
            elif time_diff.total_seconds() < 86400:  # 24小时内
                print(f"  ✓ 较新的数据 (24小时内)")
            elif time_diff.total_seconds() < 172800:  # 48小时内
                print(f"  ⚠️  数据有点旧 (24-48小时)")
            else:
                print(f"  ❌ 数据很旧 (超过48小时)")
        
        ds.close()
        return ts
        
    except Exception as e:
        print(f"处理文件时出错: {e}")
        return None

def test_time_parsing_on_all_files():
    """
    在所有NC文件上测试时间解析
    """
    script_dir = Path(__file__).parent
    
    # 搜索目录
    search_dirs = [
        script_dir / 'data/test',
        script_dir / 'data/obs',
        script_dir / 'data/obs/nc_files'
    ]
    
    nc_files = []
    for base_dir in search_dirs:
        if base_dir.exists():
            for nc_file in base_dir.rglob('*.nc'):
                nc_files.append(str(nc_file))
    
    if not nc_files:
        print("未找到任何NC文件")
        return
    
    print(f"找到 {len(nc_files)} 个NC文件，开始测试时间解析...")
    
    for nc_file in nc_files:
        parsed_time = correct_time_parsing(nc_file)
        if parsed_time:
            print(f"✓ 成功解析: {parsed_time}")
        else:
            print(f"❌ 解析失败")

def generate_fixed_time_parsing_code():
    """
    生成修复后的时间解析代码
    """
    print(f"\n{'='*60}")
    print("修复后的时间解析代码:")
    print(f"{'='*60}")
    
    code = '''
# 修复后的时间解析逻辑
def parse_obs_time_correctly(ds):
    """
    正确解析实况数据的时间戳
    
    Args:
        ds: xarray Dataset
        
    Returns:
        pandas.Timestamp: 解析后的时间戳
    """
    ts = None
    
    # 方法1: 使用time坐标 + reftime坐标 (推荐)
    if 'time' in ds.coords and 'reftime' in ds.coords:
        try:
            reftime_str = str(ds.coords['reftime'].values[0])
            reftime = pd.to_datetime(reftime_str, format='%Y%m%d%H%M')
            
            # time坐标的值是相对于reftime的小时偏移
            time_offset_hours = float(ds.coords['time'].values[0])
            ts = reftime + pd.Timedelta(hours=time_offset_hours)
            
            logger.info(f"解析时间: reftime={reftime}, offset={time_offset_hours}h, 实际时间={ts}")
            return ts
            
        except Exception as e:
            logger.warning(f"方法1时间解析失败: {e}")
    
    # 方法2: 回退到仅使用reftime
    if 'reftime' in ds.coords:
        try:
            reftime_str = str(ds.coords['reftime'].values[0])
            ts = pd.to_datetime(reftime_str, format='%Y%m%d%H%M')
            logger.info(f"使用reftime: {ts}")
            return ts
        except Exception as e:
            logger.warning(f"方法2时间解析失败: {e}")
    
    # 方法3: 检查其他时间坐标
    for coord_name in ds.coords:
        if 'time' in coord_name.lower() and coord_name not in ['time', 'reftime']:
            try:
                ts = pd.to_datetime(ds.coords[coord_name].values[0])
                logger.info(f"使用坐标 {coord_name}: {ts}")
                return ts
            except:
                continue
    
    logger.error("无法解析时间信息")
    return None

# 在process_obs_nc_files_cpu_intensive函数中的使用:
# 替换原来的时间解析部分:
# if 'reftime' in ds.coords:
#     base_time_str = str(ds.coords['reftime'].values[0])
#     ts = pd.to_datetime(base_time_str, format='%Y%m%d%H%M')

# 改为:
ts = parse_obs_time_correctly(ds)
if ts is None:
    logger.warning(f"无法解析文件 {nc_file} 的时间信息，跳过")
    continue
'''
    
    print(code)

if __name__ == '__main__':
    print("实况数据时间解析修复测试")
    test_time_parsing_on_all_files()
    generate_fixed_time_parsing_code()
