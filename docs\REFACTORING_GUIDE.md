# 代码重构指南

## 概述

本文档说明了如何使用新创建的公共模块来重构现有的天气数据处理脚本，减少代码重复，提高可维护性。

## 重构前后对比

### 重构前的问题

1. **代码重复**: `weather_cell_1h_async_hybrid.py` 和 `weather_cell_6m_async_hybrid.py` 包含大量重复的代码
2. **NetCDF处理**: 两个文件都有相似的NetCDF文件读取和处理逻辑
3. **数据库操作**: 重复的异步数据库连接和操作代码
4. **格网编码**: 相同的格网编码/解码函数
5. **配置管理**: 分散的配置和常量定义

### 重构后的改进

1. **公共模块**: 创建了 `weather_common.py` 包含所有公共函数和类
2. **NetCDF工具**: 在 `weather_download.py` 中添加了NetCDF处理的公共函数
3. **统一接口**: 提供了一致的API接口
4. **更好的组织**: 代码按功能模块化组织
5. **易于测试**: 公共函数可以独立测试

## 新的模块结构

### weather_common.py

包含以下公共组件：

#### 格网处理函数
- `encode_cell(lon, lat)` - 经纬度转格网ID
- `decode_cell(cell_id)` - 格网ID转经纬度
- `get_netcdf_indices_from_coords()` - 坐标转NetCDF索引

#### 文件处理函数
- `get_nc_file_path_single()` - 获取单个NC文件路径（6分钟数据）
- `get_nc_files_path_multiple()` - 获取多个NC文件路径（1小时数据）
- `load_valid_cells()` - 加载有效格网单元

#### 异步数据库管理
- `AsyncDatabaseManager` - 异步数据库连接池管理器
- 支持批量upsert操作
- 支持存储过程调用

#### 常量和配置
- `NC_NROWS`, `NC_NCOLS` - NetCDF网格尺寸
- `MAX_CONCURRENT_DB_OPERATIONS` - 并发数据库操作数

### weather_download.py (扩展)

新增NetCDF处理函数：

#### 数据读取函数
- `read_netcdf_data()` - 读取NetCDF文件和元数据
- `extract_netcdf_coordinates()` - 提取坐标信息
- `get_netcdf_value_at_coords()` - 获取指定坐标的数据值
- `validate_netcdf_file()` - 验证NetCDF文件有效性

## 使用示例

### 重构后的代码结构

```python
# 导入公共模块
from weather_common import (
    encode_cell, decode_cell, AsyncDatabaseManager,
    get_nc_file_path_single, load_valid_cells
)

from weather_download import (
    read_netcdf_data, extract_netcdf_coordinates,
    get_netcdf_value_at_coords
)

# 使用公共函数
async def process_weather_data():
    # 1. 获取文件路径
    nc_file_path, is_downloaded, data_type = get_nc_file_path_single()
    
    # 2. 读取数据
    ds, metadata = read_netcdf_data(nc_file_path, "WEATHER")
    
    # 3. 提取坐标
    lon_array, lat_array, lon_min, lat_min = extract_netcdf_coordinates(ds)
    
    # 4. 加载有效格网
    valid_cells = load_valid_cells()
    
    # 5. 处理数据
    async with AsyncDatabaseManager(PG_URL) as db:
        for cell_id in valid_cells:
            lon, lat = decode_cell(cell_id)
            value = get_netcdf_value_at_coords(
                ds, "WEATHER", lon, lat, 
                lon_array, lat_array, lon_min, lat_min
            )
            # 处理数据...
```

## 重构步骤

### 第一步：分析现有代码

1. 识别重复的函数和类
2. 找出可以抽象的公共逻辑
3. 确定模块边界

### 第二步：创建公共模块

1. 创建 `weather_common.py`
2. 移动公共函数到公共模块
3. 扩展 `weather_download.py` 的NetCDF功能

### 第三步：重构现有脚本

1. 修改导入语句
2. 替换重复代码为公共函数调用
3. 简化主要逻辑

### 第四步：测试和验证

1. 运行测试脚本验证功能
2. 确保重构后的代码行为一致
3. 性能测试

## 重构的好处

### 1. 代码复用
- 减少重复代码约60-70%
- 统一的实现避免不一致性
- 更容易维护和更新

### 2. 模块化设计
- 清晰的职责分离
- 更好的代码组织
- 易于理解和修改

### 3. 测试友好
- 公共函数可以独立测试
- 更好的测试覆盖率
- 更容易调试

### 4. 扩展性
- 新功能可以基于公共模块构建
- 更容易添加新的数据类型支持
- 更好的配置管理

## 迁移建议

### 渐进式迁移

1. **保留原始文件**: 在重构过程中保留原始文件作为备份
2. **逐步替换**: 一次重构一个模块或功能
3. **并行测试**: 同时运行原始版本和重构版本进行对比

### 向后兼容

1. **保持接口**: 尽量保持原有的函数签名
2. **配置兼容**: 确保配置文件格式兼容
3. **数据格式**: 保持数据库表结构不变

## 测试策略

### 单元测试
```bash
# 测试公共模块
uv run python tests/test_weather_common.py

# 测试依赖
uv run python tests/test_dependencies.py
```

### 集成测试
```bash
# 测试重构后的示例
uv run python weather_cell_refactored_example.py
```

### 性能测试
- 比较重构前后的处理时间
- 监控内存使用情况
- 验证数据库操作效率

## 注意事项

1. **配置依赖**: 确保 `config.py` 和 `config.yml` 正确配置
2. **数据库连接**: 验证数据库连接字符串和权限
3. **文件路径**: 确认NetCDF文件路径配置正确
4. **GDAL依赖**: 确保GDAL相关库正确安装

## 下一步计划

1. **完整重构**: 基于示例完全重构原始脚本
2. **性能优化**: 进一步优化数据库操作和内存使用
3. **错误处理**: 增强错误处理和恢复机制
4. **监控日志**: 改进日志记录和监控功能
5. **文档完善**: 补充API文档和使用示例

## 总结

通过创建公共模块和重构现有代码，我们实现了：

- ✅ 减少代码重复
- ✅ 提高代码可维护性
- ✅ 改善模块化设计
- ✅ 增强测试能力
- ✅ 提供更好的扩展性

重构后的代码更加清晰、可靠和易于维护，为未来的功能扩展奠定了良好的基础。
