#!/usr/bin/env python3
# coding: utf-8
"""
手动创建缺失的分区

当遇到 "no partition of relation found for row" 错误时，
可以使用此脚本手动创建缺失的分区。

使用方法:
1. 创建今天开始的7天分区:
   python create_missing_partitions.py

2. 创建指定日期开始的分区:
   python create_missing_partitions.py --start-date 2025-07-23 --days 10

3. 创建特定日期的分区:
   python create_missing_partitions.py --dates 2025-07-23 2025-07-24 2025-07-25
"""

import argparse
from datetime import datetime, timedelta
from sqlalchemy import create_engine, text

# 导入统一配置
from config import PG_URL

def create_partitions_for_dates(engine, dates):
    """
    为指定日期创建分区

    Args:
        engine: 数据库引擎
        dates: 日期列表
    """
    # 需要创建日分区的表
    daily_partition_tables = [
        'public.weather_cell_6m',
        'public.weather_cell_1h',
        'public.forecast_precipitation_6min_line',
        'public.forecast_precipitation_6min_polygon',
        'public.forecast_precipitation_6min_relation',
        'public.forecast_precipitation_hourly_line',
        'public.forecast_precipitation_hourly_polygon',
        'public.forecast_precipitation_hourly_relation',
        'public.forecast_precipitation_summary_line',
        'public.forecast_precipitation_summary_polygon',
        'public.forecast_precipitation_summary_relation'
    ]

    print(f"开始为 {len(dates)} 个日期创建分区...")

    try:
        with engine.begin() as conn:
            # 处理每个日期
            processed_months = set()  # 跟踪已处理的月份，避免重复创建月分区

            for current_date in dates:
                if isinstance(current_date, str):
                    current_date = datetime.strptime(current_date, '%Y-%m-%d').date()

                next_date = current_date + timedelta(days=1)
                date_str = current_date.strftime('%Y%m%d')

                print(f"\n处理日期: {current_date}")

                # 创建日分区
                for table_name in daily_partition_tables:
                    table_short_name = table_name.replace('public.', '')
                    partition_name = f"{table_short_name}_p{date_str}"

                    try:
                        # 检查分区是否已存在
                        exists = conn.execute(text("""
                            SELECT EXISTS (
                                SELECT 1 FROM pg_tables
                                WHERE schemaname = 'public'
                                AND tablename = :partition_name
                            )
                        """), {"partition_name": partition_name}).scalar()

                        if exists:
                            print(f"  ✓ {partition_name} (已存在)")
                            continue

                        # 创建分区
                        create_partition_sql = f"""
                            CREATE TABLE IF NOT EXISTS public.{partition_name}
                            PARTITION OF {table_name}
                            FOR VALUES FROM ('{current_date}') TO ('{next_date}')
                        """

                        conn.execute(text(create_partition_sql))
                        print(f"  ✓ {partition_name} (新建)")

                    except Exception as partition_error:
                        print(f"  ❌ {partition_name}: {partition_error}")
                        continue

                # 创建月分区 (weather_alarm表)
                year_month = current_date.strftime('%Y-%m')
                if year_month not in processed_months:
                    processed_months.add(year_month)

                    # 计算月份的开始和结束日期
                    month_start = current_date.replace(day=1)
                    if month_start.month == 12:
                        month_end = month_start.replace(year=month_start.year + 1, month=1)
                    else:
                        month_end = month_start.replace(month=month_start.month + 1)

                    month_str = current_date.strftime('%Y%m')
                    alarm_partition_name = f"weather_alarm_p{month_str}"

                    try:
                        # 检查月分区是否已存在
                        exists = conn.execute(text("""
                            SELECT EXISTS (
                                SELECT 1 FROM pg_tables
                                WHERE schemaname = 'public'
                                AND tablename = :partition_name
                            )
                        """), {"partition_name": alarm_partition_name}).scalar()

                        if exists:
                            print(f"  ✓ {alarm_partition_name} (已存在)")
                        else:
                            # 创建月分区
                            create_partition_sql = f"""
                                CREATE TABLE IF NOT EXISTS public.{alarm_partition_name}
                                PARTITION OF public.weather_alarm
                                FOR VALUES FROM ('{month_start}') TO ('{month_end}')
                            """

                            conn.execute(text(create_partition_sql))
                            print(f"  ✓ {alarm_partition_name} (新建)")

                    except Exception as partition_error:
                        print(f"  ❌ {alarm_partition_name}: {partition_error}")
                        continue

    except Exception as e:
        print(f"创建分区时发生错误: {e}")
        raise

def create_partitions_for_range(engine, start_date, days):
    """
    为日期范围创建分区
    
    Args:
        engine: 数据库引擎
        start_date: 开始日期
        days: 天数
    """
    if isinstance(start_date, str):
        start_date = datetime.strptime(start_date, '%Y-%m-%d').date()
    
    dates = []
    for i in range(days):
        dates.append(start_date + timedelta(days=i))
    
    create_partitions_for_dates(engine, dates)

def check_missing_partitions(engine, dates):
    """
    检查哪些分区缺失

    Args:
        engine: 数据库引擎
        dates: 要检查的日期列表
    """
    daily_partition_tables = [
        'public.weather_cell_6m',
        'public.weather_cell_1h',
        'public.forecast_precipitation_6min_line',
        'public.forecast_precipitation_6min_polygon',
        'public.forecast_precipitation_6min_relation',
        'public.forecast_precipitation_hourly_line',
        'public.forecast_precipitation_hourly_polygon',
        'public.forecast_precipitation_hourly_relation',
        'public.forecast_precipitation_summary_line',
        'public.forecast_precipitation_summary_polygon',
        'public.forecast_precipitation_summary_relation'
    ]

    print("检查缺失的分区...")
    missing_partitions = []

    try:
        with engine.connect() as conn:
            processed_months = set()  # 跟踪已检查的月份

            for current_date in dates:
                if isinstance(current_date, str):
                    current_date = datetime.strptime(current_date, '%Y-%m-%d').date()

                date_str = current_date.strftime('%Y%m%d')

                # 检查日分区
                for table_name in daily_partition_tables:
                    table_short_name = table_name.replace('public.', '')
                    partition_name = f"{table_short_name}_p{date_str}"

                    exists = conn.execute(text("""
                        SELECT EXISTS (
                            SELECT 1 FROM pg_tables
                            WHERE schemaname = 'public'
                            AND tablename = :partition_name
                        )
                    """), {"partition_name": partition_name}).scalar()

                    if not exists:
                        missing_partitions.append((current_date, table_name, partition_name))

                # 检查月分区 (weather_alarm表)
                year_month = current_date.strftime('%Y-%m')
                if year_month not in processed_months:
                    processed_months.add(year_month)

                    month_str = current_date.strftime('%Y%m')
                    alarm_partition_name = f"weather_alarm_p{month_str}"

                    exists = conn.execute(text("""
                        SELECT EXISTS (
                            SELECT 1 FROM pg_tables
                            WHERE schemaname = 'public'
                            AND tablename = :partition_name
                        )
                    """), {"partition_name": alarm_partition_name}).scalar()

                    if not exists:
                        missing_partitions.append((current_date, 'public.weather_alarm', alarm_partition_name))

    except Exception as e:
        print(f"检查分区时发生错误: {e}")
        return []

    if missing_partitions:
        print(f"发现 {len(missing_partitions)} 个缺失的分区:")
        for date, table, partition in missing_partitions:
            print(f"  - {partition} (表: {table}, 日期: {date})")
    else:
        print("✓ 所有分区都存在")

    return missing_partitions

def main():
    parser = argparse.ArgumentParser(description='创建缺失的数据库分区')
    parser.add_argument('--start-date', type=str, 
                       help='开始日期 (格式: YYYY-MM-DD), 默认为今天')
    parser.add_argument('--days', type=int, default=7,
                       help='创建多少天的分区，默认7天')
    parser.add_argument('--dates', nargs='+', type=str,
                       help='指定要创建分区的日期列表 (格式: YYYY-MM-DD)')
    parser.add_argument('--check-only', action='store_true',
                       help='只检查缺失的分区，不创建')
    
    args = parser.parse_args()
    
    engine = create_engine(PG_URL)
    
    try:
        if args.dates:
            # 使用指定的日期列表
            dates = args.dates
            if args.check_only:
                check_missing_partitions(engine, dates)
            else:
                create_partitions_for_dates(engine, dates)
        else:
            # 使用日期范围
            start_date = args.start_date
            if start_date is None:
                start_date = datetime.now().date()
            else:
                start_date = datetime.strptime(start_date, '%Y-%m-%d').date()
            
            dates = []
            for i in range(args.days):
                dates.append(start_date + timedelta(days=i))
            
            if args.check_only:
                check_missing_partitions(engine, dates)
            else:
                create_partitions_for_range(engine, start_date, args.days)
        
        print("\n✓ 操作完成")
        
    except Exception as e:
        print(f"❌ 操作失败: {e}")
        raise
    finally:
        engine.dispose()

if __name__ == '__main__':
    main()
