#!/usr/bin/env python3
# coding: utf-8
"""
天气数据下载目录结构演示
展示新的按年/月/日划分的下载和存储目录结构
"""

import os
import sys
from pathlib import Path
from datetime import datetime, timedelta

# 添加src目录到Python路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from weather_download import WeatherDownloader
import logging

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


def demo_new_directory_structure():
    """演示新的目录结构"""
    print("=" * 80)
    print("天气数据下载目录结构升级演示")
    print("=" * 80)
    
    print("\n1. 新的目录结构:")
    print("   data/")
    print("   ├── precipitation_6min/          # 6分钟降水数据")
    print("   │   ├── 2025/                    # 年份目录")
    print("   │   │   ├── 07/                  # 月份目录")
    print("   │   │   │   ├── 22/              # 日期目录")
    print("   │   │   │   │   ├── downloads/   # 下载的压缩文件")
    print("   │   │   │   │   │   └── gz_mpfv3_202507221430.tar.gz")
    print("   │   │   │   │   └── nc_files/    # 解压后的NC文件")
    print("   │   │   │   │       ├── MPF_20250722143000.nc")
    print("   │   │   │   │       ├── MPF_20250722143600.nc")
    print("   │   │   │   │       └── MPF_20250722144200.nc")
    print("   │   │   │   ├── 23/")
    print("   │   │   │   │   ├── downloads/")
    print("   │   │   │   │   └── nc_files/")
    print("   │   │   │   └── 24/")
    print("   │   │   │       ├── downloads/")
    print("   │   │   │       └── nc_files/")
    print("   │   │   └── 08/")
    print("   │   │       └── 01/")
    print("   │   │           ├── downloads/")
    print("   │   │           └── nc_files/")
    print("   │   └── backup/                  # 备份文件")
    print("   │       └── 2025/")
    print("   │           └── 07/")
    print("   │               └── 22/")
    print("   │                   ├── old_file1.nc")
    print("   │                   └── old_file2_143052.nc")
    print("   ├── temperature_1h/              # 1小时温度数据")
    print("   │   ├── 2025/")
    print("   │   │   └── 07/")
    print("   │   │       └── 22/")
    print("   │   │           ├── downloads/")
    print("   │   │           └── nc_files/")
    print("   │   └── backup/")
    print("   └── precipitation_1h/            # 1小时降水数据")
    print("       ├── 2025/")
    print("       └── backup/")
    
    print("\n2. 主要改进:")
    print("   ✓ 下载文件按日期自动分类存储")
    print("   ✓ NC文件按日期自动分类存储")
    print("   ✓ 备份文件按日期自动分类存储")
    print("   ✓ 便于查找特定日期的数据文件")
    print("   ✓ 支持按日期批量清理下载文件")
    print("   ✓ 避免单个目录文件过多的性能问题")
    print("   ✓ 自动创建和管理目录结构")


def demo_usage_examples():
    """演示使用示例"""
    print("\n" + "=" * 80)
    print("使用示例")
    print("=" * 80)
    
    print("\n1. 基本下载操作（自动使用新结构）:")
    print("   ```python")
    print("   from weather_download import WeatherDownloader")
    print("   ")
    print("   # 创建下载器实例")
    print("   downloader = WeatherDownloader('gz_mpfv3')")
    print("   ")
    print("   # 下载数据（自动按当前日期创建目录结构）")
    print("   files = downloader.download_from_api()")
    print("   if files:")
    print("       print(f'下载成功，文件保存在当天的目录中: {files}')")
    print("   ```")
    
    print("\n2. 获取最新文件:")
    print("   ```python")
    print("   # 在过去7天内搜索最新的NC文件")
    print("   latest_file = downloader.get_latest_nc_file(search_days=7)")
    print("   if latest_file:")
    print("       print(f'最新文件: {latest_file}')")
    print("   ```")
    
    print("\n3. 按日期获取文件:")
    print("   ```python")
    print("   from datetime import datetime")
    print("   ")
    print("   # 获取指定日期的所有NC文件")
    print("   target_date = datetime(2025, 7, 22)")
    print("   files = downloader.get_nc_files_by_date(target_date)")
    print("   print(f'2025-07-22的文件: {files}')")
    print("   ```")
    
    print("\n4. 备份指定日期的文件:")
    print("   ```python")
    print("   # 备份今天的文件")
    print("   success = downloader.backup_existing_files()")
    print("   ")
    print("   # 备份指定日期的文件")
    print("   target_date = datetime(2025, 7, 22)")
    print("   success = downloader.backup_existing_files(target_date)")
    print("   ```")
    
    print("\n5. 清理旧文件:")
    print("   ```python")
    print("   # 清理7天前的下载文件")
    print("   downloader.cleanup_old_downloads(keep_days=7)")
    print("   ")
    print("   # 清理30天前的备份文件")
    print("   downloader.cleanup_old_backups(keep_days=30)")
    print("   ```")


def demo_configuration():
    """演示配置选项"""
    print("\n" + "=" * 80)
    print("配置选项")
    print("=" * 80)
    
    print("\n在 config.yml 中可以添加的新配置:")
    print("```yaml")
    print("weather_download:")
    print("  # 现有配置")
    print("  backup_keep_days: 30        # 备份文件保留天数")
    print("  auto_cleanup_backups: true  # 是否自动清理旧备份")
    print("  enable_backup: true         # 是否启用备份功能")
    print("  ")
    print("  # 新增配置（可选）")
    print("  download_keep_days: 7       # 下载文件保留天数（默认7天）")
    print("  auto_cleanup_downloads: true # 是否自动清理旧下载文件")
    print("```")
    
    print("\n配置说明:")
    print("- download_keep_days: 设置下载文件的保留天数，超过此天数的下载文件会被清理")
    print("- auto_cleanup_downloads: 是否在下载新文件时自动清理旧下载文件")
    print("- 其他配置保持不变，向后兼容")


def show_current_structure_status():
    """显示当前目录结构状态"""
    print("\n" + "=" * 80)
    print("当前目录结构状态检查")
    print("=" * 80)
    
    try:
        # 检查各种数据类型的目录结构
        data_types = ["gz_mpfv3", "gz_didiforecast1hTEM", "gz_didiforecast1hPRE", 
                     "gz_didiforecast1hWEATHER", "gz_didiforecast1hVIS"]
        
        for data_type in data_types:
            try:
                downloader = WeatherDownloader(data_type)
                base_dir = downloader.base_dir
                
                print(f"\n{data_type} ({downloader.data_type_config['name']}):")
                print(f"  基础目录: {base_dir}")
                
                if base_dir.exists():
                    # 统计按日期组织的文件
                    date_dirs = []
                    total_nc_files = 0
                    total_download_files = 0
                    
                    # 遍历年份目录
                    for year_dir in base_dir.glob("*"):
                        if year_dir.is_dir() and year_dir.name.isdigit():
                            for month_dir in year_dir.glob("*"):
                                if month_dir.is_dir() and month_dir.name.isdigit():
                                    for day_dir in month_dir.glob("*"):
                                        if day_dir.is_dir() and day_dir.name.isdigit():
                                            date_str = f"{year_dir.name}-{month_dir.name}-{day_dir.name}"
                                            
                                            # 检查NC文件
                                            nc_dir = day_dir / "nc_files"
                                            if nc_dir.exists():
                                                nc_files = list(nc_dir.glob("*.nc"))
                                                if nc_files:
                                                    total_nc_files += len(nc_files)
                                                    date_dirs.append(date_str)
                                            
                                            # 检查下载文件
                                            download_dir = day_dir / "downloads"
                                            if download_dir.exists():
                                                download_files = list(download_dir.glob("*.tar.gz"))
                                                total_download_files += len(download_files)
                    
                    print(f"  按日期组织的NC文件数: {total_nc_files}")
                    print(f"  按日期组织的下载文件数: {total_download_files}")
                    if date_dirs:
                        print(f"  有数据的日期: {len(set(date_dirs))} 天")
                        print(f"  最近数据日期: {sorted(set(date_dirs))[-1] if date_dirs else '无'}")
                    else:
                        print("  暂无按日期组织的文件（新结构将在下次下载时生效）")
                else:
                    print("  基础目录不存在")
                    
            except Exception as e:
                print(f"  检查失败: {e}")
                
    except Exception as e:
        print(f"检查目录结构状态时发生错误: {e}")


def demo_migration_info():
    """演示迁移信息"""
    print("\n" + "=" * 80)
    print("迁移和兼容性说明")
    print("=" * 80)
    
    print("\n1. 自动生效:")
    print("   - 新的目录结构已经集成到下载系统中")
    print("   - 下次运行下载时会自动使用新的目录结构")
    print("   - 无需修改任何现有代码")
    
    print("\n2. 向后兼容:")
    print("   - 现有的备份文件保持原位置不变")
    print("   - 清理功能同时支持新旧两种格式")
    print("   - 获取最新文件功能会搜索新旧两种位置")
    
    print("\n3. 渐进式迁移:")
    print("   - 新下载的文件使用新结构")
    print("   - 旧文件可以继续使用")
    print("   - 可以选择性地整理旧文件到新结构中")
    
    print("\n4. 清理策略:")
    print("   - 下载文件默认保留7天（可配置）")
    print("   - 备份文件默认保留30天（可配置）")
    print("   - 自动删除空的日期目录")


if __name__ == "__main__":
    # 运行演示
    demo_new_directory_structure()
    demo_usage_examples()
    demo_configuration()
    show_current_structure_status()
    demo_migration_info()
    
    print("\n" + "=" * 80)
    print("演示完成")
    print("=" * 80)
    print("\n现在您的天气数据下载和存储将按照年/月/日的目录结构自动组织，")
    print("这样可以更好地管理和查找不同日期的数据文件。")
    print("\n下次运行天气数据下载时，新的目录结构就会自动生效！")
