#!/usr/bin/env python3
# coding: utf-8
"""
测试特定区域的预警内容解析
"""

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

import pymysql
from src.weather_alarm import AlarmContentParser
from src.config import MYSQL_CONFIG

def find_region_codes():
    """查找墨江县、会泽县等相关的区域代码"""
    
    try:
        mysql_conn = pymysql.connect(
            host=MYSQL_CONFIG["host"],
            port=MYSQL_CONFIG["port"],
            user=MYSQL_CONFIG["username"],
            password=MYSQL_CONFIG["password"],
            database=MYSQL_CONFIG["database"],
            charset='utf8mb4',
            autocommit=True
        )
        
        print("=" * 80)
        print("查找特定区域的代码和子区域")
        print("=" * 80)
        
        # 查找墨江县、会泽县、彝良县、绥江县相关的记录
        target_regions = ['墨江', '会泽', '彝良', '绥江']
        
        with mysql_conn.cursor() as cursor:
            for region in target_regions:
                print(f"\n🔍 查找包含'{region}'的区域:")
                
                # 查找包含该地名的记录
                cursor.execute("""
                    SELECT data_name, data_code, sup_code, data_level
                    FROM sys_region_code 
                    WHERE data_name LIKE %s
                    ORDER BY data_level, data_code
                """, (f'%{region}%',))
                
                results = cursor.fetchall()
                
                if results:
                    for row in results:
                        data_name, data_code, sup_code, data_level = row
                        print(f"   {data_name} (代码: {data_code}, 上级: {sup_code}, 级别: {data_level})")
                        
                        # 如果这是县级区域，查找其子区域
                        if data_level in [3, 4]:  # 假设3或4是县级
                            cursor.execute("""
                                SELECT COUNT(*), GROUP_CONCAT(data_name SEPARATOR ', ') as names
                                FROM sys_region_code 
                                WHERE sup_code = %s
                                LIMIT 1
                            """, (data_code,))
                            
                            sub_result = cursor.fetchone()
                            if sub_result and sub_result[0] > 0:
                                count, names = sub_result
                                print(f"     └─ 有 {count} 个子区域: {names[:100]}{'...' if len(names) > 100 else ''}")
                else:
                    print(f"   ❌ 未找到包含'{region}'的区域")
        
        # 查找一些典型的乡镇名称
        print(f"\n🔍 查找预警中提到的具体乡镇:")
        specific_towns = ['娜姑镇', '纸厂乡', '洛旺', '柳溪', '牛街', '角奎', '南岸镇', '板栗镇']
        
        with mysql_conn.cursor() as cursor:
            for town in specific_towns:
                cursor.execute("""
                    SELECT data_name, data_code, sup_code
                    FROM sys_region_code 
                    WHERE data_name LIKE %s
                    LIMIT 5
                """, (f'%{town}%',))
                
                results = cursor.fetchall()
                if results:
                    print(f"   {town}:")
                    for row in results:
                        print(f"     {row[0]} (代码: {row[1]}, 上级: {row[2]})")
        
        # 测试解析功能
        print(f"\n🧪 测试解析功能:")
        parser = AlarmContentParser(mysql_conn)
        
        # 找一个有子区域的上级代码进行测试
        with mysql_conn.cursor() as cursor:
            cursor.execute("""
                SELECT sup_code, COUNT(*) as sub_count
                FROM sys_region_code 
                WHERE sup_code IS NOT NULL 
                AND sup_code != '%'
                AND sup_code != ''
                GROUP BY sup_code
                HAVING sub_count BETWEEN 5 AND 20
                ORDER BY sub_count DESC
                LIMIT 3
            """)
            
            test_codes = cursor.fetchall()
            
            for sup_code, sub_count in test_codes:
                print(f"\n   测试上级代码: {sup_code} ({sub_count}个子区域)")
                
                # 获取该上级的子区域
                cursor.execute("""
                    SELECT data_name, data_code
                    FROM sys_region_code 
                    WHERE sup_code = %s
                    LIMIT 5
                """, (sup_code,))
                
                sub_regions = cursor.fetchall()
                print(f"   子区域示例:")
                for name, code in sub_regions:
                    print(f"     {name} ({code})")
                
                # 构造测试内容
                if len(sub_regions) >= 2:
                    region1 = sub_regions[0][0]
                    region2 = sub_regions[1][0]
                    
                    test_content = f"测试气象台发布预警：未来24小时{region1}、{region2}将出现恶劣天气。"
                    print(f"   测试内容: {test_content}")
                    
                    # 获取区域数据并解析
                    region_data = parser.get_region_data_by_geo_codes([sup_code])
                    results = parser.parse_alarm_content(test_content, sup_code, region_data)
                    
                    print(f"   解析结果: {len(results)}个匹配")
                    for result in results:
                        print(f"     ✓ {result['data_name']} ({result['data_code']})")
        
        mysql_conn.close()
        
    except Exception as e:
        print(f"查找失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    find_region_codes()
