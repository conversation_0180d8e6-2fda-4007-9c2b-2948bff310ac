#!/usr/bin/env python3
# coding: utf-8
"""
新配置系统测试脚本
测试6分钟和1小时天气处理配置
"""

import sys
from pathlib import Path

# 添加项目根目录和src目录到Python路径
project_root = Path(__file__).parent.parent
src_path = project_root / "src"
sys.path.insert(0, str(project_root))
sys.path.insert(0, str(src_path))

def test_weather_6m_config():
    """测试6分钟天气处理配置"""
    print("=== 测试6分钟天气处理配置 ===")
    
    try:
        from config import (
            WEATHER_6M_DATA_TYPE, WEATHER_6M_VARIABLE, WEATHER_6M_BATCH_SIZE,
            WEATHER_6M_MAX_CONCURRENT, WEATHER_6M_TARGET_TABLE, get_weather_6m_config
        )
        
        print(f"✓ 数据类型: {WEATHER_6M_DATA_TYPE}")
        print(f"✓ 变量名: {WEATHER_6M_VARIABLE}")
        print(f"✓ 批处理大小: {WEATHER_6M_BATCH_SIZE}")
        print(f"✓ 最大并发数: {WEATHER_6M_MAX_CONCURRENT}")
        print(f"✓ 目标表: {WEATHER_6M_TARGET_TABLE}")
        
        # 测试配置函数
        config = get_weather_6m_config()
        print(f"✓ 配置函数返回: {len(config)} 个配置项")
        
        return True
        
    except Exception as e:
        print(f"✗ 6分钟配置测试失败: {e}")
        return False


def test_weather_1h_config():
    """测试1小时天气处理配置"""
    print("\n=== 测试1小时天气处理配置 ===")
    
    try:
        from config import (
            WEATHER_1H_DATA_TYPES, WEATHER_1H_BATCH_SIZE, WEATHER_1H_MAX_CONCURRENT,
            WEATHER_1H_PRIORITY_ORDER, WEATHER_1H_TARGET_TABLE, get_weather_1h_config
        )
        
        print(f"✓ 数据类型: {WEATHER_1H_DATA_TYPES}")
        print(f"✓ 批处理大小: {WEATHER_1H_BATCH_SIZE}")
        print(f"✓ 最大并发数: {WEATHER_1H_MAX_CONCURRENT}")
        print(f"✓ 处理优先级: {WEATHER_1H_PRIORITY_ORDER}")
        print(f"✓ 目标表: {WEATHER_1H_TARGET_TABLE}")
        
        # 测试配置函数
        config = get_weather_1h_config()
        print(f"✓ 配置函数返回: {len(config)} 个配置项")
        
        return True
        
    except Exception as e:
        print(f"✗ 1小时配置测试失败: {e}")
        return False


def test_validation_config():
    """测试数据验证配置"""
    print("\n=== 测试数据验证配置 ===")
    
    try:
        from config import (
            WEATHER_6M_REQUIRED_VARS, WEATHER_6M_VALUE_RANGE,
            WEATHER_1H_REQUIRED_VARS, WEATHER_1H_OPTIONAL_VARS, WEATHER_1H_VALUE_RANGES
        )
        
        print(f"✓ 6分钟必需变量: {WEATHER_6M_REQUIRED_VARS}")
        print(f"✓ 6分钟值范围: {WEATHER_6M_VALUE_RANGE}")
        print(f"✓ 1小时必需变量: {WEATHER_1H_REQUIRED_VARS}")
        print(f"✓ 1小时可选变量: {WEATHER_1H_OPTIONAL_VARS}")
        print(f"✓ 1小时值范围: {WEATHER_1H_VALUE_RANGES}")
        
        return True
        
    except Exception as e:
        print(f"✗ 验证配置测试失败: {e}")
        return False


def test_yaml_config_structure():
    """测试YAML配置结构"""
    print("\n=== 测试YAML配置结构 ===")
    
    try:
        from config import _yaml_config
        
        # 检查weather_processing配置
        weather_processing = _yaml_config.get("weather_processing", {})
        if weather_processing:
            print("✓ weather_processing 配置存在")
            
            # 检查6分钟配置
            weather_6m = weather_processing.get("weather_6m", {})
            if weather_6m:
                print("✓ weather_6m 配置存在")
                print(f"  - 数据源: {weather_6m.get('data_source', {})}")
                print(f"  - 处理参数: {weather_6m.get('processing', {})}")
                print(f"  - 数据库配置: {weather_6m.get('database', {})}")
            
            # 检查1小时配置
            weather_1h = weather_processing.get("weather_1h", {})
            if weather_1h:
                print("✓ weather_1h 配置存在")
                print(f"  - 数据源: {weather_1h.get('data_source', {})}")
                print(f"  - 处理参数: {weather_1h.get('processing', {})}")
                print(f"  - 数据库配置: {weather_1h.get('database', {})}")
        else:
            print("⚠ weather_processing 配置不存在")
        
        return True
        
    except Exception as e:
        print(f"✗ YAML配置结构测试失败: {e}")
        return False


def test_config_completeness():
    """测试配置完整性"""
    print("\n=== 测试配置完整性 ===")
    
    try:
        from config import get_weather_6m_config, get_weather_1h_config
        
        # 测试6分钟配置完整性
        config_6m = get_weather_6m_config()
        required_6m_keys = [
            'data_type', 'variable_name', 'batch_size', 'max_concurrent_db',
            'target_table', 'conflict_columns', 'required_variables'
        ]
        
        missing_6m = [key for key in required_6m_keys if key not in config_6m]
        if not missing_6m:
            print("✓ 6分钟配置完整")
        else:
            print(f"⚠ 6分钟配置缺少: {missing_6m}")
        
        # 测试1小时配置完整性
        config_1h = get_weather_1h_config()
        required_1h_keys = [
            'data_types', 'batch_size', 'max_concurrent_db', 'priority_order',
            'target_table', 'conflict_columns', 'required_variables'
        ]
        
        missing_1h = [key for key in required_1h_keys if key not in config_1h]
        if not missing_1h:
            print("✓ 1小时配置完整")
        else:
            print(f"⚠ 1小时配置缺少: {missing_1h}")
        
        return len(missing_6m) == 0 and len(missing_1h) == 0
        
    except Exception as e:
        print(f"✗ 配置完整性测试失败: {e}")
        return False


def main():
    """主测试函数"""
    print("=" * 60)
    print("新配置系统测试")
    print("=" * 60)
    
    tests = [
        test_weather_6m_config,
        test_weather_1h_config,
        test_validation_config,
        test_yaml_config_structure,
        test_config_completeness,
    ]
    
    passed = 0
    total = len(tests)
    
    for test_func in tests:
        try:
            if test_func():
                passed += 1
        except Exception as e:
            print(f"✗ 测试 {test_func.__name__} 发生异常: {e}")
    
    print("\n" + "=" * 60)
    print("测试结果统计:")
    print("=" * 60)
    print(f"总测试数: {total}")
    print(f"通过测试: {passed}")
    print(f"失败测试: {total - passed}")
    print(f"成功率: {passed/total*100:.1f}%")
    
    if passed == total:
        print("\n🎉 所有测试通过！新配置系统工作正常。")
        return 0
    else:
        print(f"\n⚠️ 有 {total - passed} 个测试失败，请检查配置。")
        return 1


if __name__ == "__main__":
    try:
        exit_code = main()
        sys.exit(exit_code)
    except KeyboardInterrupt:
        print("\n\n测试被用户中断。")
        sys.exit(1)
    except Exception as e:
        print(f"\n\n测试过程中发生未预期的错误: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)
