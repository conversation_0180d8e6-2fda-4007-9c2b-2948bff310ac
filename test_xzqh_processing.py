#!/usr/bin/env python3
"""
测试行政区划代码处理功能
"""

import sys
import os
import pandas as pd

# 添加src目录到路径
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

# 导入配置和函数
from config import MYSQL_CONFIG, SHP_FIELD_XZQH, XZQH_TARGET_DIGITS
from build_lookup import normalize_xzqh_code, get_region_mapping

# 函数现在从build_lookup模块导入，无需重复定义

def test_normalize_xzqh_code():
    """测试行政区划代码标准化函数"""
    print("=== 测试行政区划代码标准化 ===")
    print(f"预处理截取位数: {XZQH_TARGET_DIGITS}")
    print(f"行政区划字段名: {SHP_FIELD_XZQH}")

    # 测试用例：先截取前6位，然后按原逻辑处理
    test_cases = [
        # (输入, 期望输出)
        ("53", "53"),           # 2位省级，保持2位
        ("530000", "5300"),     # 6位去0后变4位市级
        ("5300", "5300"),       # 4位市级，保持4位
        ("530100", "5301"),     # 6位去0后变4位市级
        ("530102", "530102"),   # 6位县级，保持6位
        ("530110", "53011"),    # 6位去0后变5位，补充到6位县级
        ("530102000", "530102"), # 先截取到530102，去0后变6位县级
        ("530102001", "530102"), # 先截取到530102，去0后变6位县级
        ("530102001000", "530102"), # 先截取到530102，去0后变6位县级
        ("530102001001", "530102"), # 先截取到530102，去0后变6位县级
        ("000000", None),       # 全0
        ("", None),             # 空字符串
        (None, None),           # None值
        ("5301020010010", "530102"), # 超长，先截取到530102，去0后变6位县级
    ]

    for i, (input_code, expected) in enumerate(test_cases):
        result = normalize_xzqh_code(input_code)
        status = "✓" if result == expected else "✗"
        print(f"{status} 测试 {i+1}: '{input_code}' -> '{result}' (期望: '{expected}')")
        if result != expected:
            print(f"    实际结果与期望不符")

def test_region_mapping():
    """测试行政区划映射获取"""
    print("\n=== 测试行政区划映射获取 ===")
    
    try:
        mapping = get_region_mapping()
        if mapping:
            print(f"✓ 成功获取 {len(mapping)} 条映射关系")
            # 显示前几条示例
            sample_items = list(mapping.items())[:5]
            for code, name in sample_items:
                print(f"    {code}: {name}")
        else:
            print("✗ 未获取到映射关系")
    except Exception as e:
        print(f"✗ 获取映射关系时出错: {e}")

if __name__ == "__main__":
    test_normalize_xzqh_code()
    test_region_mapping()
