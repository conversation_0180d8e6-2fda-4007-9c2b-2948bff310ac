#!/usr/bin/env python3
# coding: utf-8
"""
天气数据定时任务调度器 - FastAPI应用
基于uvicorn的定时器任务系统，包括5个定时任务（1h任务4个参数，6m任务1个）
"""

import asyncio
import logging
import signal
import sys
from contextlib import asynccontextmanager
from datetime import datetime
from typing import Dict, Any, List
from logging.handlers import TimedRotatingFileHandler
from pathlib import Path

from fastapi import FastAPI, HTTPException
from fastapi.responses import JSONResponse
import uvicorn

# 导入调度器和任务管理器
from scheduler import weather_scheduler
from task_manager import task_manager, TaskStatus
from resource_manager import resource_manager
from uptime_checker import get_api_data
from task_state_manager import task_state_manager, get_task_states_summary, reset_all_task_states
from config import (
    SCHEDULER_WEB_HOST, SCHEDULER_WEB_PORT, SCHEDULER_WEB_RELOAD,
    SCHEDULER_LOG_LEVEL, SCHEDULER_LOG_FILE
)

# 配置日志
log_level = getattr(logging, SCHEDULER_LOG_LEVEL.upper(), logging.INFO)

# 创建自定义格式器
formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')

# 配置根日志记录器
root_logger = logging.getLogger()
root_logger.setLevel(log_level)

# 清除现有处理器
for handler in root_logger.handlers[:]:
    root_logger.removeHandler(handler)

# 添加控制台处理器
console_handler = logging.StreamHandler(sys.stdout)
console_handler.setLevel(log_level)
console_handler.setFormatter(formatter)
root_logger.addHandler(console_handler)

# 添加按天轮转的文件处理器
log_dir = Path(SCHEDULER_LOG_FILE).parent
log_dir.mkdir(parents=True, exist_ok=True)

# 使用TimedRotatingFileHandler实现按天轮转
# 文件名格式: weather_scheduler_YYYY-MM-DD.log
log_base_name = Path(SCHEDULER_LOG_FILE).stem
file_handler = TimedRotatingFileHandler(
    filename=SCHEDULER_LOG_FILE,
    when='midnight',  # 每天午夜轮转
    interval=1,       # 每1天轮转一次
    backupCount=30,   # 保留30天的日志文件
    encoding='utf-8',
    delay=False,
    utc=False
)
# 设置轮转后的文件名格式
file_handler.suffix = "%Y-%m-%d.log"
file_handler.setLevel(log_level)
file_handler.setFormatter(formatter)
root_logger.addHandler(file_handler)

# 配置uvicorn相关的日志记录器
uvicorn_loggers = [
    "uvicorn",
    "uvicorn.error",
    "uvicorn.access",
    "fastapi"
]

for logger_name in uvicorn_loggers:
    logger = logging.getLogger(logger_name)
    logger.setLevel(log_level)
    logger.handlers = []  # 清除默认处理器
    logger.addHandler(console_handler)
    logger.addHandler(file_handler)
    logger.propagate = False  # 防止重复日志

logger = logging.getLogger(__name__)


@asynccontextmanager
async def lifespan(app: FastAPI):
    """应用生命周期管理"""
    logger.info("启动天气数据定时任务调度器...")
    
    # 启动调度器
    try:
        await weather_scheduler.start()
        logger.info("调度器启动成功")
    except Exception as e:
        logger.error(f"调度器启动失败: {e}")
        raise
    
    yield
    
    # 关闭调度器
    logger.info("关闭天气数据定时任务调度器...")
    try:
        await weather_scheduler.stop()
        logger.info("调度器关闭成功")
    except Exception as e:
        logger.error(f"调度器关闭失败: {e}")


# 创建FastAPI应用
app = FastAPI(
    title="天气数据定时任务调度器",
    description="基于uvicorn的天气数据处理定时任务系统",
    version="1.0.0",
    lifespan=lifespan
)


@app.get("/")
async def root():
    """根路径"""
    return {
        "message": "天气数据定时任务调度器",
        "version": "1.0.0",
        "status": "running",
        "timestamp": datetime.now().isoformat()
    }


@app.get("/health")
async def health_check():
    """健康检查"""
    try:
        scheduler_status = weather_scheduler.get_status()
        return {
            "status": "healthy",
            "timestamp": datetime.now().isoformat(),
            "scheduler": scheduler_status
        }
    except Exception as e:
        logger.error(f"健康检查失败: {e}")
        raise HTTPException(status_code=500, detail=f"健康检查失败: {str(e)}")


@app.get("/status")
async def get_status():
    """获取调度器状态"""
    try:
        return weather_scheduler.get_status()
    except Exception as e:
        logger.error(f"获取状态失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取状态失败: {str(e)}")


@app.get("/tasks")
async def get_all_tasks():
    """获取所有任务"""
    try:
        tasks = task_manager.get_all_tasks()
        return {
            "total_tasks": len(tasks),
            "tasks": {
                task_id: {
                    "task_id": task.task_id,
                    "task_type": task.task_type,
                    "data_type": task.data_type,
                    "status": task.status.value,
                    "scheduled_time": task.scheduled_time.isoformat(),
                    "start_time": task.start_time.isoformat() if task.start_time else None,
                    "end_time": task.end_time.isoformat() if task.end_time else None,
                    "duration_seconds": task.duration.total_seconds() if task.duration else None,
                    "error_message": task.error_message,
                    "retry_count": task.retry_count,
                    "timeout_minutes": task.timeout_minutes
                }
                for task_id, task in tasks.items()
            }
        }
    except Exception as e:
        logger.error(f"获取任务列表失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取任务列表失败: {str(e)}")


@app.get("/tasks/running")
async def get_running_tasks():
    """获取正在运行的任务"""
    try:
        running_tasks = task_manager.get_running_tasks()
        return {
            "running_count": len(running_tasks),
            "tasks": {
                task_id: {
                    "task_id": task.task_id,
                    "task_type": task.task_type,
                    "data_type": task.data_type,
                    "status": task.status.value,
                    "start_time": task.start_time.isoformat() if task.start_time else None,
                    "duration_seconds": task.duration.total_seconds() if task.duration else None,
                    "timeout_minutes": task.timeout_minutes,
                    "is_timeout": task.is_timeout
                }
                for task_id, task in running_tasks.items()
            }
        }
    except Exception as e:
        logger.error(f"获取运行中任务失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取运行中任务失败: {str(e)}")


@app.get("/tasks/{task_id}")
async def get_task_status(task_id: str):
    """获取指定任务状态"""
    try:
        task = task_manager.get_task_status(task_id)
        if not task:
            raise HTTPException(status_code=404, detail=f"任务 {task_id} 不存在")
        
        return {
            "task_id": task.task_id,
            "task_type": task.task_type,
            "data_type": task.data_type,
            "status": task.status.value,
            "scheduled_time": task.scheduled_time.isoformat(),
            "start_time": task.start_time.isoformat() if task.start_time else None,
            "end_time": task.end_time.isoformat() if task.end_time else None,
            "duration_seconds": task.duration.total_seconds() if task.duration else None,
            "error_message": task.error_message,
            "retry_count": task.retry_count,
            "timeout_minutes": task.timeout_minutes,
            "is_timeout": task.is_timeout
        }
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取任务状态失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取任务状态失败: {str(e)}")


@app.get("/queues")
async def get_queue_stats():
    """获取队列统计"""
    try:
        return {
            "queue_stats": task_manager.get_queue_stats(),
            "timestamp": datetime.now().isoformat()
        }
    except Exception as e:
        logger.error(f"获取队列统计失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取队列统计失败: {str(e)}")


@app.get("/resources")
async def get_resource_stats():
    """获取资源池统计"""
    try:
        return resource_manager.get_stats()
    except Exception as e:
        logger.error(f"获取资源统计失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取资源统计失败: {str(e)}")


@app.get("/api-data/{data_type}")
async def get_api_data_endpoint(data_type: str):
    """获取指定数据类型的API数据"""
    try:
        api_data = await get_api_data(data_type)
        return {
            "data_type": data_type,
            "api_data": api_data,
            "success": api_data is not None,
            "timestamp": datetime.now().isoformat()
        }
    except Exception as e:
        logger.error(f"获取API数据失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取API数据失败: {str(e)}")


@app.get("/schedule/next")
async def get_next_schedules():
    """获取下一次调度时间"""
    try:
        from scheduler import WeatherTaskScheduler
        temp_scheduler = WeatherTaskScheduler()
        
        return {
            "next_hourly_schedule": temp_scheduler._get_next_hourly_schedule().isoformat(),
            "next_six_minute_schedule": temp_scheduler._get_next_six_minute_schedule().isoformat(),
            "timestamp": datetime.now().isoformat()
        }
    except Exception as e:
        logger.error(f"获取调度时间失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取调度时间失败: {str(e)}")


@app.get("/task-states")
async def get_task_states():
    """获取所有任务状态"""
    try:
        return {
            "task_states": get_task_states_summary(),
            "statistics": task_state_manager.get_statistics(),
            "timestamp": datetime.now().isoformat()
        }
    except Exception as e:
        logger.error(f"获取任务状态失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取任务状态失败: {str(e)}")


@app.get("/task-states/{task_type}/{data_type}")
async def get_task_state(task_type: str, data_type: str):
    """获取指定任务的状态"""
    try:
        state = task_state_manager.get_task_state(task_type, data_type)
        if not state:
            raise HTTPException(status_code=404, detail=f"任务状态不存在: {task_type}_{data_type}")

        return {
            "task_key": state.task_key,
            "task_type": state.task_type,
            "data_type": state.data_type,
            "last_uptime": state.last_uptime,
            "last_update": state.last_update.isoformat() if state.last_update else None,
            "total_executions": state.total_executions,
            "is_first_run": state.is_first_run(),
            "timestamp": datetime.now().isoformat()
        }
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取任务状态失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取任务状态失败: {str(e)}")


@app.post("/task-states/reset")
async def reset_task_states():
    """重置所有任务状态"""
    try:
        reset_all_task_states()
        return {
            "message": "所有任务状态已重置",
            "timestamp": datetime.now().isoformat()
        }
    except Exception as e:
        logger.error(f"重置任务状态失败: {e}")
        raise HTTPException(status_code=500, detail=f"重置任务状态失败: {str(e)}")


@app.post("/task-states/{task_type}/{data_type}/reset")
async def reset_task_state(task_type: str, data_type: str):
    """重置指定任务状态"""
    try:
        task_state_manager.reset_task_state(task_type, data_type)
        return {
            "message": f"任务 {task_type}_{data_type} 状态已重置",
            "task_type": task_type,
            "data_type": data_type,
            "timestamp": datetime.now().isoformat()
        }
    except Exception as e:
        logger.error(f"重置任务状态失败: {e}")
        raise HTTPException(status_code=500, detail=f"重置任务状态失败: {str(e)}")


@app.post("/tasks/{task_id}/cancel")
async def cancel_task(task_id: str):
    """取消指定任务（仅对等待中的任务有效）"""
    try:
        task = task_manager.get_task_status(task_id)
        if not task:
            raise HTTPException(status_code=404, detail=f"任务 {task_id} 不存在")

        if task.status != TaskStatus.PENDING:
            raise HTTPException(
                status_code=400,
                detail=f"任务 {task_id} 状态为 {task.status.value}，无法取消"
            )

        # 这里可以添加取消逻辑
        # 目前的实现中，任务一旦提交到队列就会执行
        return {
            "message": f"任务 {task_id} 取消请求已提交",
            "task_id": task_id,
            "timestamp": datetime.now().isoformat()
        }
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"取消任务失败: {e}")
        raise HTTPException(status_code=500, detail=f"取消任务失败: {str(e)}")


def setup_signal_handlers():
    """设置信号处理器"""
    def signal_handler(signum, frame):
        logger.info(f"收到信号 {signum}，准备快速关闭应用...")

        # 立即设置关闭标志
        import asyncio
        try:
            # 获取当前事件循环
            loop = asyncio.get_event_loop()
            if loop.is_running():
                # 创建一个任务来快速关闭调度器
                loop.create_task(emergency_shutdown())
        except Exception as e:
            logger.error(f"快速关闭失败: {e}")

        # 短暂延迟后强制退出
        import threading
        def force_exit():
            import time
            time.sleep(3)  # 给3秒时间进行优雅关闭
            logger.warning("强制退出应用")
            import os
            os._exit(0)

        threading.Thread(target=force_exit, daemon=True).start()

    signal.signal(signal.SIGINT, signal_handler)
    signal.signal(signal.SIGTERM, signal_handler)


async def emergency_shutdown():
    """紧急关闭处理"""
    try:
        logger.info("开始紧急关闭流程...")

        # 快速停止调度器
        await weather_scheduler.stop()

        logger.info("紧急关闭完成")
    except Exception as e:
        logger.error(f"紧急关闭过程中出错: {e}")


def main():
    """主函数"""
    setup_signal_handlers()

    print(f"🚀 启动天气数据定时任务调度器...")
    print(f"📊 监听地址: http://{SCHEDULER_WEB_HOST}:{SCHEDULER_WEB_PORT}")
    print(f"📝 日志级别: {SCHEDULER_LOG_LEVEL}")
    print(f"📁 日志文件: {SCHEDULER_LOG_FILE}")
    print("=" * 60)

    # 启动uvicorn服务器
    uvicorn.run(
        "weather_scheduler_app:app",
        host=SCHEDULER_WEB_HOST,
        port=SCHEDULER_WEB_PORT,
        log_level=SCHEDULER_LOG_LEVEL.lower(),
        access_log=True,
        reload=SCHEDULER_WEB_RELOAD,
        # 禁用uvicorn的默认日志配置，使用我们自定义的
        log_config=None,
        # 强制输出到stdout
        use_colors=False
    )


if __name__ == "__main__":
    main()
