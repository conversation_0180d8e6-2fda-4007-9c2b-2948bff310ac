#!/usr/bin/env python3
# coding: utf-8
"""
测试路径解析功能
验证在不同工作目录下，路径解析是否正确
"""

import sys
import os
from pathlib import Path

# 添加src目录到Python路径
src_path = Path(__file__).parent / "src"
sys.path.insert(0, str(src_path))

def test_path_resolution():
    """测试路径解析功能"""
    print("=" * 60)
    print("测试路径解析功能")
    print("=" * 60)
    
    # 显示当前工作目录
    print(f"当前工作目录: {os.getcwd()}")
    print(f"脚本文件位置: {__file__}")
    print(f"脚本目录: {Path(__file__).parent}")
    
    try:
        # 导入配置
        from config import SCRIPT_DIR, DATA_PATHS, NETCDF_CONFIG, resolve_path
        
        print(f"\n脚本根目录 (SCRIPT_DIR): {SCRIPT_DIR}")
        print(f"脚本根目录是否存在: {SCRIPT_DIR.exists()}")
        
        print("\n=== 数据路径配置 ===")
        for key, path in DATA_PATHS.items():
            print(f"{key}: {path}")
            print(f"  -> 路径是否为绝对路径: {Path(path).is_absolute()}")
            print(f"  -> 路径是否存在: {Path(path).exists()}")
        
        print("\n=== NetCDF 配置 ===")
        print(f"default_file: {NETCDF_CONFIG['default_file']}")
        print(f"  -> 路径是否为绝对路径: {Path(NETCDF_CONFIG['default_file']).is_absolute()}")
        print(f"  -> 路径是否存在: {Path(NETCDF_CONFIG['default_file']).exists()}")
        
        print("\n=== 测试 resolve_path 函数 ===")
        test_paths = [
            "./data/weather",
            "/data/weather", 
            "data/test.txt",
            "/absolute/path/test.txt",
            None,
            ""
        ]
        
        for test_path in test_paths:
            resolved = resolve_path(test_path)
            print(f"输入: {test_path!r} -> 输出: {resolved!r}")
            if resolved:
                print(f"  -> 是否为绝对路径: {Path(resolved).is_absolute()}")
        
        print("\n=== 测试完成 ===")
        print("如果所有路径都是绝对路径，则配置正确")
        
    except Exception as e:
        print(f"测试失败: {e}")
        import traceback
        traceback.print_exc()

def test_different_working_directories():
    """测试在不同工作目录下的路径解析"""
    print("\n" + "=" * 60)
    print("测试不同工作目录下的路径解析")
    print("=" * 60)
    
    original_cwd = os.getcwd()
    
    try:
        from config import SCRIPT_DIR, DATA_PATHS
        
        # 测试目录列表
        test_dirs = [
            SCRIPT_DIR,  # 项目根目录
            SCRIPT_DIR / "src",  # src目录
            Path("/tmp") if Path("/tmp").exists() else Path.home(),  # 临时目录或用户目录
        ]
        
        for test_dir in test_dirs:
            if not test_dir.exists():
                print(f"跳过不存在的目录: {test_dir}")
                continue
                
            print(f"\n切换到目录: {test_dir}")
            os.chdir(test_dir)
            print(f"当前工作目录: {os.getcwd()}")
            
            # 重新导入配置以测试路径解析
            import importlib
            import config
            importlib.reload(config)
            
            print("数据路径:")
            for key, path in config.DATA_PATHS.items():
                print(f"  {key}: {path}")
                print(f"    -> 绝对路径: {Path(path).is_absolute()}")
                
    except Exception as e:
        print(f"测试失败: {e}")
        import traceback
        traceback.print_exc()
    finally:
        # 恢复原始工作目录
        os.chdir(original_cwd)
        print(f"\n恢复工作目录: {os.getcwd()}")

if __name__ == "__main__":
    test_path_resolution()
    test_different_working_directories()
