# 天气调度器快速停止优化说明

## 问题分析

之前WinSW和systemd守护进程停止慢的主要原因：

### 1. 配置问题
- **WinSW**: `stoptimeout`设置为30秒，等待时间过长
- **systemd**: 缺少停止超时配置，使用默认的90秒超时

### 2. 程序内部问题
- 异步任务链复杂，包含多个长期运行的调度任务
- 等待循环使用长时间的`asyncio.sleep()`，无法快速响应关闭信号
- 资源池关闭时等待所有任务完成，耗时较长
- 信号处理不够完善，没有主动触发快速关闭

## 优化方案

### 1. WinSW配置优化 (service.xml)

```xml
<!-- 减少停止超时时间 -->
<stoptimeout>10 sec</stoptimeout>

<!-- 使用专用停止脚本 -->
<stoparguments>D:\script\weather_script\stop_weather_scheduler.py</stoparguments>
<stopexecutable>D:\script\weather_script\.venv\Scripts\python.exe</stopexecutable>
```

**优化效果**:
- 停止超时从30秒减少到10秒
- 使用专用脚本强制杀死进程树，确保快速停止

### 2. systemd配置优化 (weather-scheduler.service)

```ini
# 设置停止超时时间为10秒
TimeoutStopSec=10s
# 发送SIGTERM信号给主进程
KillMode=mixed
# 如果SIGTERM无效，5秒后发送SIGKILL
TimeoutSec=5s
```

**优化效果**:
- 停止超时从默认90秒减少到10秒
- 使用混合杀死模式，先优雅关闭，后强制杀死

### 3. 程序内部优化

#### 3.1 信号处理优化
- 添加紧急关闭处理函数
- 设置3秒强制退出机制
- 立即设置关闭标志

#### 3.2 调度器停止优化
- 为所有异步操作添加超时控制
- 调度任务停止超时：2秒
- 任务管理器停止超时：3秒
- 资源管理器关闭超时：2秒

#### 3.3 可中断睡眠
- 将长时间的`asyncio.sleep()`替换为可中断睡眠
- 每秒检查一次关闭标志
- 快速响应关闭信号

#### 3.4 资源池快速关闭
- 线程池和进程池使用`shutdown(wait=False)`
- 数据库连接池关闭添加1秒超时
- 不等待任务完成，立即关闭

#### 3.5 任务管理器优化
- 停止时强制杀死所有正在运行的任务进程
- 监控任务快速取消，1秒超时

### 4. 专用停止脚本 (stop_weather_scheduler.py)

**功能**:
- 自动查找调度器进程
- 发送SIGTERM信号进行优雅关闭
- 3秒后强制杀死进程树
- 确保所有相关进程都被停止

## 使用方法

### Windows (WinSW)
1. 更新`service.xml`配置
2. 重新安装服务：
   ```cmd
   weather-scheduler.exe uninstall
   weather-scheduler.exe install
   ```
3. 测试停止速度：
   ```cmd
   weather-scheduler.exe stop
   ```

### Linux (systemd)
1. 更新`weather-scheduler.service`配置
2. 重新加载配置：
   ```bash
   sudo systemctl daemon-reload
   sudo systemctl restart weather-scheduler
   ```
3. 测试停止速度：
   ```bash
   sudo systemctl stop weather-scheduler
   ```

## 预期效果

### 优化前
- **WinSW**: 30秒停止超时
- **systemd**: 90秒停止超时
- 程序内部可能需要额外时间等待任务完成

### 优化后
- **WinSW**: 10秒内完成停止（通常3-5秒）
- **systemd**: 10秒内完成停止（通常3-5秒）
- 程序响应关闭信号更快，资源释放更及时

## 注意事项

1. **数据安全**: 快速停止可能会中断正在执行的任务，确保重要数据已持久化
2. **日志记录**: 停止过程会记录详细日志，便于问题排查
3. **进程清理**: 专用停止脚本确保所有子进程都被正确清理
4. **重启策略**: 服务配置保持原有的重启策略不变

## 故障排除

### 如果停止仍然很慢
1. 检查是否有任务卡在长时间操作中
2. 查看日志确认关闭流程是否正常执行
3. 手动运行停止脚本：`python stop_weather_scheduler.py`

### 如果服务无法正常重启
1. 检查进程是否完全清理：`tasklist | findstr python` (Windows) 或 `ps aux | grep python` (Linux)
2. 手动清理残留进程
3. 重新启动服务
