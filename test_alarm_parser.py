#!/usr/bin/env python3
# coding: utf-8
"""
预警内容解析功能测试脚本
"""

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

import pymysql
from src.weather_alarm import AlarmContentParser
from src.config import MYSQL_CONFIG

def test_alarm_parser():
    """测试预警内容解析功能"""
    
    # 测试用例
    test_cases = [
        {
            "content": "墨江县气象台2025年7月18日07时05分发布大雾黄色预警信号：未来12小时墨江县大部乡镇将出现浓雾，请做好防护。（预警信息来源：国家预警信息发布中心）",
            "geo_code": "530800",  # 假设的geo_code
            "expected_admin": "墨江县"
        },
        {
            "content": "会泽县气象台2025年7月17日16时25分发布高温黄色预警信号：未来3天娜姑镇、纸厂乡的低矮河谷地区最高气温将在35℃以上，请注意防范。（预警信息来源：国家预警信息发布中心）",
            "geo_code": "530300",  # 假设的geo_code
            "expected_admin": "会泽县"
        },
        {
            "content": "彝良县气象台2025年7月18日9时00分继续发布高温橙色预警信号：未来24小时，彝良县洛旺、柳溪、牛街、角奎、发界、海子、洛泽河、两河等乡镇（街道）海拔1200米以下低矮河谷区域日最高气温将升至37℃以上，午后请减少户外活动。（预警信息来源：国家预警信息发布中心）",
            "geo_code": "530600",  # 假设的geo_code
            "expected_admin": "彝良县"
        },
        {
            "content": "绥江县气象台2025年7月18日08时34分发布高温橙色预警信号：未来24小时，我县南岸镇、板栗镇、中城镇、新滩镇、会仪镇最高气温将升至37℃以上，午后请减少户外活动。（预警信息来源：国家预警信息发布中心）",
            "geo_code": "530600",  # 假设的geo_code
            "expected_admin": "绥江县"
        }
    ]
    
    try:
        # 连接MySQL数据库
        mysql_conn = pymysql.connect(
            host=MYSQL_CONFIG["host"],
            port=MYSQL_CONFIG["port"],
            user=MYSQL_CONFIG["username"],
            password=MYSQL_CONFIG["password"],
            database=MYSQL_CONFIG["database"],
            charset='utf8mb4',
            autocommit=True
        )
        
        # 创建解析器
        parser = AlarmContentParser(mysql_conn)
        
        print("=" * 80)
        print("预警内容解析功能测试")
        print("=" * 80)
        
        for i, test_case in enumerate(test_cases, 1):
            print(f"\n测试用例 {i}:")
            print("-" * 60)
            
            content = test_case["content"]
            geo_code = test_case["geo_code"]
            expected_admin = test_case["expected_admin"]
            
            print(f"预警内容: {content[:100]}...")
            print(f"预期行政区: {expected_admin}")
            
            # 1. 测试行政区名称提取
            admin_region = parser.extract_admin_region_name(content)
            print(f"提取的行政区: {admin_region}")
            print(f"行政区提取{'✓' if admin_region == expected_admin else '✗'}")
            
            # 2. 测试冒号后内容提取
            content_after_colon = parser.extract_content_after_colon(content)
            print(f"冒号后内容: {content_after_colon[:100]}...")
            
            # 3. 测试时间和地点提取
            location_text = parser.extract_time_and_location(content_after_colon)
            print(f"提取的地点文本: {location_text}")
            
            # 4. 测试前缀清理
            cleaned_location = parser.clean_location_prefix(location_text, admin_region)
            print(f"清理后的地点: {cleaned_location}")
            
            # 5. 测试地点分割
            locations = parser.split_locations(cleaned_location)
            print(f"分割后的地点: {locations}")
            
            # 6. 获取区域数据进行匹配测试
            region_data = parser.get_region_data_by_geo_codes([geo_code])
            current_region_data = region_data.get(geo_code, [])
            
            if current_region_data:
                print(f"找到 {len(current_region_data)} 个子区域数据")
                
                # 7. 测试区域匹配
                matched_results = parser.match_region_names(locations, current_region_data)
                print(f"匹配结果: {len(matched_results)} 个")
                
                for j, result in enumerate(matched_results):
                    print(f"  匹配 {j+1}: {result['data_name']} ({result['data_code']})")
            else:
                print("未找到对应的子区域数据")
            
            # 8. 完整解析测试
            print("\n完整解析测试:")
            full_results = parser.parse_alarm_content(content, geo_code, region_data)
            print(f"完整解析结果: {len(full_results)} 个匹配")
            for j, result in enumerate(full_results):
                print(f"  结果 {j+1}: {result['data_name']} ({result['data_code']})")
        
        print("\n" + "=" * 80)
        print("测试完成")
        print("=" * 80)
        
        mysql_conn.close()
        
    except Exception as e:
        print(f"测试失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_alarm_parser()
