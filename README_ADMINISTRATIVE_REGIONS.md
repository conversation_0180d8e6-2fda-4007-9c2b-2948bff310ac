# 行政区数据加载脚本使用说明

## 概述

`load_administrative_regions.py` 脚本用于读取云南省市级和县级行政区的GeoJSON文件，处理地理编码，并将数据存储到PostgreSQL数据库中。

## 功能特性

- ✅ 读取市级和县级行政区GeoJSON文件
- ✅ 自动处理geo_code字段（根据区域类型进行不同的编码转换）
- ✅ 支持坐标系转换（自动转换为WGS84）
- ✅ 数据库表自动创建和索引优化
- ✅ 完整的日志记录和进度跟踪

## 数据处理规则

### geo_code字段处理

脚本会根据区域类型对原始GB代码进行不同的处理：

- **市级行政区**: 去掉前3个字符和后2个字符
  - 例如: `156530100` → `5301`
- **县级行政区**: 去掉前3个字符
  - 例如: `156530102` → `530102`

### 数据库表结构

创建的 `administrative_regions` 表包含以下字段：

| 字段名 | 类型 | 说明 |
|--------|------|------|
| geo_code | VARCHAR(20) | 地理编码（主键） |
| name | VARCHAR(100) | 行政区名称 |
| shape | GEOMETRY | 几何形状（WGS84坐标系） |

## 配置

在 `config.yml` 中配置文件路径：

```yaml
paths:
  # 行政区geojson文件配置
  administrative_regions:
    city_geojson: "./data/geojson/云南省_市.geojson"
    county_geojson: "./data/geojson/云南省_县.geojson"
```

## 使用方法

### 使用配置文件默认路径

```bash
uv run python src/load_administrative_regions.py
```

### 指定文件路径

```bash
uv run python src/load_administrative_regions.py ./data/geojson/云南省_市.geojson ./data/geojson/云南省_县.geojson
```

## 运行结果

脚本成功运行后会输出类似以下的日志：

```
2025-07-18 15:38:39 - INFO - 🚀 开始加载行政区数据...
2025-07-18 15:38:39 - INFO - ✅ 输入文件验证通过
2025-07-18 15:38:39 - INFO - ✅ 数据库连接成功
2025-07-18 15:38:39 - INFO - 📖 读取city级行政区文件
2025-07-18 15:38:39 - INFO -    原始数据: 16 条记录
2025-07-18 15:38:39 - INFO -    处理完成: 16 条有效记录
2025-07-18 15:38:39 - INFO - 📖 读取county级行政区文件
2025-07-18 15:38:39 - INFO -    原始数据: 129 条记录
2025-07-18 15:38:39 - INFO -    处理完成: 129 条有效记录
2025-07-18 15:38:39 - INFO - 🔄 合并市级和县级数据...
2025-07-18 15:38:39 - INFO -    合并后总计: 145 条记录
2025-07-18 15:38:39 - INFO - 🔧 创建数据库表和索引...
2025-07-18 15:38:39 - INFO - ✅ 表和索引创建成功
2025-07-18 15:38:39 - INFO - 💾 保存 145 条记录到数据库...
2025-07-18 15:38:40 - INFO - ✅ 数据保存成功
2025-07-18 15:38:40 - INFO - 📊 数据库中共有 145 条记录
2025-07-18 15:38:40 - INFO - 🎉 行政区数据加载完成！
```

## 数据验证

可以使用以下SQL查询验证数据：

```sql
-- 查看总记录数
SELECT COUNT(*) FROM administrative_regions;

-- 查看表结构
SELECT column_name, data_type 
FROM information_schema.columns 
WHERE table_name = 'administrative_regions'
ORDER BY ordinal_position;

-- 查看具体数据
SELECT geo_code, name FROM administrative_regions ORDER BY geo_code LIMIT 10;

-- 查看市级数据（geo_code长度为4）
SELECT geo_code, name FROM administrative_regions 
WHERE LENGTH(geo_code) = 4 
ORDER BY geo_code;

-- 查看县级数据（geo_code长度为6）
SELECT geo_code, name FROM administrative_regions 
WHERE LENGTH(geo_code) = 6 
ORDER BY geo_code;
```

## 示例数据

加载完成后，数据库中会包含如下数据：

```
geo_code | name
---------|--------
5301     | 昆明市
530102   | 五华区
530103   | 盘龙区
530111   | 官渡区
530112   | 西山区
530113   | 东川区
530114   | 呈贡区
530115   | 晋宁区
...      | ...
```

## 注意事项

1. **数据替换**: 每次运行脚本都会完全替换表中的数据
2. **坐标系**: 自动转换为WGS84坐标系（EPSG:4326）
3. **编码处理**: 确保GeoJSON文件使用UTF-8编码
4. **数据库权限**: 确保数据库用户有创建表和索引的权限
5. **文件格式**: 支持标准的GeoJSON格式，要求包含name和gb字段

## 故障排除

### 常见问题

1. **ImportError**: 确保已安装所有依赖包
   ```bash
   uv sync
   ```

2. **数据库连接失败**: 检查config.yml中的数据库配置

3. **文件读取失败**: 确保GeoJSON文件路径正确且文件格式有效

4. **几何数据无效**: 检查GeoJSON中的几何数据是否有效

### 调试模式

可以通过修改日志级别来获得更详细的调试信息：

```python
logging.basicConfig(level=logging.DEBUG)
```

## 性能优化

- 使用批量插入操作提高数据库写入性能
- 创建适当的索引优化查询性能
- 支持大文件处理，内存使用优化
- 使用事务确保数据一致性
