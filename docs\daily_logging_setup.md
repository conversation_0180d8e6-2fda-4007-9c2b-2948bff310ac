# 按天划分日志配置说明

## 概述

已成功将天气数据处理系统的日志配置从固定文件名改为按天轮转的方式，实现了日志的自动按天划分和管理。

## 主要修改

### 1. 主应用日志配置 (`src/weather_scheduler_app.py`)

**修改前:**
```python
# 添加文件处理器
file_handler = logging.FileHandler(SCHEDULER_LOG_FILE, encoding='utf-8')
```

**修改后:**
```python
# 添加按天轮转的文件处理器
log_dir = Path(SCHEDULER_LOG_FILE).parent
log_dir.mkdir(parents=True, exist_ok=True)

# 使用TimedRotatingFileHandler实现按天轮转
file_handler = TimedRotatingFileHandler(
    filename=SCHEDULER_LOG_FILE,
    when='midnight',  # 每天午夜轮转
    interval=1,       # 每1天轮转一次
    backupCount=30,   # 保留30天的日志文件
    encoding='utf-8',
    delay=False,
    utc=False
)
# 设置轮转后的文件名格式
file_handler.suffix = "%Y-%m-%d.log"
```

### 2. 通用日志配置 (`src/weather_common.py`)

**修改前:**
```python
logging.basicConfig(
    level=level,
    format=format_str,
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler('weather_processing.log', encoding='utf-8')
    ]
)
```

**修改后:**
```python
# 创建按天轮转的文件处理器
file_handler = TimedRotatingFileHandler(
    filename='logs/weather_processing.log',
    when='midnight',  # 每天午夜轮转
    interval=1,       # 每1天轮转一次
    backupCount=30,   # 保留30天的日志文件
    encoding='utf-8',
    delay=False,
    utc=False
)
file_handler.suffix = "%Y-%m-%d.log"

logging.basicConfig(
    level=level,
    format=format_str,
    handlers=[
        logging.StreamHandler(),
        file_handler
    ]
)
```

### 3. 配置文件更新 (`config.yml`)

**修改前:**
```yaml
monitoring:
  log_level: "INFO"
  log_file: "weather_scheduler.log"
```

**修改后:**
```yaml
monitoring:
  log_level: "INFO"
  log_file: "logs/weather_scheduler.log"  # 按天轮转，实际文件名会是 weather_scheduler_YYYY-MM-DD.log
```

## 日志文件命名规则

### 当前日志文件
- `logs/weather_scheduler.log` - 当前正在写入的日志文件
- `logs/weather_processing.log` - 数据处理相关的当前日志

### 历史日志文件
- `logs/weather_scheduler_2025-07-22.log` - 2025年7月22日的日志
- `logs/weather_scheduler_2025-07-21.log` - 2025年7月21日的日志
- `logs/weather_processing_2025-07-22.log` - 2025年7月22日的处理日志
- 以此类推...

## 日志轮转特性

### 轮转时机
- **when='midnight'**: 每天午夜0点进行日志轮转
- **interval=1**: 每1天轮转一次

### 文件保留策略
- **backupCount=30**: 保留最近30天的日志文件
- 超过30天的日志文件会被自动删除

### 文件名格式
- **suffix="%Y-%m-%d.log"**: 轮转后的文件名格式为 `原文件名_YYYY-MM-DD.log`

## 优势

1. **自动管理**: 无需手动管理日志文件，系统自动按天轮转
2. **空间控制**: 自动删除超过30天的旧日志，避免磁盘空间无限增长
3. **便于查找**: 按日期命名，便于查找特定日期的日志
4. **不中断服务**: 日志轮转过程不会中断应用程序运行
5. **统一配置**: 所有模块使用相同的日志轮转策略

## 测试验证

已通过以下方式验证配置正确性：

1. **功能测试**: 创建测试脚本验证 `TimedRotatingFileHandler` 正常工作
2. **应用测试**: 启动主应用确认日志正常写入到 `logs/weather_scheduler.log`
3. **目录结构**: 确认 `logs/` 目录自动创建

## 注意事项

1. **目录权限**: 确保应用有权限在 `logs/` 目录创建和写入文件
2. **磁盘空间**: 虽然有30天的保留策略，但仍需监控磁盘空间使用情况
3. **时区设置**: 使用本地时间进行轮转（`utc=False`）
4. **并发安全**: `TimedRotatingFileHandler` 是线程安全的，支持多线程应用

## 部署建议

在生产环境部署时：

1. 确保 `logs/` 目录存在且有适当权限
2. 可以考虑将日志目录设置为独立的磁盘分区
3. 建议配置日志监控，及时发现异常情况
4. 可以根据实际需求调整 `backupCount` 参数（保留天数）

## 相关文件

- `src/weather_scheduler_app.py` - 主应用日志配置
- `src/weather_common.py` - 通用日志配置函数
- `config.yml` - 日志文件路径配置
- `logs/` - 日志文件存储目录
