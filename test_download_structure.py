#!/usr/bin/env python3
# coding: utf-8
"""
测试新的下载目录结构功能
"""

import os
import sys
import tempfile
import shutil
from pathlib import Path
from datetime import datetime, timedelta

# 添加src目录到Python路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from weather_download import WeatherDownloader
import logging

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


def test_date_based_directories():
    """测试基于日期的目录创建"""
    logger.info("开始测试基于日期的目录创建...")
    
    with tempfile.TemporaryDirectory() as temp_dir:
        temp_path = Path(temp_dir)
        
        try:
            # 创建下载器实例
            downloader = WeatherDownloader("gz_mpfv3")
            
            # 临时修改基础目录到测试目录
            original_base_dir = downloader.base_dir
            downloader.base_dir = temp_path / "test_data"
            
            # 测试获取基于日期的目录
            test_date = datetime(2025, 7, 22, 14, 30, 0)
            download_dir, nc_file_dir = downloader._get_date_based_dirs(test_date)
            
            expected_date_dir = temp_path / "test_data" / "2025" / "07" / "22"
            expected_download_dir = expected_date_dir / "downloads"
            expected_nc_dir = expected_date_dir / "nc_files"
            
            # 验证目录结构
            if download_dir == expected_download_dir and nc_file_dir == expected_nc_dir:
                logger.info("✓ 目录路径生成正确")
            else:
                logger.error(f"✗ 目录路径生成错误")
                logger.error(f"  预期下载目录: {expected_download_dir}")
                logger.error(f"  实际下载目录: {download_dir}")
                logger.error(f"  预期NC目录: {expected_nc_dir}")
                logger.error(f"  实际NC目录: {nc_file_dir}")
                return False
            
            # 验证目录是否被创建
            if download_dir.exists() and nc_file_dir.exists():
                logger.info("✓ 目录创建成功")
            else:
                logger.error("✗ 目录创建失败")
                return False
            
            # 恢复原始基础目录
            downloader.base_dir = original_base_dir
            
            return True
            
        except Exception as e:
            logger.error(f"测试过程中发生错误: {e}")
            return False


def test_get_latest_nc_file():
    """测试获取最新NC文件功能"""
    logger.info("开始测试获取最新NC文件功能...")
    
    with tempfile.TemporaryDirectory() as temp_dir:
        temp_path = Path(temp_dir)
        
        try:
            # 创建下载器实例
            downloader = WeatherDownloader("gz_mpfv3")
            
            # 临时修改基础目录
            original_base_dir = downloader.base_dir
            downloader.base_dir = temp_path / "test_data"
            
            # 创建测试文件
            test_dates = [
                datetime(2025, 7, 20),
                datetime(2025, 7, 21),
                datetime(2025, 7, 22)
            ]
            
            created_files = []
            for i, test_date in enumerate(test_dates):
                _, nc_file_dir = downloader._get_date_based_dirs(test_date)
                test_file = nc_file_dir / f"test_file_{i}.nc"
                test_file.write_text(f"测试文件 {i}")
                created_files.append(test_file)
                
                # 设置不同的修改时间
                import time
                time.sleep(0.1)  # 确保文件时间不同
            
            # 测试获取最新文件
            latest_file = downloader.get_latest_nc_file(search_days=7)
            
            if latest_file and Path(latest_file) == created_files[-1]:
                logger.info("✓ 获取最新NC文件功能正常")
            else:
                logger.error(f"✗ 获取最新NC文件功能异常")
                logger.error(f"  预期文件: {created_files[-1]}")
                logger.error(f"  实际文件: {latest_file}")
                return False
            
            # 测试按日期获取文件
            date_files = downloader.get_nc_files_by_date(test_dates[1])
            if len(date_files) == 1 and Path(date_files[0]) == created_files[1]:
                logger.info("✓ 按日期获取NC文件功能正常")
            else:
                logger.error(f"✗ 按日期获取NC文件功能异常")
                return False
            
            # 恢复原始基础目录
            downloader.base_dir = original_base_dir
            
            return True
            
        except Exception as e:
            logger.error(f"测试过程中发生错误: {e}")
            return False


def test_cleanup_downloads():
    """测试清理下载文件功能"""
    logger.info("开始测试清理下载文件功能...")
    
    with tempfile.TemporaryDirectory() as temp_dir:
        temp_path = Path(temp_dir)
        
        try:
            # 创建下载器实例
            downloader = WeatherDownloader("gz_mpfv3")
            
            # 临时修改基础目录
            original_base_dir = downloader.base_dir
            downloader.base_dir = temp_path / "test_data"
            
            # 创建测试下载文件
            old_date = datetime.now() - timedelta(days=10)
            recent_date = datetime.now()
            
            # 创建旧文件
            old_download_dir, _ = downloader._get_date_based_dirs(old_date)
            old_file = old_download_dir / "old_download.tar.gz"
            old_file.write_text("旧下载文件")
            
            # 创建新文件
            recent_download_dir, _ = downloader._get_date_based_dirs(recent_date)
            recent_file = recent_download_dir / "recent_download.tar.gz"
            recent_file.write_text("新下载文件")
            
            # 执行清理（保留7天）
            downloader.cleanup_old_downloads(keep_days=7)
            
            # 验证清理结果
            if not old_file.exists() and recent_file.exists():
                logger.info("✓ 清理下载文件功能正常")
            else:
                logger.error("✗ 清理下载文件功能异常")
                logger.error(f"  旧文件存在: {old_file.exists()}")
                logger.error(f"  新文件存在: {recent_file.exists()}")
                return False
            
            # 恢复原始基础目录
            downloader.base_dir = original_base_dir
            
            return True
            
        except Exception as e:
            logger.error(f"测试过程中发生错误: {e}")
            return False


def show_new_structure_example():
    """展示新的目录结构示例"""
    logger.info("新的下载和存储目录结构示例:")
    logger.info("data/")
    logger.info("├── precipitation_6min/")
    logger.info("│   ├── 2025/")
    logger.info("│   │   ├── 07/")
    logger.info("│   │   │   ├── 22/")
    logger.info("│   │   │   │   ├── downloads/")
    logger.info("│   │   │   │   │   └── gz_mpfv3_202507221430.tar.gz")
    logger.info("│   │   │   │   └── nc_files/")
    logger.info("│   │   │   │       ├── MPF_20250722143000.nc")
    logger.info("│   │   │   │       └── MPF_20250722143600.nc")
    logger.info("│   │   │   └── 23/")
    logger.info("│   │   │       ├── downloads/")
    logger.info("│   │   │       └── nc_files/")
    logger.info("│   │   └── 08/")
    logger.info("│   │       └── 01/")
    logger.info("│   └── backup/")
    logger.info("│       └── 2025/")
    logger.info("│           └── 07/")
    logger.info("│               └── 22/")
    logger.info("│                   └── old_file.nc")
    logger.info("└── temperature_1h/")
    logger.info("    ├── 2025/")
    logger.info("    │   └── 07/")
    logger.info("    │       └── 22/")
    logger.info("    │           ├── downloads/")
    logger.info("    │           └── nc_files/")
    logger.info("    └── backup/")
    logger.info("")
    logger.info("优势:")
    logger.info("- 下载文件和NC文件按日期自动分类")
    logger.info("- 便于查找特定日期的数据")
    logger.info("- 支持按日期批量清理")
    logger.info("- 避免单个目录文件过多")
    logger.info("- 备份文件也按日期组织")


if __name__ == "__main__":
    print("=" * 60)
    print("天气数据下载目录结构测试")
    print("=" * 60)
    
    # 显示新结构示例
    show_new_structure_example()
    
    print("\n" + "=" * 60)
    print("开始功能测试")
    print("=" * 60)
    
    # 运行测试
    test1_success = test_date_based_directories()
    test2_success = test_get_latest_nc_file()
    test3_success = test_cleanup_downloads()
    
    print("\n" + "=" * 60)
    print("测试结果汇总")
    print("=" * 60)
    
    if test1_success and test2_success and test3_success:
        print("✓ 所有测试通过！新的下载目录结构功能正常")
    else:
        print("✗ 部分测试失败，请检查错误信息")
        
    print("\n使用说明:")
    print("1. 下载的文件现在会自动按 年/月/日 的目录结构保存")
    print("2. NC文件解压后也会保存到对应日期的目录中")
    print("3. 支持按日期查找和获取文件")
    print("4. 清理功能会自动处理新的目录结构")
    print("5. 备份文件也按相同的日期结构组织")
