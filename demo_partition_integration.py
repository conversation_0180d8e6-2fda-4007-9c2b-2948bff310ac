#!/usr/bin/env python3
# coding: utf-8
"""
演示分区管理集成功能
展示 create_forecast_tables.py 的集成分区管理功能
"""

from sqlalchemy import create_engine, text
from src.config import PG_URL

def check_prerequisites():
    """
    检查前置条件
    """
    engine = create_engine(PG_URL)
    
    print("检查前置条件...")
    
    try:
        with engine.begin() as conn:
            # 检查数据库连接
            conn.execute(text("SELECT 1"))
            print("✓ 数据库连接正常")
            
            # 检查扩展
            extensions = conn.execute(text("""
                SELECT extname, extversion 
                FROM pg_extension 
                WHERE extname IN ('pg_partman', 'pg_cron')
                ORDER BY extname
            """)).fetchall()
            
            partman_installed = False
            cron_installed = False
            
            if extensions:
                print("✓ 已安装的扩展:")
                for ext in extensions:
                    print(f"  - {ext[0]} v{ext[1]}")
                    if ext[0] == 'pg_partman':
                        partman_installed = True
                    elif ext[0] == 'pg_cron':
                        cron_installed = True
            
            # 检查缺失的扩展
            missing_extensions = []
            if not partman_installed:
                missing_extensions.append('pg_partman')
            if not cron_installed:
                missing_extensions.append('pg_cron')
            
            if missing_extensions:
                print(f"⚠️ 缺少扩展: {', '.join(missing_extensions)}")
                print("建议安装命令:")
                for ext in missing_extensions:
                    print(f"  CREATE EXTENSION {ext};")
                return False, missing_extensions
            else:
                print("✓ 所有必需扩展已安装")
                return True, []
                
    except Exception as e:
        print(f"❌ 检查前置条件失败: {e}")
        return False, []
    finally:
        engine.dispose()

def simulate_table_creation():
    """
    模拟表创建过程（不实际创建）
    """
    print("\n模拟表创建过程...")
    
    tables = [
        "weather_cell_6m",
        "weather_cell_1h", 
        "weather_alarm",
        "forecast_precipitation_6min_line",
        "forecast_precipitation_6min_polygon",
        "forecast_precipitation_6min_relation",
        "forecast_precipitation_hourly_line",
        "forecast_precipitation_hourly_polygon", 
        "forecast_precipitation_hourly_relation",
        "forecast_precipitation_summary_line",
        "forecast_precipitation_summary_polygon",
        "forecast_precipitation_summary_relation"
    ]
    
    print("将创建以下分区表:")
    for i, table in enumerate(tables, 1):
        print(f"  {i:2d}. {table}")
    
    print(f"\n总计: {len(tables)} 个分区表")

def simulate_partition_config():
    """
    模拟分区配置过程
    """
    print("\n模拟分区配置过程...")
    
    # 核心数据表配置
    core_tables = [
        "weather_cell_6m",
        "weather_cell_1h",
        "forecast_precipitation_6min_line",
        "forecast_precipitation_6min_polygon", 
        "forecast_precipitation_6min_relation",
        "forecast_precipitation_hourly_line",
        "forecast_precipitation_hourly_polygon",
        "forecast_precipitation_hourly_relation",
        "forecast_precipitation_summary_line",
        "forecast_precipitation_summary_polygon",
        "forecast_precipitation_summary_relation"
    ]
    
    print("核心数据表配置 (每日分区, 保留90天):")
    for table in core_tables:
        print(f"  ✓ {table}")
        print(f"    - 分区键: pre_time")
        print(f"    - 分区间隔: 1 day")
        print(f"    - 预创建: 7 天")
        print(f"    - 保留期: 90 天")
    
    print("\n预警表配置 (每月分区, 保留3年):")
    print("  ✓ weather_alarm")
    print("    - 分区键: publish_time")
    print("    - 分区间隔: 1 month")
    print("    - 预创建: 4 个月")
    print("    - 保留期: 3 年")
    
    print("\n自动维护任务:")
    print("  ✓ 定时任务: partman-maintenance")
    print("    - 调度: 每天凌晨2点 (0 2 * * *)")
    print("    - 命令: CALL partman.run_maintenance_proc()")

def show_expected_output():
    """
    显示期望的输出
    """
    print("\n期望的 create_forecast_tables.py 输出:")
    print("=" * 60)
    
    expected_output = """
开始创建降水预报相关数据表...
创建表: weather_cell_6m
✓ weather_cell_6m 表创建/检查完成
创建表: weather_cell_1h
✓ weather_cell_1h 表创建/检查完成
创建表: weather_alarm
✓ weather_alarm 表创建/检查完成
... (其他表) ...

✓ 所有预报表创建完成

开始创建PostgreSQL函数...
✓ get_rainfall_type 函数创建完成
✓ get_rainfall_type_hourly 函数创建完成

开始创建存储过程...
✓ sp_precip_polygon 存储过程创建完成
✓ sp_precip_line 存储过程创建完成

✓ 所有函数和存储过程创建完成

开始配置分区管理...
✓ pg_partman 扩展已安装
配置核心数据表 (每日分区, 保留90天):
  ✓ public.weather_cell_6m
  ✓ public.weather_cell_1h
  ✓ public.forecast_precipitation_6min_line
  ✓ public.forecast_precipitation_6min_polygon
  ✓ public.forecast_precipitation_6min_relation
  ✓ public.forecast_precipitation_hourly_line
  ✓ public.forecast_precipitation_hourly_polygon
  ✓ public.forecast_precipitation_hourly_relation
  ✓ public.forecast_precipitation_summary_line
  ✓ public.forecast_precipitation_summary_polygon
  ✓ public.forecast_precipitation_summary_relation
配置预警表 (每月分区, 保留3年):
  ✓ public.weather_alarm
设置自动维护任务:
  ✓ 定时维护任务已创建 (ID: 1)
✓ 分区管理配置完成
"""
    
    print(expected_output.strip())
    print("=" * 60)

def show_verification_commands():
    """
    显示验证命令
    """
    print("\n验证命令:")
    print("=" * 40)
    
    commands = [
        ("集成测试", "python test_integrated_partition_setup.py"),
        ("分区配置验证", "python verify_partman_setup.py"),
        ("分区表结构验证", "python test_partition_tables.py"),
        ("手动运行维护", "psql -d db -c 'CALL partman.run_maintenance_proc()'"),
        ("查看分区配置", "psql -d db -c 'SELECT * FROM partman.part_config'"),
        ("查看定时任务", "psql -d db -c 'SELECT * FROM cron.job'")
    ]
    
    for desc, cmd in commands:
        print(f"{desc}:")
        print(f"  {cmd}")
        print()

def main():
    """
    主函数
    """
    print("分区管理集成功能演示")
    print("=" * 60)
    
    # 1. 检查前置条件
    ready, missing = check_prerequisites()
    
    if not ready:
        print(f"\n❌ 前置条件不满足，缺少扩展: {', '.join(missing)}")
        print("\n请先安装缺少的扩展，然后重新运行演示")
        return
    
    # 2. 模拟表创建
    simulate_table_creation()
    
    # 3. 模拟分区配置
    simulate_partition_config()
    
    # 4. 显示期望输出
    show_expected_output()
    
    # 5. 显示验证命令
    show_verification_commands()
    
    print("下一步操作:")
    print("=" * 40)
    print("1. 运行: python src/create_forecast_tables.py")
    print("2. 验证: python test_integrated_partition_setup.py")
    print("3. 开始使用分区表")
    
    print("\n✓ 演示完成！")

if __name__ == '__main__':
    main()
