#!/usr/bin/env python3
# coding: utf-8
"""
检查 pg_partman 分区管理状态

此脚本用于检查：
1. pg_partman 扩展是否安装
2. 分区配置是否正确
3. 自动维护任务是否设置
4. 现有分区状态
5. 手动运行维护任务

使用方法:
python check_partman_status.py
"""

from sqlalchemy import create_engine, text
from datetime import datetime, timedelta

# 导入统一配置
from config import PG_URL

def check_partman_extension(engine):
    """检查 pg_partman 扩展状态"""
    print("1. 检查 pg_partman 扩展状态...")
    
    try:
        with engine.connect() as conn:
            # 检查扩展是否安装
            result = conn.execute(text("""
                SELECT EXISTS (
                    SELECT 1 FROM pg_extension WHERE extname = 'pg_partman'
                )
            """)).scalar()
            
            if result:
                print("  ✓ pg_partman 扩展已安装")
                
                # 获取扩展版本
                version = conn.execute(text("""
                    SELECT extversion FROM pg_extension WHERE extname = 'pg_partman'
                """)).scalar()
                print(f"  ✓ 版本: {version}")
                
                return True
            else:
                print("  ❌ pg_partman 扩展未安装")
                print("     请运行: CREATE EXTENSION pg_partman;")
                return False
                
    except Exception as e:
        print(f"  ❌ 检查扩展时出错: {e}")
        return False

def check_partition_configs(engine):
    """检查分区配置"""
    print("\n2. 检查分区配置...")
    
    try:
        with engine.connect() as conn:
            configs = conn.execute(text("""
                SELECT 
                    parent_table,
                    control,
                    partition_type,
                    partition_interval,
                    premake,
                    retention,
                    retention_keep_table,
                    automatic_maintenance
                FROM partman.part_config
                ORDER BY parent_table
            """)).fetchall()
            
            if not configs:
                print("  ❌ 没有找到分区配置")
                return False
            
            print(f"  ✓ 找到 {len(configs)} 个分区配置:")
            
            for config in configs:
                print(f"    - {config.parent_table}")
                print(f"      控制字段: {config.control}")
                print(f"      分区间隔: {config.partition_interval}")
                print(f"      预创建: {config.premake}")
                print(f"      保留策略: {config.retention}")
                print(f"      自动维护: {config.automatic_maintenance}")
                print()
            
            return True
            
    except Exception as e:
        print(f"  ❌ 检查分区配置时出错: {e}")
        return False

def check_maintenance_job(engine):
    """检查自动维护任务"""
    print("3. 检查自动维护任务...")
    
    try:
        # 先检查当前数据库的 pg_cron
        with engine.connect() as conn:
            cron_exists = conn.execute(text("""
                SELECT EXISTS (
                    SELECT 1 FROM pg_extension WHERE extname = 'pg_cron'
                )
            """)).scalar()
            
            if cron_exists:
                print("  ✓ 当前数据库有 pg_cron 扩展")
                
                jobs = conn.execute(text("""
                    SELECT jobid, jobname, schedule, command, active
                    FROM cron.job
                    WHERE jobname LIKE '%partman%'
                """)).fetchall()
                
                if jobs:
                    print(f"  ✓ 找到 {len(jobs)} 个相关定时任务:")
                    for job in jobs:
                        status = "活跃" if job.active else "非活跃"
                        print(f"    - ID: {job.jobid}, 名称: {job.jobname}")
                        print(f"      计划: {job.schedule}, 状态: {status}")
                        print(f"      命令: {job.command}")
                else:
                    print("  ⚠️ 当前数据库没有找到 partman 维护任务")
            else:
                print("  ⚠️ 当前数据库没有 pg_cron 扩展")
        
        # 检查 gis 数据库的 pg_cron
        try:
            gis_url = PG_URL.replace('/middle-data-dev', '/gis')
            gis_engine = create_engine(gis_url)
            
            with gis_engine.connect() as gis_conn:
                cron_exists = gis_conn.execute(text("""
                    SELECT EXISTS (
                        SELECT 1 FROM pg_extension WHERE extname = 'pg_cron'
                    )
                """)).scalar()
                
                if cron_exists:
                    print("  ✓ gis 数据库有 pg_cron 扩展")
                    
                    jobs = gis_conn.execute(text("""
                        SELECT jobid, jobname, schedule, command, active
                        FROM cron.job
                        WHERE jobname LIKE '%partman%' OR command LIKE '%partman%'
                    """)).fetchall()
                    
                    if jobs:
                        print(f"  ✓ gis 数据库找到 {len(jobs)} 个相关定时任务:")
                        for job in jobs:
                            status = "活跃" if job.active else "非活跃"
                            print(f"    - ID: {job.jobid}, 名称: {job.jobname}")
                            print(f"      计划: {job.schedule}, 状态: {status}")
                            print(f"      命令: {job.command}")
                    else:
                        print("  ⚠️ gis 数据库没有找到 partman 维护任务")
                else:
                    print("  ⚠️ gis 数据库没有 pg_cron 扩展")
            
            gis_engine.dispose()
            
        except Exception as gis_error:
            print(f"  ⚠️ 检查 gis 数据库时出错: {gis_error}")
        
        return True
        
    except Exception as e:
        print(f"  ❌ 检查维护任务时出错: {e}")
        return False

def check_existing_partitions(engine):
    """检查现有分区"""
    print("\n4. 检查现有分区...")
    
    try:
        with engine.connect() as conn:
            # 获取所有分区表
            partitions = conn.execute(text("""
                SELECT 
                    schemaname,
                    tablename,
                    pg_size_pretty(pg_total_relation_size(schemaname||'.'||tablename)) as size
                FROM pg_tables 
                WHERE tablename LIKE '%_p20%'
                ORDER BY tablename
            """)).fetchall()
            
            if not partitions:
                print("  ⚠️ 没有找到分区表")
                return False
            
            print(f"  ✓ 找到 {len(partitions)} 个分区表:")
            
            # 按表名前缀分组
            partition_groups = {}
            for partition in partitions:
                # 提取表名前缀（去掉 _pYYYYMMDD 部分）
                table_name = partition.tablename
                if '_p20' in table_name:
                    prefix = table_name.split('_p20')[0]
                    if prefix not in partition_groups:
                        partition_groups[prefix] = []
                    partition_groups[prefix].append(partition)
            
            for prefix, group in partition_groups.items():
                print(f"    {prefix}: {len(group)} 个分区")
                # 显示最新的几个分区
                recent_partitions = sorted(group, key=lambda x: x.tablename)[-3:]
                for p in recent_partitions:
                    print(f"      - {p.tablename} ({p.size})")
            
            return True
            
    except Exception as e:
        print(f"  ❌ 检查现有分区时出错: {e}")
        return False

def run_maintenance_manually(engine):
    """手动运行维护任务"""
    print("\n5. 手动运行维护任务...")
    
    try:
        with engine.begin() as conn:
            print("  正在运行 partman.run_maintenance_proc()...")
            
            result = conn.execute(text("CALL partman.run_maintenance_proc()"))
            
            print("  ✓ 维护任务执行完成")
            
            # 检查是否有新分区被创建
            print("  检查维护结果...")
            
            # 获取最近创建的分区
            recent_partitions = conn.execute(text("""
                SELECT 
                    schemaname,
                    tablename
                FROM pg_tables 
                WHERE tablename LIKE '%_p20%'
                AND tablename > :today_partition
                ORDER BY tablename DESC
                LIMIT 10
            """), {"today_partition": f"weather_cell_1h_p{datetime.now().strftime('%Y%m%d')}"}).fetchall()
            
            if recent_partitions:
                print(f"  ✓ 找到 {len(recent_partitions)} 个最新分区:")
                for p in recent_partitions:
                    print(f"    - {p.tablename}")
            else:
                print("  ⚠️ 没有找到新创建的分区")
            
            return True
            
    except Exception as e:
        print(f"  ❌ 运行维护任务时出错: {e}")
        return False

def main():
    """主函数"""
    print("pg_partman 分区管理状态检查")
    print("=" * 50)
    
    engine = create_engine(PG_URL)
    
    try:
        # 1. 检查扩展
        if not check_partman_extension(engine):
            print("\n❌ pg_partman 扩展未安装，无法继续检查")
            return
        
        # 2. 检查配置
        check_partition_configs(engine)
        
        # 3. 检查维护任务
        check_maintenance_job(engine)
        
        # 4. 检查现有分区
        check_existing_partitions(engine)
        
        # 5. 询问是否手动运行维护
        print("\n" + "=" * 50)
        response = input("是否要手动运行一次维护任务？(y/N): ").strip().lower()
        if response in ['y', 'yes']:
            run_maintenance_manually(engine)
        
        print("\n✓ 检查完成")
        
    except Exception as e:
        print(f"❌ 检查过程中发生错误: {e}")
        raise
    finally:
        engine.dispose()

if __name__ == '__main__':
    main()
