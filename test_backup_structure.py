#!/usr/bin/env python3
# coding: utf-8
"""
测试新的备份目录结构功能
"""

import os
import sys
import tempfile
import shutil
from pathlib import Path
from datetime import datetime

# 添加src目录到Python路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from weather_download import WeatherDownloader
import logging

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


def test_backup_structure():
    """测试新的备份目录结构"""
    logger.info("开始测试新的备份目录结构...")
    
    # 创建临时目录用于测试
    with tempfile.TemporaryDirectory() as temp_dir:
        temp_path = Path(temp_dir)
        
        # 创建测试文件
        test_file = temp_path / "test_weather_data.nc"
        test_file.write_text("这是一个测试的天气数据文件")
        
        logger.info(f"创建测试文件: {test_file}")
        
        try:
            # 创建下载器实例
            downloader = WeatherDownloader("gz_mpfv3")
            
            # 临时修改备份目录到测试目录
            original_backup_dir = downloader.backup_dir
            test_backup_dir = temp_path / "backup"
            downloader.backup_dir = test_backup_dir
            
            logger.info(f"使用测试备份目录: {test_backup_dir}")
            
            # 测试备份功能
            success = downloader.move_to_backup(str(test_file))
            
            if success:
                logger.info("✓ 备份功能测试成功")
                
                # 检查目录结构
                now = datetime.now()
                expected_dir = test_backup_dir / now.strftime("%Y") / now.strftime("%m") / now.strftime("%d")
                
                if expected_dir.exists():
                    logger.info(f"✓ 按日期创建的目录结构正确: {expected_dir}")
                    
                    # 检查备份文件是否存在
                    backup_files = list(expected_dir.glob("*.nc"))
                    if backup_files:
                        logger.info(f"✓ 找到备份文件: {backup_files[0]}")
                        
                        # 测试清理功能
                        logger.info("测试清理旧备份功能...")
                        downloader.cleanup_old_backups(0)  # 立即清理所有文件
                        
                        # 检查文件是否被清理
                        remaining_files = list(expected_dir.glob("*.nc")) if expected_dir.exists() else []
                        if not remaining_files:
                            logger.info("✓ 清理功能测试成功")
                        else:
                            logger.warning(f"清理后仍有文件残留: {remaining_files}")
                    else:
                        logger.error("✗ 未找到备份文件")
                else:
                    logger.error(f"✗ 未找到预期的目录结构: {expected_dir}")
            else:
                logger.error("✗ 备份功能测试失败")
                
            # 恢复原始备份目录
            downloader.backup_dir = original_backup_dir
            
        except Exception as e:
            logger.error(f"测试过程中发生错误: {e}")
            return False
    
    logger.info("备份目录结构测试完成")
    return True


def test_multiple_backups():
    """测试多个文件备份和重名处理"""
    logger.info("开始测试多个文件备份...")
    
    with tempfile.TemporaryDirectory() as temp_dir:
        temp_path = Path(temp_dir)
        
        # 创建多个测试文件
        test_files = []
        for i in range(3):
            test_file = temp_path / f"test_weather_data_{i}.nc"
            test_file.write_text(f"这是第{i+1}个测试的天气数据文件")
            test_files.append(test_file)
        
        # 创建同名文件测试重名处理
        duplicate_file = temp_path / "test_weather_data_0.nc"
        duplicate_file.write_text("这是重名的测试文件")
        
        try:
            downloader = WeatherDownloader("gz_mpfv3")
            
            # 临时修改备份目录
            original_backup_dir = downloader.backup_dir
            test_backup_dir = temp_path / "backup"
            downloader.backup_dir = test_backup_dir
            
            # 备份所有文件
            success_count = 0
            for test_file in test_files:
                if downloader.move_to_backup(str(test_file)):
                    success_count += 1
            
            # 备份重名文件
            if downloader.move_to_backup(str(duplicate_file)):
                success_count += 1
            
            logger.info(f"成功备份 {success_count}/{len(test_files) + 1} 个文件")
            
            # 检查备份结果
            now = datetime.now()
            backup_date_dir = test_backup_dir / now.strftime("%Y") / now.strftime("%m") / now.strftime("%d")
            
            if backup_date_dir.exists():
                backup_files = list(backup_date_dir.glob("*.nc"))
                logger.info(f"备份目录中找到 {len(backup_files)} 个文件:")
                for bf in backup_files:
                    logger.info(f"  - {bf.name}")
                    
                if len(backup_files) == len(test_files) + 1:
                    logger.info("✓ 多文件备份测试成功")
                else:
                    logger.warning(f"备份文件数量不匹配，预期: {len(test_files) + 1}, 实际: {len(backup_files)}")
            
            # 恢复原始备份目录
            downloader.backup_dir = original_backup_dir
            
        except Exception as e:
            logger.error(f"多文件备份测试中发生错误: {e}")
            return False
    
    logger.info("多文件备份测试完成")
    return True


def show_backup_structure_example():
    """展示新的备份目录结构示例"""
    logger.info("新的备份目录结构示例:")
    logger.info("backup/")
    logger.info("├── 2025/")
    logger.info("│   ├── 01/")
    logger.info("│   │   ├── 15/")
    logger.info("│   │   │   ├── weather_data_001.nc")
    logger.info("│   │   │   └── weather_data_002.nc")
    logger.info("│   │   └── 16/")
    logger.info("│   │       └── weather_data_003.nc")
    logger.info("│   └── 02/")
    logger.info("│       └── 01/")
    logger.info("│           └── weather_data_004.nc")
    logger.info("└── 2024/")
    logger.info("    └── 12/")
    logger.info("        └── 31/")
    logger.info("            └── old_weather_data.nc")
    logger.info("")
    logger.info("优势:")
    logger.info("- 按日期自动分类，便于查找特定日期的备份")
    logger.info("- 避免单个目录文件过多的问题")
    logger.info("- 支持按日期批量清理旧备份")
    logger.info("- 保持与旧格式的兼容性")


if __name__ == "__main__":
    print("=" * 60)
    print("天气数据备份目录结构测试")
    print("=" * 60)
    
    # 显示新结构示例
    show_backup_structure_example()
    
    print("\n" + "=" * 60)
    print("开始功能测试")
    print("=" * 60)
    
    # 运行测试
    test1_success = test_backup_structure()
    test2_success = test_multiple_backups()
    
    print("\n" + "=" * 60)
    print("测试结果汇总")
    print("=" * 60)
    
    if test1_success and test2_success:
        print("✓ 所有测试通过！新的备份目录结构功能正常")
    else:
        print("✗ 部分测试失败，请检查错误信息")
        
    print("\n使用说明:")
    print("1. 现在备份文件会自动按 年/月/日 的目录结构保存")
    print("2. 清理功能会自动处理新的目录结构")
    print("3. 保持与旧格式备份文件的兼容性")
    print("4. 如果同一天有重名文件，会自动添加时间戳后缀")
