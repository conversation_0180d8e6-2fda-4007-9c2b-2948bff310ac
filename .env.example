# Weather Scheduler 环境变量配置文件
# 复制此文件为 .env 并根据实际环境修改配置

# ================================
# 数据库配置（可选，覆盖 config.yml 中的设置）
# ================================

# PostgreSQL 配置
# POSTGRES_HOST=***************
# POSTGRES_PORT=5432
# POSTGRES_DATABASE=middle-data-dev
# POSTGRES_USERNAME=root
# POSTGRES_PASSWORD=your_password_here

# MySQL 配置
# MYSQL_HOST=***************
# MYSQL_PORT=13306
# MYSQL_USERNAME=root
# MYSQL_PASSWORD=your_password_here
# MYSQL_DATABASE=ylzx-system-dev

# ================================
# Python 环境配置
# ================================

# Python 路径配置
PYTHONPATH=/data/script/weather_script/src

# Python 输出缓冲配置（确保日志实时输出）
PYTHONUNBUFFERED=1

# ================================
# 应用配置（可选）
# ================================

# 日志级别
# LOG_LEVEL=INFO

# 工作目录（systemd 会自动设置，这里仅作说明）
# WORKING_DIRECTORY=/data/script/weather_script

# ================================
# 系统配置
# ================================

# 时区设置
TZ=Asia/Shanghai

# 语言设置
LANG=zh_CN.UTF-8
LC_ALL=zh_CN.UTF-8
