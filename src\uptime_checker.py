#!/usr/bin/env python3
# coding: utf-8
"""
数据时效性检查模块
检查数据的uptime是否在指定时间内，支持重试机制
"""

import asyncio
import logging
import requests
import json
from datetime import datetime, timedelta
from typing import Optional, Dict, Any, Tuple
from urllib.parse import urlparse, parse_qs

from config import (
    WEATHER_DOWNLOAD_CONFIG,
    SCHEDULER_RETRY_INTERVAL_SECONDS,
    SCHEDULER_MAX_RETRY_DURATION_MINUTES,
    SCHEDULER_RETRY_UNTIL_FRESH
)

logger = logging.getLogger(__name__)


class UptimeChecker:
    """数据时效性检查器"""
    
    def __init__(self, data_type: str):
        self.data_type = data_type
        self.config = WEATHER_DOWNLOAD_CONFIG
        self.api_base_url = self.config["api_base_url"]
        self.api_key = self.config["api_key"]
        
        # 重试配置
        self.retry_interval_seconds = SCHEDULER_RETRY_INTERVAL_SECONDS  # 重试间隔（秒）
        self.max_retry_duration_minutes = SCHEDULER_MAX_RETRY_DURATION_MINUTES  # 最大重试时长（分钟）
    
    def _build_api_url(self) -> str:
        """构建API URL"""
        return f"{self.api_base_url}?key={self.api_key}&type={self.data_type}"
    
    async def _call_api(self) -> Optional[Dict[str, Any]]:
        """调用API获取数据信息"""
        try:
            api_url = self._build_api_url()
            logger.debug(f"[{self.data_type}] 调用API: {api_url}")
            
            # 使用异步HTTP请求
            loop = asyncio.get_event_loop()
            response = await loop.run_in_executor(
                None, 
                lambda: requests.get(api_url, timeout=30)
            )
            response.raise_for_status()
            
            api_data = response.json()
            logger.debug(f"[{self.data_type}] API响应: {api_data}")
            
            if api_data.get('status') != 'success':
                logger.error(f"[{self.data_type}] API返回错误: {api_data.get('errorMsg', '未知错误')}")
                return None
            
            return api_data.get('result', {})
            
        except requests.RequestException as e:
            logger.error(f"[{self.data_type}] 网络请求错误: {e}")
            return None
        except json.JSONDecodeError as e:
            logger.error(f"[{self.data_type}] API响应JSON解析错误: {e}")
            return None
        except Exception as e:
            logger.error(f"[{self.data_type}] API调用过程中发生错误: {e}")
            return None
    
    def _parse_file_time(self, file_time: str) -> Optional[datetime]:
        """解析文件时间字符串"""
        if not file_time:
            return None
        
        try:
            # 尝试多种时间格式
            time_formats = [
                "%Y%m%d%H%M%S",  # 20250713120000
                "%Y-%m-%d %H:%M:%S",  # 2025-07-13 12:00:00
                "%Y%m%d%H%M",  # 202507131200
                "%Y-%m-%dT%H:%M:%S",  # 2025-07-13T12:00:00
                "%Y-%m-%dT%H:%M:%SZ",  # 2025-07-13T12:00:00Z
            ]
            
            for fmt in time_formats:
                try:
                    return datetime.strptime(file_time, fmt)
                except ValueError:
                    continue
            
            logger.warning(f"[{self.data_type}] 无法解析时间格式: {file_time}")
            return None
            
        except Exception as e:
            logger.error(f"[{self.data_type}] 解析文件时间失败: {e}")
            return None
    
    async def get_api_data(self) -> Optional[Dict[str, Any]]:
        """
        获取API数据

        Returns:
            API结果数据，失败返回None
        """
        api_result = await self._call_api()
        if not api_result:
            return None

        file_time = api_result.get('uptime', '')
        if not file_time:
            logger.warning(f"[{self.data_type}] API响应中未找到文件时间")
            return None

        logger.info(f"[{self.data_type}] 获取到API数据，uptime: {file_time}")
        return api_result
    
    async def get_data_with_retry(self) -> Optional[Dict[str, Any]]:
        """
        获取API数据，支持重试机制

        Returns:
            API结果数据，如果超时则返回None
        """
        start_time = datetime.now()
        max_retry_duration = timedelta(minutes=self.max_retry_duration_minutes)
        retry_count = 0

        logger.info(f"[{self.data_type}] 开始获取API数据，最大重试时间: {self.max_retry_duration_minutes}分钟")

        while datetime.now() - start_time < max_retry_duration:
            retry_count += 1
            logger.info(f"[{self.data_type}] 第 {retry_count} 次尝试获取API数据...")

            api_result = await self.get_api_data()

            if api_result:
                logger.info(f"[{self.data_type}] 成功获取API数据，总等待时间: "
                           f"{(datetime.now() - start_time).total_seconds():.1f}秒")
                return api_result

            logger.warning(f"[{self.data_type}] API调用失败，{self.retry_interval_seconds}秒后重试...")

            # 等待重试间隔
            await asyncio.sleep(self.retry_interval_seconds)

        total_wait_time = (datetime.now() - start_time).total_seconds()
        logger.warning(f"[{self.data_type}] 获取API数据超时，总等待时间: {total_wait_time:.1f}秒")
        return None
    
    async def get_api_data_with_retry(self) -> Optional[Dict[str, Any]]:
        """
        获取API数据，支持重试
        这是主要的对外接口

        Returns:
            API结果数据，包含下载URL等信息
        """
        logger.info(f"[{self.data_type}] 开始获取API数据...")

        # 首先尝试一次
        api_result = await self.get_api_data()

        if api_result:
            logger.info(f"[{self.data_type}] 成功获取API数据")
            return api_result

        # 失败则开始重试
        logger.info(f"[{self.data_type}] 初始API调用失败，开始重试...")
        return await self.get_data_with_retry()


async def get_api_data(data_type: str) -> Optional[Dict[str, Any]]:
    """
    便捷函数：获取API数据

    Args:
        data_type: 数据类型，如 'gz_mpfv3', 'gz_didiforecast1hTEM' 等

    Returns:
        API结果数据，失败返回None
    """
    try:
        checker = UptimeChecker(data_type)
        return await checker.get_api_data_with_retry()
    except Exception as e:
        logger.error(f"获取API数据失败: {e}")
        return None


# 保持向后兼容的别名
async def check_and_get_fresh_data(data_type: str, task_type: str = None,
                                  last_processed_uptime: str = None) -> Optional[Dict[str, Any]]:
    """
    获取新鲜的API数据，如果uptime不是新的会重试

    Args:
        data_type: 数据类型，如 'gz_mpfv3', 'gz_didiforecast1hTEM' 等
        task_type: 任务类型 ("1h" 或 "6m")，用于检查uptime新鲜度
        last_processed_uptime: 上次处理的uptime，如果提供则会检查新鲜度

    Returns:
        API结果数据，失败返回None
    """
    # 如果没有启用重试直到新鲜功能，或者没有提供uptime检查参数，直接获取API数据
    if not SCHEDULER_RETRY_UNTIL_FRESH or not task_type or not last_processed_uptime:
        return await get_api_data(data_type)

    # 导入任务状态管理器（避免循环导入）
    from task_state_manager import should_process_task

    checker = UptimeChecker(data_type)
    start_time = datetime.now()
    max_retry_duration = timedelta(minutes=checker.max_retry_duration_minutes)
    retry_count = 0

    logger.info(f"[{data_type}] 开始获取新鲜数据，最大重试时间: {checker.max_retry_duration_minutes}分钟")

    while datetime.now() - start_time < max_retry_duration:
        retry_count += 1
        logger.info(f"[{data_type}] 第 {retry_count} 次尝试获取新鲜数据...")

        # 获取API数据
        api_result = await checker.get_api_data()

        if not api_result:
            logger.warning(f"[{data_type}] API调用失败，{checker.retry_interval_seconds}秒后重试...")
            await asyncio.sleep(checker.retry_interval_seconds)
            continue

        # 检查uptime是否为新的
        current_uptime = api_result.get('uptime', '')
        if not current_uptime:
            logger.warning(f"[{data_type}] API响应中未找到uptime，{checker.retry_interval_seconds}秒后重试...")
            await asyncio.sleep(checker.retry_interval_seconds)
            continue

        # 格式化uptime
        formatted_uptime = _format_uptime(current_uptime)
        if not formatted_uptime:
            logger.warning(f"[{data_type}] 无效的uptime格式: {current_uptime}，{checker.retry_interval_seconds}秒后重试...")
            await asyncio.sleep(checker.retry_interval_seconds)
            continue

        # 检查是否需要处理这个uptime
        # 注意：这里我们需要使用原始的data_type来检查，因为6m任务的data_type就是gz_mpfv3
        check_data_type = data_type.replace("gz_didiforecast1h", "") if "gz_didiforecast1h" in data_type else data_type
        if should_process_task(task_type, check_data_type, formatted_uptime):
            logger.info(f"[{data_type}] 获取到新鲜数据，uptime: {formatted_uptime}，总等待时间: "
                       f"{(datetime.now() - start_time).total_seconds():.1f}秒")
            return api_result
        else:
            logger.info(f"[{data_type}] uptime {formatted_uptime} 已处理，{checker.retry_interval_seconds}秒后重试...")
            await asyncio.sleep(checker.retry_interval_seconds)

    total_wait_time = (datetime.now() - start_time).total_seconds()
    logger.warning(f"[{data_type}] 获取新鲜数据超时，总等待时间: {total_wait_time:.1f}秒")
    return None


def _format_uptime(uptime: str) -> Optional[str]:
    """
    格式化uptime为YYYYMMDDHHMM格式

    Args:
        uptime: 原始uptime字符串

    Returns:
        格式化后的uptime字符串，失败返回None
    """
    if not uptime:
        return None

    try:
        # 移除所有非数字字符
        clean_uptime = ''.join(filter(str.isdigit, uptime))

        # 根据长度判断格式
        if len(clean_uptime) >= 12:
            # YYYYMMDDHHMM 或更长
            return clean_uptime[:12]
        elif len(clean_uptime) == 10:
            # YYYYMMDDHH，补充00分钟
            return clean_uptime + "00"
        elif len(clean_uptime) == 8:
            # YYYYMMDD，补充0000时分
            return clean_uptime + "0000"
        else:
            logger.warning(f"无法格式化uptime: {uptime}")
            return None

    except Exception as e:
        logger.error(f"格式化uptime失败: {e}")
        return None


# 保持向后兼容的别名
async def get_fresh_data_with_retry(data_type: str, task_type: str = None,
                                   last_processed_uptime: str = None) -> Optional[Dict[str, Any]]:
    """
    便捷函数：获取新鲜数据（向后兼容）

    Args:
        data_type: 数据类型，如 'gz_mpfv3', 'gz_didiforecast1hTEM' 等
        task_type: 任务类型 ("1h" 或 "6m")
        last_processed_uptime: 上次处理的uptime

    Returns:
        API结果数据，失败返回None
    """
    return await check_and_get_fresh_data(data_type, task_type, last_processed_uptime)
