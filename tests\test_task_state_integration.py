#!/usr/bin/env python3
# coding: utf-8
"""
测试任务状态管理集成
验证调度器与任务状态管理器的集成功能
"""

import asyncio
import sys
from pathlib import Path
import logging

# 添加src目录到Python路径
src_path = Path(__file__).parent / "src"
sys.path.insert(0, str(src_path))

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

logger = logging.getLogger(__name__)


async def test_task_state_manager():
    """测试任务状态管理器"""
    logger.info("=== 测试任务状态管理器 ===")
    
    try:
        from task_state_manager import (
            task_state_manager, should_process_task, 
            mark_task_completed, is_first_run, get_task_states_summary
        )
        
        # 测试初始状态
        logger.info("检查初始状态...")
        assert is_first_run("1h", "PRE"), "1h_PRE应该是首次运行"
        assert is_first_run("6m", "gz_mpfv3"), "6m_gz_mpfv3应该是首次运行"
        
        # 测试处理判断
        logger.info("测试处理判断...")
        assert should_process_task("1h", "PRE", "202507131304"), "首次运行应该处理"
        
        # 标记完成
        logger.info("标记任务完成...")
        mark_task_completed("1h", "PRE", "202507131304")
        
        # 验证状态更新
        assert not is_first_run("1h", "PRE"), "1h_PRE不应该再是首次运行"
        assert not should_process_task("1h", "PRE", "202507131304"), "相同uptime不应该重复处理"
        assert should_process_task("1h", "PRE", "202507131305"), "新uptime应该处理"
        
        # 获取状态摘要
        summary = get_task_states_summary()
        assert len(summary) == 5, "应该有5个任务状态"
        assert summary["1h_PRE"]["total_executions"] == 1, "1h_PRE应该执行了1次"
        
        logger.info("✓ 任务状态管理器测试通过")
        return True
        
    except Exception as e:
        logger.error(f"✗ 任务状态管理器测试失败: {e}")
        return False


async def test_uptime_formatting():
    """测试uptime格式化"""
    logger.info("=== 测试uptime格式化 ===")
    
    try:
        from scheduler import WeatherTaskScheduler
        
        scheduler = WeatherTaskScheduler()
        
        # 测试各种uptime格式
        test_cases = [
            ("202507131304", "202507131304"),  # 标准格式
            ("20250713130400", "202507131304"),  # 带秒
            ("2507131304", "202507131304"),  # 短格式
            ("2025-07-13 13:04", "202507131304"),  # 带分隔符
            ("invalid", None),  # 无效格式
        ]
        
        for input_uptime, expected in test_cases:
            result = scheduler._format_uptime(input_uptime)
            if result != expected:
                logger.error(f"格式化失败: {input_uptime} -> {result}, 期望: {expected}")
                return False
            logger.info(f"✓ {input_uptime} -> {result}")
        
        logger.info("✓ uptime格式化测试通过")
        return True
        
    except Exception as e:
        logger.error(f"✗ uptime格式化测试失败: {e}")
        return False


async def test_scheduler_integration():
    """测试调度器集成"""
    logger.info("=== 测试调度器集成 ===")
    
    try:
        from scheduler import WeatherTaskScheduler
        from task_state_manager import task_state_manager
        
        scheduler = WeatherTaskScheduler()
        
        # 测试状态获取
        status = scheduler.get_status()
        assert "task_states" in status, "状态中应该包含任务状态"
        assert "task_statistics" in status, "状态中应该包含任务统计"
        
        # 验证任务状态结构
        task_states = status["task_states"]
        assert len(task_states) == 5, "应该有5个任务状态"
        
        expected_tasks = ["1h_PRE", "1h_TEM", "1h_WEATHER", "1h_VIS", "6m_gz_mpfv3"]
        for task_key in expected_tasks:
            assert task_key in task_states, f"应该包含任务: {task_key}"
        
        # 验证统计信息
        stats = status["task_statistics"]
        assert stats["total_tasks"] == 5, "总任务数应该是5"
        assert stats["1h_tasks"] == 4, "1小时任务应该是4个"
        assert stats["6m_tasks"] == 1, "6分钟任务应该是1个"
        
        logger.info("✓ 调度器集成测试通过")
        return True
        
    except Exception as e:
        logger.error(f"✗ 调度器集成测试失败: {e}")
        return False


async def test_mock_task_execution():
    """测试模拟任务执行"""
    logger.info("=== 测试模拟任务执行 ===")
    
    try:
        from scheduler import WeatherTaskScheduler
        from task_state_manager import task_state_manager, reset_all_task_states
        
        # 重置所有任务状态
        reset_all_task_states()
        
        scheduler = WeatherTaskScheduler()
        
        # 模拟API响应
        mock_api_result = {
            "time": "202507131304",
            "url": "http://example.com/data.tar.gz",
            "md5": "abc123"
        }
        
        # 模拟1小时任务执行（不实际下载）
        logger.info("模拟1小时PRE任务执行...")
        
        # 检查初始状态
        assert task_state_manager.is_first_run("1h", "PRE"), "应该是首次运行"
        
        # 模拟执行过程
        uptime = scheduler._format_uptime(mock_api_result["time"])
        should_process = task_state_manager.should_process_uptime("1h", "PRE", uptime)
        assert should_process, "首次运行应该处理"
        
        # 标记完成
        task_state_manager.mark_task_completed("1h", "PRE", uptime)
        
        # 验证状态更新
        assert not task_state_manager.is_first_run("1h", "PRE"), "不应该再是首次运行"
        
        # 测试重复执行检查
        should_process_again = task_state_manager.should_process_uptime("1h", "PRE", uptime)
        assert not should_process_again, "相同uptime不应该重复处理"
        
        logger.info("✓ 模拟任务执行测试通过")
        return True
        
    except Exception as e:
        logger.error(f"✗ 模拟任务执行测试失败: {e}")
        return False


async def test_api_endpoints():
    """测试API端点（模拟）"""
    logger.info("=== 测试API端点功能 ===")
    
    try:
        from task_state_manager import (
            task_state_manager, get_task_states_summary, 
            reset_all_task_states
        )
        
        # 测试获取任务状态
        summary = get_task_states_summary()
        assert isinstance(summary, dict), "状态摘要应该是字典"
        assert len(summary) == 5, "应该有5个任务"
        
        # 测试统计信息
        stats = task_state_manager.get_statistics()
        assert "total_tasks" in stats, "统计信息应该包含总任务数"
        assert "first_run_tasks" in stats, "统计信息应该包含首次运行任务数"
        
        # 测试重置功能
        reset_all_task_states()
        stats_after_reset = task_state_manager.get_statistics()
        assert stats_after_reset["total_executions"] == 0, "重置后总执行次数应该为0"
        
        logger.info("✓ API端点功能测试通过")
        return True
        
    except Exception as e:
        logger.error(f"✗ API端点功能测试失败: {e}")
        return False


async def main():
    """主测试函数"""
    logger.info("🧪 开始任务状态管理集成测试...")
    logger.info("=" * 60)
    
    tests = [
        ("任务状态管理器", test_task_state_manager),
        ("uptime格式化", test_uptime_formatting),
        ("调度器集成", test_scheduler_integration),
        ("模拟任务执行", test_mock_task_execution),
        ("API端点功能", test_api_endpoints),
    ]
    
    results = []
    for test_name, test_func in tests:
        try:
            result = await test_func()
            results.append((test_name, result))
        except Exception as e:
            logger.error(f"测试 {test_name} 发生异常: {e}")
            results.append((test_name, False))
        
        logger.info("-" * 40)
    
    # 汇总结果
    logger.info("=" * 60)
    logger.info("📊 测试结果汇总:")
    
    all_passed = True
    for test_name, passed in results:
        status = "✅ 通过" if passed else "❌ 失败"
        logger.info(f"  {test_name}: {status}")
        if not passed:
            all_passed = False
    
    logger.info("=" * 60)
    if all_passed:
        logger.info("🎉 所有测试通过！任务状态管理系统集成成功。")
        logger.info("")
        logger.info("💡 下一步:")
        logger.info("   uv run python run_weather_scheduler.py")
        logger.info("   然后访问 http://localhost:8000/task-states 查看任务状态")
        sys.exit(0)
    else:
        logger.error("💥 部分测试失败！请检查相关模块。")
        sys.exit(1)


if __name__ == "__main__":
    asyncio.run(main())
