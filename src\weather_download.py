#!/usr/bin/env python3
# coding: utf-8
"""
天气数据下载模块
支持多种数据类型的下载、解压和文件管理
"""

import os
import shutil
import requests
from datetime import datetime, timedelta
from typing import Optional, Dict, Any, Tuple, List
from pathlib import Path
import logging
import tarfile
import json
import numpy as np
import xarray as xr
import time

from requests.adapters import HTTPAdapter
from urllib3.util.retry import Retry

# 导入配置
try:
    from .config import WEATHER_DOWNLOAD_CONFIG
except ImportError:
    from config import WEATHER_DOWNLOAD_CONFIG

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


class WeatherDownloader:
    """天气数据下载器"""
    
    def __init__(self, data_type: str = None):
        """
        初始化下载器
        
        Args:
            data_type: 数据类型，如 'gz_mpfv3', 'gz_didiforecast1hTEM' 等
        """
        self.config = WEATHER_DOWNLOAD_CONFIG
        self.data_type = data_type or self.config["default_data_type"]
        
        # 验证数据类型
        if self.data_type not in self.config["data_types"]:
            raise ValueError(f"不支持的数据类型: {self.data_type}")
        
        self.data_type_config = self.config["data_types"][self.data_type]
        
        # 设置路径 - 使用基于脚本目录的绝对路径
        from config import SCRIPT_DIR
        self.base_dir = SCRIPT_DIR / "data" / self.data_type_config["folder"]
        self.backup_dir = self.base_dir / "backup"

        # 注意：download_dir 和 nc_file_dir 现在会根据日期动态创建
        
        # 创建目录
        self._create_directories()
        
        # 文件匹配模式
        self.file_pattern = "*.nc"
        
    def _create_directories(self):
        """创建必要的目录"""
        directories = [
            self.base_dir,
            self.backup_dir
        ]

        for directory in directories:
            directory.mkdir(parents=True, exist_ok=True)
            logger.debug(f"确保目录存在: {directory}")

    def _get_date_based_dirs(self, date_time: datetime = None) -> tuple:
        """
        获取基于日期的下载和NC文件目录

        Args:
            date_time: 指定日期时间，如果为None则使用当前时间

        Returns:
            tuple: (download_dir, nc_file_dir)
        """
        if date_time is None:
            date_time = datetime.now()

        year = date_time.strftime("%Y")
        month = date_time.strftime("%m")
        day = date_time.strftime("%d")

        # 创建按日期划分的目录结构
        date_dir = self.base_dir / year / month / day
        download_dir = date_dir / "downloads"
        nc_file_dir = date_dir / "nc_files"

        # 确保目录存在
        download_dir.mkdir(parents=True, exist_ok=True)
        nc_file_dir.mkdir(parents=True, exist_ok=True)

        return download_dir, nc_file_dir

    def _create_session_with_retry(self) -> requests.Session:
        """创建带有重试机制的requests session"""
        session = requests.Session()

        # 配置重试策略
        retry_strategy = Retry(
            total=self.config.get("max_retries", 3),
            backoff_factor=1,  # 重试间隔递增因子
            status_forcelist=[429, 500, 502, 503, 504],  # 需要重试的HTTP状态码
            allowed_methods=["HEAD", "GET", "OPTIONS"]  # 允许重试的HTTP方法
        )

        # 创建适配器
        adapter = HTTPAdapter(max_retries=retry_strategy)

        # 挂载适配器
        session.mount("http://", adapter)
        session.mount("https://", adapter)

        return session

    def _download_file(self, url: str, file_path: Path) -> bool:
        """
        直接下载文件，如果失败则重新下载

        Args:
            url: 下载URL
            file_path: 本地文件路径

        Returns:
            下载成功返回True，失败返回False
        """
        max_retries = self.config.get("max_retries", 3)
        chunk_size = self.config.get("chunk_size", 8192)
        retry_intervals = self.config.get("retry_intervals", [30, 60, 120, 240, 300])

        for attempt in range(max_retries):
            try:
                # 如果文件已存在，删除重新下载
                if file_path.exists():
                    file_path.unlink()
                    logger.info(f"[{self.data_type}] 删除已存在的文件，重新下载")

                # 创建带重试的session
                session = self._create_session_with_retry()

                # 发起下载请求
                response = session.get(
                    url,
                    timeout=self.config["download_timeout"],
                    stream=True
                )

                # 检查响应状态
                response.raise_for_status()

                # 获取文件总大小
                content_length = response.headers.get('content-length')
                total_size = int(content_length) if content_length else None

                logger.info(f"[{self.data_type}] 开始下载，总大小: {total_size} bytes")
                logger.info(f"[{self.data_type}] 响应状态码: {response.status_code}")

                # 写入文件
                downloaded_size = 0
                with open(file_path, 'wb') as f:
                    for chunk in response.iter_content(chunk_size=chunk_size):
                        if chunk:
                            f.write(chunk)
                            downloaded_size += len(chunk)

                            # 每下载10MB输出一次进度
                            if downloaded_size % (10 * 1024 * 1024) == 0:
                                if total_size and total_size > 0:
                                    progress = (downloaded_size / total_size) * 100
                                    logger.info(f"[{self.data_type}] 下载进度: {progress:.1f}% ({downloaded_size}/{total_size})")
                                else:
                                    logger.info(f"[{self.data_type}] 已下载: {downloaded_size} bytes")

                logger.info(f"[{self.data_type}] 下载完成，文件大小: {downloaded_size} bytes")
                return True

            except requests.exceptions.RequestException as e:
                logger.warning(f"[{self.data_type}] 下载尝试 {attempt + 1}/{max_retries} 失败: {e}")
                if file_path.exists():
                    file_path.unlink()  # 删除不完整的文件
                if attempt < max_retries - 1:
                    # 使用配置的重试间隔，如果超出配置长度则使用最后一个值
                    wait_time = retry_intervals[min(attempt, len(retry_intervals) - 1)]
                    logger.info(f"[{self.data_type}] 等待 {wait_time} 秒后重试...")
                    time.sleep(wait_time)
                else:
                    logger.error(f"[{self.data_type}] 所有下载尝试都失败")

            except Exception as e:
                logger.error(f"[{self.data_type}] 下载过程中发生未知错误: {e}")
                if file_path.exists():
                    file_path.unlink()  # 删除不完整的文件
                if attempt < max_retries - 1:
                    # 使用配置的重试间隔，如果超出配置长度则使用最后一个值
                    wait_time = retry_intervals[min(attempt, len(retry_intervals) - 1)]
                    logger.info(f"[{self.data_type}] 等待 {wait_time} 秒后重试...")
                    time.sleep(wait_time)
                else:
                    break

        return False



    def _build_api_url(self) -> str:
        """构建API请求URL"""
        base_url = self.config["api_base_url"]
        api_key = self.config["api_key"]
        return f"{base_url}?type={self.data_type}&key={api_key}"

    def _call_api_with_retry(self) -> Optional[Dict[str, Any]]:
        """
        调用API获取下载信息，支持重试机制

        Returns:
            API结果数据，失败返回None
        """
        max_retries = self.config.get("max_retries", 3)
        retry_intervals = self.config.get("retry_intervals", [30, 60, 120, 240, 300])

        for attempt in range(max_retries):
            try:
                # 构建API URL
                api_url = self._build_api_url()
                logger.info(f"[{self.data_type}] 正在调用API (尝试 {attempt + 1}/{max_retries}): {api_url}")

                # 创建带重试的session
                session = self._create_session_with_retry()

                # 调用API获取下载信息
                api_timeout = self.config.get("api_timeout", 30)
                response = session.get(api_url, timeout=api_timeout)
                response.raise_for_status()

                api_data = response.json()
                logger.info(f"[{self.data_type}] API响应: {api_data}")

                # 检查API响应
                if api_data.get('status') != 'success':
                    error_msg = api_data.get('errorMsg', '未知错误')
                    logger.error(f"[{self.data_type}] API返回错误: {error_msg}")

                    # 某些错误不需要重试
                    if 'key' in error_msg.lower() or 'auth' in error_msg.lower():
                        logger.error(f"[{self.data_type}] 认证错误，不进行重试")
                        return None

                    if attempt < max_retries - 1:
                        # 使用配置的重试间隔，如果超出配置长度则使用最后一个值
                        wait_time = retry_intervals[min(attempt, len(retry_intervals) - 1)]
                        logger.info(f"[{self.data_type}] API错误，等待 {wait_time} 秒后重试...")
                        time.sleep(wait_time)
                        continue
                    else:
                        return None

                return api_data.get('result', {})

            except requests.exceptions.RequestException as e:
                logger.warning(f"[{self.data_type}] API调用尝试 {attempt + 1}/{max_retries} 失败: {e}")
                if attempt < max_retries - 1:
                    # 使用配置的重试间隔，如果超出配置长度则使用最后一个值
                    wait_time = retry_intervals[min(attempt, len(retry_intervals) - 1)]
                    logger.info(f"[{self.data_type}] 等待 {wait_time} 秒后重试...")
                    time.sleep(wait_time)
                else:
                    logger.error(f"[{self.data_type}] 所有API调用尝试都失败")

            except json.JSONDecodeError as e:
                logger.warning(f"[{self.data_type}] API响应JSON解析失败 (尝试 {attempt + 1}/{max_retries}): {e}")
                if attempt < max_retries - 1:
                    # 使用配置的重试间隔，如果超出配置长度则使用最后一个值
                    wait_time = retry_intervals[min(attempt, len(retry_intervals) - 1)]
                    logger.info(f"[{self.data_type}] 等待 {wait_time} 秒后重试...")
                    time.sleep(wait_time)
                else:
                    logger.error(f"[{self.data_type}] 所有API调用尝试都失败")

            except Exception as e:
                logger.error(f"[{self.data_type}] API调用过程中发生未知错误: {e}")
                if attempt < max_retries - 1:
                    # 使用配置的重试间隔，如果超出配置长度则使用最后一个值
                    wait_time = retry_intervals[min(attempt, len(retry_intervals) - 1)]
                    logger.info(f"[{self.data_type}] 等待 {wait_time} 秒后重试...")
                    time.sleep(wait_time)
                else:
                    break

        return None
    
    def backup_existing_files(self, target_date: datetime = None) -> bool:
        """
        备份指定日期NC文件目录中的现有文件

        Args:
            target_date: 目标日期，如果为None则备份当天的文件

        Returns:
            备份成功返回True，失败返回False
        """
        try:
            # 获取指定日期的NC文件目录
            _, nc_file_dir = self._get_date_based_dirs(target_date)

            existing_files = list(nc_file_dir.glob(self.file_pattern))

            if not existing_files:
                date_str = (target_date or datetime.now()).strftime("%Y-%m-%d")
                logger.info(f"[{self.data_type}] {date_str} NC文件目录中没有现有文件需要备份")
                return True

            date_str = (target_date or datetime.now()).strftime("%Y-%m-%d")
            logger.info(f"[{self.data_type}] 发现 {date_str} 的 {len(existing_files)} 个现有文件，开始备份...")

            moved_count = 0
            for file_path in existing_files:
                if self.move_to_backup(str(file_path)):
                    moved_count += 1
                else:
                    logger.error(f"[{self.data_type}] 备份文件失败: {file_path}")

            logger.info(f"[{self.data_type}] 成功备份 {moved_count}/{len(existing_files)} 个文件")
            return moved_count == len(existing_files)

        except Exception as e:
            logger.error(f"[{self.data_type}] 备份现有文件时发生错误: {e}")
            return False

    def backup_all_existing_files(self) -> bool:
        """
        备份所有日期目录中的现有文件

        Returns:
            备份成功返回True，失败返回False
        """
        try:
            total_moved = 0
            total_files = 0

            # 遍历所有年份目录
            for year_dir in self.base_dir.glob("*"):
                if not year_dir.is_dir() or not year_dir.name.isdigit():
                    continue

                # 遍历月份目录
                for month_dir in year_dir.glob("*"):
                    if not month_dir.is_dir() or not month_dir.name.isdigit():
                        continue

                    # 遍历日期目录
                    for day_dir in month_dir.glob("*"):
                        if not day_dir.is_dir() or not day_dir.name.isdigit():
                            continue

                        # 检查NC文件目录
                        nc_file_dir = day_dir / "nc_files"
                        if nc_file_dir.exists():
                            existing_files = list(nc_file_dir.glob(self.file_pattern))
                            total_files += len(existing_files)

                            for file_path in existing_files:
                                if self.move_to_backup(str(file_path)):
                                    total_moved += 1

            logger.info(f"[{self.data_type}] 总计备份 {total_moved}/{total_files} 个文件")
            return total_moved == total_files

        except Exception as e:
            logger.error(f"[{self.data_type}] 备份所有现有文件时发生错误: {e}")
            return False
    
    def download_from_api(self, api_result: Optional[Dict] = None) -> Optional[List[str]]:
        """
        从API下载天气数据

        Args:
            api_result: 可选的API结果，如果提供则直接使用，否则调用API获取

        Returns:
            下载并解压后的NC文件路径列表，失败返回None
        """
        try:
            # 备份现有文件
            logger.info(f"[{self.data_type}] 下载前备份现有文件...")
            if not self.backup_existing_files():
                logger.warning(f"[{self.data_type}] 备份现有文件时出现问题，但继续下载...")

            # 获取API结果
            if api_result is None:
                result = self._call_api_with_retry()
                if not result:
                    return None
            else:
                result = api_result
                logger.info(f"[{self.data_type}] 使用提供的API结果")

            download_url = result.get('url')
            file_time = result.get('uptime', '')

            if not download_url:
                logger.error(f"[{self.data_type}] API响应中未找到下载URL")
                return None

            # 将HTTP链接修改为HTTPS
            if download_url.startswith('http://'):
                download_url = download_url.replace('http://', 'https://', 1)
                logger.info(f"[{self.data_type}] 已将下载链接从HTTP修改为HTTPS")

            logger.info(f"[{self.data_type}] 获取到下载URL: {download_url}")
            logger.info(f"[{self.data_type}] 文件时间: {file_time}")

            # 下载文件
            return self._download_and_extract(download_url, file_time)

        except requests.RequestException as e:
            logger.error(f"[{self.data_type}] 网络请求错误: {e}")
            return None
        except json.JSONDecodeError as e:
            logger.error(f"[{self.data_type}] API响应JSON解析错误: {e}")
            return None
        except Exception as e:
            logger.error(f"[{self.data_type}] 下载过程中发生错误: {e}")
            return None
    
    def _download_and_extract(self, download_url: str, file_time: str) -> Optional[List[str]]:
        """
        下载并解压文件

        Args:
            download_url: 下载URL
            file_time: 文件时间戳

        Returns:
            解压后的NC文件路径列表，失败返回None
        """
        try:
            # 获取基于当前日期的目录
            download_dir, nc_file_dir = self._get_date_based_dirs()

            # 下载tar.gz文件
            filename = f"{self.data_type}_{file_time}.tar.gz" if file_time else f"{self.data_type}.tar.gz"
            download_path = download_dir / filename

            logger.info(f"[{self.data_type}] 开始下载文件到: {download_path}")

            # 直接下载
            if self._download_file(download_url, download_path):
                logger.info(f"[{self.data_type}] 文件下载完成: {download_path}")

                # 解压文件
                return self._extract_tar_gz(download_path, nc_file_dir)
            else:
                logger.error(f"[{self.data_type}] 文件下载失败")
                return None

        except Exception as e:
            logger.error(f"[{self.data_type}] 下载和解压过程中发生错误: {e}")
            return None
    
    def _extract_tar_gz(self, tar_path: Path, target_dir: Path) -> Optional[List[str]]:
        """
        解压tar.gz文件

        Args:
            tar_path: tar.gz文件路径
            target_dir: 目标解压目录

        Returns:
            所有解压的NC文件路径列表，失败返回None
        """
        try:
            logger.info(f"[{self.data_type}] 开始解压文件到: {target_dir}")

            extracted_files = []

            with tarfile.open(tar_path, 'r:gz') as tar:
                members = tar.getmembers()
                nc_files = [m for m in members if m.name.endswith('.nc')]

                if not nc_files:
                    logger.error(f"[{self.data_type}] 压缩包中未找到NC文件")
                    return None

                # 解压所有NC文件
                for member in nc_files:
                    # 只保留文件名，不保留目录结构
                    member.name = os.path.basename(member.name)
                    tar.extract(member, target_dir)

                    # 记录解压的文件路径
                    extracted_file_path = str(target_dir / member.name)
                    extracted_files.append(extracted_file_path)
                    logger.info(f"[{self.data_type}] 解压文件: {member.name}")

            logger.info(f"[{self.data_type}] 文件解压完成")

            if extracted_files:
                # 按文件名排序，确保时间段顺序正确
                extracted_files.sort()
                logger.info(f"[{self.data_type}] 成功解压 {len(extracted_files)} 个NC文件")
                for file_path in extracted_files:
                    logger.info(f"[{self.data_type}] 解压文件: {os.path.basename(file_path)}")
                return extracted_files
            else:
                logger.error(f"[{self.data_type}] 解压后未找到有效的NC文件")
                return None

        except tarfile.TarError as e:
            logger.error(f"[{self.data_type}] 解压文件错误: {e}")
            return None
        except Exception as e:
            logger.error(f"[{self.data_type}] 解压过程中发生错误: {e}")
            return None

    def get_latest_nc_file(self, search_days: int = 7) -> Optional[str]:
        """
        获取最新的NC文件（在指定天数内搜索）

        Args:
            search_days: 搜索的天数范围，默认7天

        Returns:
            最新文件路径，未找到返回None
        """
        try:
            all_nc_files = []
            current_date = datetime.now()

            # 在指定天数内搜索NC文件
            for i in range(search_days):
                search_date = current_date - timedelta(days=i)
                _, nc_file_dir = self._get_date_based_dirs(search_date)

                if nc_file_dir.exists():
                    nc_files = list(nc_file_dir.glob(self.file_pattern))
                    all_nc_files.extend(nc_files)

            if not all_nc_files:
                logger.warning(f"[{self.data_type}] 在过去{search_days}天内未找到NC文件")
                return None

            # 按文件修改时间排序，取最新的
            latest_file = max(all_nc_files, key=lambda f: f.stat().st_mtime)
            logger.info(f"[{self.data_type}] 找到最新的NC文件: {latest_file}")
            return str(latest_file)

        except Exception as e:
            logger.error(f"[{self.data_type}] 获取最新NC文件时发生错误: {e}")
            return None

    def get_nc_files_by_date(self, target_date: datetime = None) -> List[str]:
        """
        获取指定日期的所有NC文件

        Args:
            target_date: 目标日期，如果为None则使用当前日期

        Returns:
            NC文件路径列表
        """
        try:
            _, nc_file_dir = self._get_date_based_dirs(target_date)

            if not nc_file_dir.exists():
                date_str = (target_date or datetime.now()).strftime("%Y-%m-%d")
                logger.info(f"[{self.data_type}] {date_str} 的NC文件目录不存在")
                return []

            nc_files = list(nc_file_dir.glob(self.file_pattern))
            nc_files.sort(key=lambda f: f.name)  # 按文件名排序

            date_str = (target_date or datetime.now()).strftime("%Y-%m-%d")
            logger.info(f"[{self.data_type}] 找到 {date_str} 的 {len(nc_files)} 个NC文件")

            return [str(f) for f in nc_files]

        except Exception as e:
            logger.error(f"[{self.data_type}] 获取指定日期NC文件时发生错误: {e}")
            return []

    def move_to_backup(self, file_path: str) -> bool:
        """
        将文件移动到备份目录，按年/月/日划分文件夹

        Args:
            file_path: 要移动的文件路径

        Returns:
            移动成功返回True，失败返回False
        """
        try:
            source_path = Path(file_path)
            if not source_path.exists():
                logger.error(f"[{self.data_type}] 源文件不存在: {file_path}")
                return False

            # 获取当前日期，创建年/月/日目录结构
            now = datetime.now()
            year = now.strftime("%Y")
            month = now.strftime("%m")
            day = now.strftime("%d")

            # 创建按日期划分的备份目录结构
            date_backup_dir = self.backup_dir / year / month / day
            date_backup_dir.mkdir(parents=True, exist_ok=True)

            # 目标路径
            backup_path = date_backup_dir / source_path.name

            # 如果备份文件已存在，添加时间戳
            if backup_path.exists():
                timestamp = now.strftime("%H%M%S")
                name_parts = source_path.stem, timestamp, source_path.suffix
                backup_path = date_backup_dir / f"{name_parts[0]}_{name_parts[1]}{name_parts[2]}"

            # 移动文件
            shutil.move(str(source_path), str(backup_path))
            logger.info(f"[{self.data_type}] 文件已移动到备份目录: {source_path} -> {backup_path}")
            return True

        except Exception as e:
            logger.error(f"[{self.data_type}] 移动文件到备份目录时发生错误: {e}")
            return False

    def cleanup_old_backups(self, keep_days: int = None):
        """
        清理旧的备份文件，支持年/月/日目录结构

        Args:
            keep_days: 保留天数，默认使用配置中的值
        """
        try:
            keep_days = keep_days or self.config["backup_keep_days"]
            current_time = datetime.now()
            deleted_count = 0
            deleted_dirs = 0

            # 遍历年份目录
            for year_dir in self.backup_dir.glob("*"):
                if not year_dir.is_dir() or not year_dir.name.isdigit():
                    continue

                # 遍历月份目录
                for month_dir in year_dir.glob("*"):
                    if not month_dir.is_dir() or not month_dir.name.isdigit():
                        continue

                    # 遍历日期目录
                    for day_dir in month_dir.glob("*"):
                        if not day_dir.is_dir() or not day_dir.name.isdigit():
                            continue

                        # 检查日期目录的年龄
                        try:
                            dir_date = datetime.strptime(f"{year_dir.name}-{month_dir.name}-{day_dir.name}", "%Y-%m-%d")
                            age_days = (current_time.date() - dir_date.date()).days

                            if age_days > keep_days:
                                # 删除整个日期目录及其内容
                                import shutil
                                shutil.rmtree(day_dir)
                                logger.info(f"[{self.data_type}] 删除旧备份目录: {day_dir} (年龄: {age_days} 天)")
                                deleted_dirs += 1
                            else:
                                # 如果日期目录不需要删除，检查其中的文件
                                backup_files = list(day_dir.glob(self.file_pattern))
                                for backup_file in backup_files:
                                    file_time = datetime.fromtimestamp(backup_file.stat().st_mtime)
                                    file_age_days = (current_time - file_time).days

                                    if file_age_days > keep_days:
                                        backup_file.unlink()
                                        logger.info(f"[{self.data_type}] 删除旧备份文件: {backup_file} (年龄: {file_age_days} 天)")
                                        deleted_count += 1

                        except ValueError as e:
                            logger.warning(f"[{self.data_type}] 无法解析日期目录名: {day_dir.name}, 错误: {e}")
                            continue

                    # 清理空的月份目录
                    if month_dir.exists() and not any(month_dir.iterdir()):
                        month_dir.rmdir()
                        logger.info(f"[{self.data_type}] 删除空的月份目录: {month_dir}")

                # 清理空的年份目录
                if year_dir.exists() and not any(year_dir.iterdir()):
                    year_dir.rmdir()
                    logger.info(f"[{self.data_type}] 删除空的年份目录: {year_dir}")

            # 同时处理旧格式的备份文件（直接在backup_dir下的文件）
            old_backup_files = [f for f in self.backup_dir.glob(self.file_pattern) if f.is_file()]
            for backup_file in old_backup_files:
                file_time = datetime.fromtimestamp(backup_file.stat().st_mtime)
                age_days = (current_time - file_time).days

                if age_days > keep_days:
                    backup_file.unlink()
                    logger.info(f"[{self.data_type}] 删除旧格式备份文件: {backup_file} (年龄: {age_days} 天)")
                    deleted_count += 1

            logger.info(f"[{self.data_type}] 清理完成，删除了 {deleted_count} 个旧备份文件和 {deleted_dirs} 个旧备份目录")

        except Exception as e:
            logger.error(f"[{self.data_type}] 清理旧备份文件时发生错误: {e}")

    def cleanup_old_downloads(self, keep_days: int = None):
        """
        清理旧的下载文件，支持年/月/日目录结构

        Args:
            keep_days: 保留天数，默认使用配置中的值
        """
        try:
            keep_days = keep_days or self.config.get("download_keep_days", 7)  # 下载文件默认保留7天
            current_time = datetime.now()
            deleted_count = 0
            deleted_dirs = 0

            logger.info(f"[{self.data_type}] 开始清理 {keep_days} 天前的下载文件...")

            # 遍历年份目录
            for year_dir in self.base_dir.glob("*"):
                if not year_dir.is_dir() or not year_dir.name.isdigit():
                    continue

                # 遍历月份目录
                for month_dir in year_dir.glob("*"):
                    if not month_dir.is_dir() or not month_dir.name.isdigit():
                        continue

                    # 遍历日期目录
                    for day_dir in month_dir.glob("*"):
                        if not day_dir.is_dir() or not day_dir.name.isdigit():
                            continue

                        # 检查日期目录的年龄
                        try:
                            dir_date = datetime.strptime(f"{year_dir.name}-{month_dir.name}-{day_dir.name}", "%Y-%m-%d")
                            age_days = (current_time.date() - dir_date.date()).days

                            if age_days > keep_days:
                                # 删除下载目录
                                download_dir = day_dir / "downloads"
                                if download_dir.exists():
                                    import shutil
                                    shutil.rmtree(download_dir)
                                    logger.info(f"[{self.data_type}] 删除旧下载目录: {download_dir} (年龄: {age_days} 天)")
                                    deleted_dirs += 1
                            else:
                                # 如果日期目录不需要删除，检查其中的下载文件
                                download_dir = day_dir / "downloads"
                                if download_dir.exists():
                                    download_files = list(download_dir.glob("*.tar.gz"))
                                    for download_file in download_files:
                                        file_time = datetime.fromtimestamp(download_file.stat().st_mtime)
                                        file_age_days = (current_time - file_time).days

                                        if file_age_days > keep_days:
                                            download_file.unlink()
                                            logger.info(f"[{self.data_type}] 删除旧下载文件: {download_file} (年龄: {file_age_days} 天)")
                                            deleted_count += 1

                        except ValueError as e:
                            logger.warning(f"[{self.data_type}] 无法解析日期目录名: {day_dir.name}, 错误: {e}")
                            continue

                    # 清理空的月份目录
                    if month_dir.exists() and not any(month_dir.iterdir()):
                        month_dir.rmdir()
                        logger.info(f"[{self.data_type}] 删除空的月份目录: {month_dir}")

                # 清理空的年份目录
                if year_dir.exists() and not any(year_dir.iterdir()):
                    year_dir.rmdir()
                    logger.info(f"[{self.data_type}] 删除空的年份目录: {year_dir}")

            logger.info(f"[{self.data_type}] 下载文件清理完成，删除了 {deleted_count} 个旧下载文件和 {deleted_dirs} 个旧下载目录")

        except Exception as e:
            logger.error(f"[{self.data_type}] 清理旧下载文件时发生错误: {e}")


# ================================
# 便捷函数
# ================================

def download_weather_data(data_type: str = None) -> Optional[str]:
    """
    便捷函数：下载指定类型的天气数据（兼容性函数，返回第一个文件）

    Args:
        data_type: 数据类型，如 'gz_mpfv3', 'gz_didiforecast1hTEM' 等

    Returns:
        第一个下载文件路径，失败返回None
    """
    try:
        downloader = WeatherDownloader(data_type)
        files = downloader.download_from_api()
        return files[0] if files else None
    except Exception as e:
        logger.error(f"下载天气数据失败: {e}")
        return None


def download_weather_data_all(data_type: str = None) -> Optional[List[str]]:
    """
    便捷函数：下载指定类型的天气数据（返回所有文件）

    Args:
        data_type: 数据类型，如 'gz_mpfv3', 'gz_didiforecast1hTEM' 等

    Returns:
        所有下载文件路径列表，失败返回None
    """
    try:
        downloader = WeatherDownloader(data_type)
        return downloader.download_from_api()
    except Exception as e:
        logger.error(f"下载天气数据失败: {e}")
        return None


def get_latest_weather_file(data_type: str = None) -> Optional[str]:
    """
    便捷函数：获取指定类型的最新天气文件

    Args:
        data_type: 数据类型

    Returns:
        最新文件路径，未找到返回None
    """
    try:
        downloader = WeatherDownloader(data_type)
        return downloader.get_latest_nc_file()
    except Exception as e:
        logger.error(f"获取最新天气文件失败: {e}")
        return None


def backup_weather_file(file_path: str, data_type: str = None) -> bool:
    """
    便捷函数：备份天气文件

    Args:
        file_path: 要备份的文件路径
        data_type: 数据类型

    Returns:
        备份成功返回True，失败返回False
    """
    try:
        downloader = WeatherDownloader(data_type)
        return downloader.move_to_backup(file_path)
    except Exception as e:
        logger.error(f"备份天气文件失败: {e}")
        return False


def download_and_get_latest_weather_data(data_type: str = None) -> Optional[str]:
    """
    便捷函数：下载天气数据并返回最新文件路径（兼容旧接口）

    Args:
        data_type: 数据类型或完整的下载URL（兼容旧版本）

    Returns:
        最新文件路径，失败返回None
    """
    # 兼容旧版本：如果传入的是URL，提取数据类型
    if data_type and data_type.startswith("http"):
        # 将HTTP链接修改为HTTPS
        if data_type.startswith("http://"):
            data_type = data_type.replace("http://", "https://", 1)
            logger.info(f"已将下载链接从HTTP修改为HTTPS: {data_type}")

        if "type=" in data_type:
            # 从URL中提取type参数
            import urllib.parse as urlparse
            parsed = urlparse.urlparse(data_type)
            params = urlparse.parse_qs(parsed.query)
            data_type = params.get('type', [None])[0]
        else:
            data_type = None

    return download_weather_data(data_type)


def download_weather_data_with_api_result(data_type: str, api_result: Dict[str, Any]) -> Optional[List[str]]:
    """
    使用提供的API结果下载天气数据

    Args:
        data_type: 数据类型
        api_result: API结果，包含下载URL等信息

    Returns:
        下载的文件路径列表，失败返回None
    """
    try:
        downloader = WeatherDownloader(data_type)
        return downloader.download_from_api(api_result)
    except Exception as e:
        logger.error(f"使用API结果下载天气数据失败: {e}")
        return None


def process_and_backup_weather_file(file_path: str, was_downloaded: bool = True, data_type: str = None) -> bool:
    """
    便捷函数：处理完成后备份天气文件（兼容旧接口）

    Args:
        file_path: 要备份的文件路径
        was_downloaded: 是否是下载的文件
        data_type: 数据类型

    Returns:
        备份成功返回True，失败返回False
    """
    if not was_downloaded:
        logger.info(f"文件不是下载的，跳过备份: {file_path}")
        return True

    return backup_weather_file(file_path, data_type)


if __name__ == "__main__":
    # 测试代码
    print("天气下载模块测试")

    # 测试不同数据类型
    data_types = ["gz_mpfv3", "gz_didiforecast1hTEM", "gz_didiforecast1hPRE"]

    for dt in data_types:
        print(f"\n=== 测试数据类型: {dt} ===")
        try:
            downloader = WeatherDownloader(dt)
            print(f"数据类型: {downloader.data_type_config['name']}")
            print(f"存储目录: {downloader.base_dir}")

            # 测试获取最新文件
            latest_file = downloader.get_latest_nc_file()
            if latest_file:
                print(f"最新文件: {latest_file}")
            else:
                print("未找到NC文件")

        except Exception as e:
            print(f"测试失败: {e}")


# ========== NetCDF文件处理公共函数 ==========

def read_netcdf_data(file_path: str, var_name: str = None) -> Tuple[xr.Dataset, Dict[str, Any]]:
    """
    读取NetCDF文件数据

    Args:
        file_path: NetCDF文件路径
        var_name: 变量名，如果为None则返回所有变量

    Returns:
        tuple: (xarray.Dataset, metadata_dict)
    """
    try:
        # 打开NetCDF文件
        ds = xr.open_dataset(file_path)

        # 提取元数据
        metadata = {
            'file_path': file_path,
            'dimensions': dict(ds.dims),
            'variables': list(ds.data_vars.keys()),
            'coordinates': list(ds.coords.keys()),
            'attrs': dict(ds.attrs)
        }

        # 如果指定了变量名，只返回该变量
        if var_name and var_name in ds.data_vars:
            ds = ds[[var_name]]
            metadata['selected_variable'] = var_name

        logger.info(f"成功读取NetCDF文件: {file_path}")
        logger.info(f"数据维度: {metadata['dimensions']}")
        logger.info(f"变量列表: {metadata['variables']}")

        return ds, metadata

    except Exception as e:
        logger.error(f"读取NetCDF文件失败 {file_path}: {e}")
        raise


def extract_netcdf_coordinates(ds: xr.Dataset) -> Tuple[np.ndarray, np.ndarray, float, float]:
    """
    从NetCDF数据集中提取坐标信息

    Args:
        ds: xarray.Dataset对象

    Returns:
        tuple: (lon_array, lat_array, lon_min, lat_min)
    """
    try:
        # 尝试不同的坐标变量名
        lon_names = ['longitude', 'lon', 'x', 'X']
        lat_names = ['latitude', 'lat', 'y', 'Y']

        lon_var = None
        lat_var = None

        # 查找经度变量
        for name in lon_names:
            if name in ds.coords:
                lon_var = ds.coords[name]
                break

        # 查找纬度变量
        for name in lat_names:
            if name in ds.coords:
                lat_var = ds.coords[name]
                break

        if lon_var is None or lat_var is None:
            raise ValueError("无法找到经纬度坐标变量")

        # 转换为numpy数组
        lon_array = lon_var.values
        lat_array = lat_var.values

        # 计算最小值
        lon_min = float(np.min(lon_array))
        lat_min = float(np.min(lat_array))

        logger.info(f"坐标范围 - 经度: [{lon_min:.4f}, {np.max(lon_array):.4f}], "
                   f"纬度: [{lat_min:.4f}, {np.max(lat_array):.4f}]")

        return lon_array, lat_array, lon_min, lat_min

    except Exception as e:
        logger.error(f"提取坐标信息失败: {e}")
        raise


def get_netcdf_value_at_coords(ds: xr.Dataset, var_name: str, lon: float, lat: float,
                              lon_array: np.ndarray, lat_array: np.ndarray,
                              lon_min: float, lat_min: float) -> Optional[float]:
    """
    获取指定坐标处的NetCDF数据值

    Args:
        ds: xarray.Dataset对象
        var_name: 变量名
        lon: 经度
        lat: 纬度
        lon_array: 经度数组
        lat_array: 纬度数组
        lon_min: 最小经度
        lat_min: 最小纬度

    Returns:
        float or None: 数据值，如果无效则返回None
    """
    try:
        # 计算索引（使用0.01度的步长）
        step = 0.01
        lon_idx = int(round((lon - lon_min) / step))
        lat_idx = int(round((lat - lat_min) / step))

        # 检查索引是否在有效范围内
        if (0 <= lon_idx < len(lon_array) and 0 <= lat_idx < len(lat_array)):
            # 获取数据值
            if var_name in ds.data_vars:
                data_var = ds[var_name]

                # 处理不同的数据维度
                if len(data_var.dims) == 2:
                    value = data_var.isel({data_var.dims[0]: lat_idx, data_var.dims[1]: lon_idx})
                elif len(data_var.dims) == 3:
                    # 假设第一个维度是时间
                    value = data_var.isel({data_var.dims[0]: 0, data_var.dims[1]: lat_idx, data_var.dims[2]: lon_idx})
                else:
                    logger.warning(f"不支持的数据维度: {data_var.dims}")
                    return None

                # 转换为Python标量
                value = float(value.values)

                # 检查是否为有效值
                if np.isnan(value) or np.isinf(value):
                    return None

                return value
            else:
                logger.warning(f"变量 {var_name} 不存在于数据集中")
                return None
        else:
            return None

    except Exception as e:
        logger.warning(f"获取坐标 ({lon}, {lat}) 处的数据值失败: {e}")
        return None


def validate_netcdf_file(file_path: str, required_vars: List[str] = None) -> bool:
    """
    验证NetCDF文件的有效性

    Args:
        file_path: NetCDF文件路径
        required_vars: 必需的变量列表

    Returns:
        bool: 文件是否有效
    """
    try:
        if not os.path.exists(file_path):
            logger.error(f"文件不存在: {file_path}")
            return False

        # 尝试打开文件
        with xr.open_dataset(file_path) as ds:
            # 检查是否有数据变量
            if not ds.data_vars:
                logger.error(f"文件中没有数据变量: {file_path}")
                return False

            # 检查必需的变量
            if required_vars:
                missing_vars = [var for var in required_vars if var not in ds.data_vars]
                if missing_vars:
                    logger.error(f"缺少必需的变量 {missing_vars}: {file_path}")
                    return False

            # 检查坐标
            try:
                extract_netcdf_coordinates(ds)
            except Exception as e:
                logger.error(f"坐标验证失败: {e}")
                return False

        logger.info(f"NetCDF文件验证通过: {file_path}")
        return True

    except Exception as e:
        logger.error(f"NetCDF文件验证失败 {file_path}: {e}")
        return False


def test_downloader():
    """测试下载器功能"""
    logger.info("开始测试天气数据下载器...")

    # 测试6分钟数据下载
    logger.info("测试6分钟数据下载...")
    result_6m = download_weather_data("gz_mpfv3")
    if result_6m:
        logger.info(f"6分钟数据下载成功: {result_6m}")
    else:
        logger.warning("6分钟数据下载失败")

    # 测试1小时数据下载
    for data_type in ["PRE", "TEM", "WEATHER", "VIS"]:
        logger.info(f"测试1小时{data_type}数据下载...")
        download_type = f"gz_didiforecast1h{data_type}"
        result = download_weather_data(download_type)
        if result:
            logger.info(f"1小时{data_type}数据下载成功: {result}")
        else:
            logger.warning(f"1小时{data_type}数据下载失败")

    logger.info("下载器测试完成")


if __name__ == "__main__":
    test_downloader()
