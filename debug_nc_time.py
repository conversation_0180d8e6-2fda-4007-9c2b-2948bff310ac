#!/usr/bin/env python3
# coding: utf-8
"""
调试NC文件时间信息的脚本
用于检查实况数据NC文件中的时间相关变量和坐标
"""

import os
import xarray as xr
import pandas as pd
import numpy as np
from pathlib import Path

def debug_nc_file_time_info(nc_file_path):
    """
    调试单个NC文件的时间信息
    """
    print(f"\n{'='*60}")
    print(f"调试NC文件: {os.path.basename(nc_file_path)}")
    print(f"完整路径: {nc_file_path}")
    print(f"{'='*60}")
    
    try:
        # 打开NC文件
        ds = xr.open_dataset(nc_file_path, engine="netcdf4")
        
        # 1. 基本信息
        print(f"\n1. 基本信息:")
        print(f"   文件维度: {dict(ds.dims)}")
        print(f"   坐标变量: {list(ds.coords.keys())}")
        print(f"   数据变量: {list(ds.data_vars.keys())}")
        
        # 2. 全局属性
        print(f"\n2. 全局属性:")
        for attr_name, attr_value in ds.attrs.items():
            print(f"   {attr_name}: {attr_value}")
        
        # 3. 所有时间相关的坐标
        print(f"\n3. 时间相关坐标:")
        time_coords = []
        for coord_name in ds.coords:
            if any(keyword in coord_name.lower() for keyword in ['time', 'ref', 'date']):
                time_coords.append(coord_name)
                coord_data = ds.coords[coord_name]
                print(f"   坐标名: {coord_name}")
                print(f"     形状: {coord_data.shape}")
                print(f"     数据类型: {coord_data.dtype}")
                print(f"     值: {coord_data.values}")
                if hasattr(coord_data, 'attrs') and coord_data.attrs:
                    print(f"     属性: {coord_data.attrs}")
                print()
        
        # 4. 所有时间相关的数据变量
        print(f"\n4. 时间相关数据变量:")
        time_vars = []
        for var_name in ds.data_vars:
            if any(keyword in var_name.lower() for keyword in ['time', 'ref', 'date']):
                time_vars.append(var_name)
                var_data = ds.data_vars[var_name]
                print(f"   变量名: {var_name}")
                print(f"     形状: {var_data.shape}")
                print(f"     数据类型: {var_data.dtype}")
                print(f"     维度: {var_data.dims}")
                print(f"     值: {var_data.values}")
                if hasattr(var_data, 'attrs') and var_data.attrs:
                    print(f"     属性: {var_data.attrs}")
                print()
        
        # 5. 尝试不同的时间解析方法
        print(f"\n5. 时间解析尝试:")
        
        # 方法1: 使用time数据变量 + reftime坐标
        if 'time' in ds.data_vars and 'reftime' in ds.coords:
            try:
                reftime_str = str(ds.coords['reftime'].values[0])
                reftime = pd.to_datetime(reftime_str, format='%Y%m%d%H%M')
                time_offset_hours = float(ds.data_vars['time'].values[0])
                actual_time = reftime + pd.Timedelta(hours=time_offset_hours)
                print(f"   方法1 (time变量+reftime): ")
                print(f"     reftime: {reftime}")
                print(f"     time偏移: {time_offset_hours} 小时")
                print(f"     实际时间: {actual_time}")
                print(f"     ✓ 推荐使用此方法")
            except Exception as e:
                print(f"   方法1 失败: {e}")
        
        # 方法2: 直接使用reftime
        if 'reftime' in ds.coords:
            try:
                reftime_str = str(ds.coords['reftime'].values[0])
                reftime = pd.to_datetime(reftime_str, format='%Y%m%d%H%M')
                print(f"   方法2 (仅reftime): {reftime}")
            except Exception as e:
                print(f"   方法2 失败: {e}")
        
        # 方法3: 直接解析time变量
        if 'time' in ds.data_vars:
            try:
                time_val = ds.data_vars['time'].values[0]
                direct_time = pd.to_datetime(time_val)
                print(f"   方法3 (直接解析time): {direct_time}")
            except Exception as e:
                print(f"   方法3 失败: {e}")
        
        # 方法4: 检查其他时间坐标
        for coord_name in ds.coords:
            if 'time' in coord_name.lower() and coord_name != 'reftime':
                try:
                    coord_time = pd.to_datetime(ds.coords[coord_name].values[0])
                    print(f"   方法4 ({coord_name}坐标): {coord_time}")
                except Exception as e:
                    print(f"   方法4 ({coord_name}) 失败: {e}")
        
        # 6. 检查一些关键数据变量的信息
        print(f"\n6. 关键数据变量信息:")
        key_vars = ['PRE10m', 'PRE1h', 'TEM', 'VIS', 'WEATHER']
        for var_name in key_vars:
            if var_name in ds.data_vars:
                var_data = ds.data_vars[var_name]
                print(f"   {var_name}: 形状={var_data.shape}, 维度={var_data.dims}")
                # 显示一些统计信息
                values = var_data.values
                if np.issubdtype(values.dtype, np.number):
                    valid_mask = ~np.isnan(values) if np.issubdtype(values.dtype, np.floating) else values >= 0
                    valid_count = np.sum(valid_mask)
                    if valid_count > 0:
                        print(f"     有效值数量: {valid_count}/{values.size}")
                        print(f"     值范围: {np.min(values[valid_mask]):.3f} ~ {np.max(values[valid_mask]):.3f}")
        
        ds.close()
        
    except Exception as e:
        print(f"处理文件时出错: {e}")
        import traceback
        traceback.print_exc()

def find_and_debug_obs_nc_files():
    """
    查找并调试所有实况NC文件
    """
    script_dir = Path(__file__).parent
    
    # 搜索目录
    search_dirs = [
        script_dir / 'data/test',
        script_dir / 'data/obs',
        script_dir / 'data/obs/nc_files'
    ]
    
    nc_files = []
    for base_dir in search_dirs:
        if base_dir.exists():
            for nc_file in base_dir.rglob('*.nc'):
                nc_files.append(str(nc_file))
    
    if not nc_files:
        print("未找到任何NC文件")
        print(f"搜索目录: {[str(d) for d in search_dirs]}")
        return
    
    print(f"找到 {len(nc_files)} 个NC文件:")
    for i, nc_file in enumerate(nc_files, 1):
        print(f"  {i}. {nc_file}")
    
    # 调试每个文件（最多调试前3个文件）
    for nc_file in nc_files[:3]:
        debug_nc_file_time_info(nc_file)
    
    if len(nc_files) > 3:
        print(f"\n注意: 只调试了前3个文件，还有 {len(nc_files) - 3} 个文件未调试")

if __name__ == '__main__':
    print("实况NC文件时间信息调试工具")
    print("当前时间:", pd.Timestamp.now())
    find_and_debug_obs_nc_files()
