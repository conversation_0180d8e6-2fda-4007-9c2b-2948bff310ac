# 天气数据定时任务调度器 - 快速启动指南

## 🚀 5分钟快速启动

### 1. 环境准备

```bash
# 克隆项目（如果需要）
git clone <repository-url>
cd weather_script

# 安装依赖
uv sync
```

### 2. 配置检查

确保 `config.yml` 中的数据库、API和调度器配置正确：

```yaml
# 数据库配置
database:
  postgres:
    host: "***************"
    port: 5432
    database: "middle-data-dev"
    username: "root"
    password: "Ylzx@9008*12-3*"

# 天气数据下载配置
weather_download:
  api_base_url: "http://way.weatherdt.com/apimall/basic/ncfile.htm"
  api_key: "d23bf68181a1a038a0dfb0deaa04232f"
  enable_download: true

# 调度器配置（可选，使用默认值）
scheduler:
  uptime_check:
    retry_interval_seconds: 30  # API重试间隔
    max_retry_duration_minutes: 60  # 最大重试时长
  web:
    host: "0.0.0.0"
    port: 8000
```

### 3. 验证配置

```bash
# 验证配置文件是否正确
uv run python validate_config.py
```

### 4. 启动调度器

```bash
# 启动定时任务调度器
uv run python run_weather_scheduler.py
```

### 5. 验证运行

打开浏览器访问：http://localhost:8000

你将看到调度器的Web界面，包含：
- 系统状态
- 任务队列
- 资源使用情况
- 下次执行时间

## 📊 监控界面

### 主要端点

- **系统状态**: http://localhost:8000/status
- **健康检查**: http://localhost:8000/health
- **所有任务**: http://localhost:8000/tasks
- **运行中任务**: http://localhost:8000/tasks/running
- **队列统计**: http://localhost:8000/queues
- **资源统计**: http://localhost:8000/resources

### 示例API调用

```bash
# 检查系统健康状态
curl http://localhost:8000/health

# 查看下次调度时间
curl http://localhost:8000/schedule/next

# 检查数据新鲜度
curl http://localhost:8000/data-freshness/gz_mpfv3
```

## ⏰ 调度时间表

### 1小时任务
- **执行时间**: 每小时的第4分钟（01:04, 02:04, 03:04...）
- **数据类型**: PRE（降水）、TEM（温度）、WEATHER（天气现象）、VIS（能见度）
- **执行顺序**: 串行执行，一个完成后执行下一个

### 6分钟任务
- **执行时间**: 04, 10, 16, 22, 28, 34, 40, 46, 52, 58分钟
- **数据类型**: gz_mpfv3（6分钟降水数据）
- **执行频率**: 每6分钟一次

## 🔧 常见问题

### Q: 调度器启动失败
**A**: 检查数据库连接配置和网络连接

### Q: 任务执行失败
**A**: 查看日志文件 `weather_scheduler.log`，检查API密钥和网络状态

### Q: 数据不够新鲜
**A**: 系统会自动重试，最多等待60分钟。检查数据源状态。

### Q: 如何停止调度器
**A**: 使用 Ctrl+C 或发送 SIGTERM 信号

## 📝 日志查看

```bash
# 实时查看日志
tail -f weather_scheduler.log

# 查看最近的错误
grep ERROR weather_scheduler.log | tail -10
```

## 🧪 测试系统

```bash
# 运行系统测试
uv run python test_scheduler.py

# 测试单个组件
uv run python -c "
import asyncio
import sys
from pathlib import Path
sys.path.insert(0, str(Path('src')))
from uptime_checker import is_data_fresh
print(asyncio.run(is_data_fresh('gz_mpfv3')))
"
```

## 📈 性能优化建议

### 生产环境配置

1. **调整资源池大小**（在 `src/resource_manager.py` 中）：
   ```python
   self.max_db_connections = 30  # 增加数据库连接数
   self.max_threads = 50         # 增加线程数
   ```

2. **配置日志轮转**：
   ```python
   # 在 weather_scheduler_app.py 中添加
   from logging.handlers import RotatingFileHandler
   handler = RotatingFileHandler('weather_scheduler.log', maxBytes=10*1024*1024, backupCount=5)
   ```

3. **使用进程管理器**：
   ```bash
   # 使用 systemd 或 supervisor 管理进程
   # 示例 systemd 服务文件
   [Unit]
   Description=Weather Data Scheduler
   After=network.target

   [Service]
   Type=simple
   User=weather
   WorkingDirectory=/path/to/weather_script
   ExecStart=/path/to/uv run python run_weather_scheduler.py
   Restart=always

   [Install]
   WantedBy=multi-user.target
   ```

## 🔗 相关文档

- [详细使用指南](docs/SCHEDULER_GUIDE.md)
- [API文档](http://localhost:8000/docs) - 启动后访问
- [项目README](README.md)

---

**需要帮助？** 查看日志文件或访问 http://localhost:8000/health 检查系统状态。
