@echo off
REM ============================================================================
REM Windows 依赖安装脚本
REM 用于在Windows环境下安装weather-script项目的所有依赖
REM ============================================================================

echo [INFO] 开始安装 weather-script 项目依赖...
echo [INFO] 操作系统: Windows
echo [INFO] 包管理器: uv
echo.

REM 检查uv是否已安装
echo [INFO] 检查uv包管理器...
uv --version >nul 2>&1
if %errorlevel% neq 0 (
    echo [ERROR] uv未安装或不在PATH中
    echo [INFO] 请先安装uv: https://docs.astral.sh/uv/getting-started/installation/
    echo [INFO] 或使用PowerShell安装: powershell -c "irm https://astral.sh/uv/install.ps1 | iex"
    pause
    exit /b 1
)
echo [SUCCESS] uv已安装

REM 检查Python版本
echo [INFO] 检查Python版本...
python --version 2>nul | findstr "3.12" >nul
if %errorlevel% neq 0 (
    echo [WARNING] 建议使用Python 3.12，当前版本可能不兼容
    python --version 2>nul
    echo [INFO] 继续安装...
)

REM 检查wheels目录
echo [INFO] 检查本地wheel文件...
if not exist "wheels\" (
    echo [ERROR] wheels目录不存在
    echo [INFO] 请确保wheels目录存在并包含必要的wheel文件
    pause
    exit /b 1
)

if not exist "wheels\gdal-3.10.2-cp312-cp312-win_amd64.whl" (
    echo [ERROR] GDAL wheel文件不存在
    echo [INFO] 请确保wheels目录包含: gdal-3.10.2-cp312-cp312-win_amd64.whl
    pause
    exit /b 1
)
echo [SUCCESS] 本地wheel文件检查完成

REM 创建虚拟环境（如果不存在）
echo [INFO] 检查虚拟环境...
if not exist ".venv\" (
    echo [INFO] 创建虚拟环境...
    uv venv
    if %errorlevel% neq 0 (
        echo [ERROR] 创建虚拟环境失败
        pause
        exit /b 1
    )
    echo [SUCCESS] 虚拟环境创建完成
) else (
    echo [INFO] 虚拟环境已存在
)

REM 同步依赖
echo [INFO] 开始同步项目依赖...
echo [INFO] 这可能需要几分钟时间，请耐心等待...
uv sync
if %errorlevel% neq 0 (
    echo [ERROR] 依赖同步失败
    echo [INFO] 请检查网络连接和pyproject.toml配置
    pause
    exit /b 1
)
echo [SUCCESS] 项目依赖同步完成

REM 安装开发依赖（可选）
echo.
set /p install_dev="是否安装开发依赖 (ruff, pytest)? (y/N): "
if /i "%install_dev%"=="y" (
    echo [INFO] 安装开发依赖...
    uv sync --extra dev
    if %errorlevel% neq 0 (
        echo [ERROR] 开发依赖安装失败
    ) else (
        echo [SUCCESS] 开发依赖安装完成
    )
)

REM 验证关键依赖
echo.
echo [INFO] 验证关键依赖安装...
echo [INFO] 测试GDAL导入...
uv run python -c "import gdal; print(f'GDAL版本: {gdal.__version__}')" 2>nul
if %errorlevel% neq 0 (
    echo [WARNING] GDAL导入失败，可能需要手动检查
) else (
    echo [SUCCESS] GDAL导入成功
)

echo [INFO] 测试geopandas导入...
uv run python -c "import geopandas; print(f'GeoPandas版本: {geopandas.__version__}')" 2>nul
if %errorlevel% neq 0 (
    echo [WARNING] GeoPandas导入失败
) else (
    echo [SUCCESS] GeoPandas导入成功
)

echo [INFO] 测试pandas导入...
uv run python -c "import pandas; print(f'Pandas版本: {pandas.__version__}')" 2>nul
if %errorlevel% neq 0 (
    echo [WARNING] Pandas导入失败
) else (
    echo [SUCCESS] Pandas导入成功
)

REM 显示安装摘要
echo.
echo ============================================================================
echo [SUCCESS] 依赖安装完成！
echo ============================================================================
echo [INFO] 虚拟环境位置: .venv\
echo [INFO] 激活虚拟环境: .venv\Scripts\activate
echo [INFO] 运行脚本示例: uv run python src/main.py
echo [INFO] 查看已安装包: uv pip list
echo.
echo [INFO] 如果遇到问题，请检查：
echo [INFO] 1. Python版本是否为3.12
echo [INFO] 2. wheels目录是否包含正确的wheel文件
echo [INFO] 3. 网络连接是否正常
echo [INFO] 4. 运行测试: uv run python tests/test_gdal_dependencies.py
echo ============================================================================

pause
