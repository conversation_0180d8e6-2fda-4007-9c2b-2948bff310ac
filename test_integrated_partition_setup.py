#!/usr/bin/env python3
# coding: utf-8
"""
测试集成的分区设置功能
验证 create_forecast_tables.py 中的分区管理配置是否正常工作
"""

from sqlalchemy import create_engine, text
from src.config import PG_URL

def test_partition_management_integration():
    """
    测试分区管理集成功能
    """
    engine = create_engine(PG_URL)
    
    try:
        with engine.begin() as conn:
            print("测试分区管理集成...")
            
            # 1. 检查 pg_partman 扩展
            print("\n1. 检查扩展状态:")
            extensions = conn.execute(text("""
                SELECT extname, extversion 
                FROM pg_extension 
                WHERE extname IN ('pg_partman', 'pg_cron')
                ORDER BY extname
            """)).fetchall()
            
            partman_installed = False
            cron_installed = False
            
            if extensions:
                for ext in extensions:
                    print(f"  ✓ {ext[0]} v{ext[1]}")
                    if ext[0] == 'pg_partman':
                        partman_installed = True
                    elif ext[0] == 'pg_cron':
                        cron_installed = True
            else:
                print("  ⚠️ 未找到相关扩展")
            
            if not partman_installed:
                print("  ❌ pg_partman 扩展未安装")
                print("     请先安装: CREATE EXTENSION pg_partman;")
                return False
            
            # 2. 检查分区配置
            print("\n2. 检查分区配置:")
            configs = conn.execute(text("""
                SELECT 
                    parent_table,
                    control,
                    partition_interval,
                    premake,
                    retention
                FROM partman.part_config
                ORDER BY parent_table
            """)).fetchall()
            
            if configs:
                print(f"  ✓ 找到 {len(configs)} 个分区配置:")
                for config in configs:
                    print(f"    {config[0]}: {config[1]} | {config[2]} | 预创建:{config[3]} | 保留:{config[4]}")
            else:
                print("  ⚠️ 没有找到分区配置")
                print("     这可能意味着:")
                print("     - create_forecast_tables.py 还未运行")
                print("     - 分区管理配置失败")
                return False
            
            # 3. 检查维护任务
            print("\n3. 检查维护任务:")
            if cron_installed:
                jobs = conn.execute(text("""
                    SELECT jobid, jobname, schedule, command, active
                    FROM cron.job
                    WHERE jobname = 'partman-maintenance'
                """)).fetchall()
                
                if jobs:
                    for job in jobs:
                        status = "活跃" if job[4] else "非活跃"
                        print(f"  ✓ 维护任务: {job[1]} ({status})")
                        print(f"    调度: {job[2]}")
                        print(f"    命令: {job[3]}")
                else:
                    print("  ⚠️ 没有找到维护任务")
            else:
                print("  ⚠️ pg_cron 扩展未安装，无法检查定时任务")
            
            # 4. 检查表结构
            print("\n4. 检查分区表结构:")
            tables = conn.execute(text("""
                SELECT 
                    c.relname as table_name,
                    c.relkind,
                    pg_get_partkeydef(c.oid) as partition_key
                FROM pg_class c
                JOIN pg_namespace n ON n.oid = c.relnamespace
                WHERE n.nspname = 'public' 
                  AND c.relkind = 'p'
                  AND (c.relname LIKE 'weather_%' OR c.relname LIKE 'forecast_%')
                ORDER BY c.relname
            """)).fetchall()
            
            if tables:
                print(f"  ✓ 找到 {len(tables)} 个分区表:")
                for table in tables:
                    print(f"    {table[0]}: 分区键 = {table[2]}")
            else:
                print("  ❌ 没有找到分区表")
                print("     请检查表是否正确创建为分区表")
                return False
            
            # 5. 验证主键结构
            print("\n5. 验证主键结构:")
            primary_keys = conn.execute(text("""
                SELECT 
                    t.table_name,
                    string_agg(k.column_name, ', ' ORDER BY k.ordinal_position) as pk_columns
                FROM information_schema.tables t
                JOIN information_schema.key_column_usage k ON k.table_name = t.table_name
                JOIN information_schema.table_constraints c ON c.constraint_name = k.constraint_name
                WHERE t.table_schema = 'public' 
                  AND (t.table_name LIKE 'weather_%' OR t.table_name LIKE 'forecast_%')
                  AND c.constraint_type = 'PRIMARY KEY'
                  AND t.table_type = 'BASE TABLE'
                GROUP BY t.table_name
                ORDER BY t.table_name
            """)).fetchall()
            
            if primary_keys:
                print("  ✓ 主键结构:")
                for pk in primary_keys:
                    # 检查主键是否包含分区键
                    if pk[0] == 'weather_alarm':
                        expected_key = 'publish_time'
                    else:
                        expected_key = 'pre_time'
                    
                    if expected_key in pk[1]:
                        print(f"    ✓ {pk[0]}: ({pk[1]})")
                    else:
                        print(f"    ❌ {pk[0]}: ({pk[1]}) - 缺少分区键 {expected_key}")
            else:
                print("  ⚠️ 没有找到主键信息")
            
            print("\n" + "=" * 60)
            print("✓ 分区管理集成测试完成")
            
            # 总结
            expected_tables = 12  # 11个核心表 + 1个预警表
            if len(configs) == expected_tables and len(tables) == expected_tables:
                print("✓ 所有表都已正确配置分区管理")
                return True
            else:
                print(f"⚠️ 期望 {expected_tables} 个表，实际配置 {len(configs)} 个，分区表 {len(tables)} 个")
                return False
                
    except Exception as e:
        print(f"测试过程中发生错误: {e}")
        return False
    finally:
        engine.dispose()

def test_manual_maintenance():
    """
    测试手动运行维护
    """
    engine = create_engine(PG_URL)
    
    try:
        with engine.begin() as conn:
            print("\n测试手动维护...")
            
            # 运行维护过程
            conn.execute(text("CALL partman.run_maintenance_proc()"))
            print("✓ 维护过程执行成功")
            
            # 检查是否创建了分区
            partitions = conn.execute(text("""
                SELECT 
                    schemaname,
                    tablename,
                    pg_size_pretty(pg_total_relation_size(schemaname||'.'||tablename)) as size
                FROM pg_tables 
                WHERE tablename ~ '_p\d{8}$' OR tablename ~ '_p\d{6}$'
                ORDER BY schemaname, tablename
                LIMIT 10
            """)).fetchall()
            
            if partitions:
                print(f"✓ 找到 {len(partitions)} 个子分区 (显示前10个):")
                for partition in partitions:
                    print(f"  {partition[0]}.{partition[1]} ({partition[2]})")
            else:
                print("ℹ️ 暂无子分区，将在数据插入时自动创建")
                
    except Exception as e:
        print(f"手动维护测试失败: {e}")
    finally:
        engine.dispose()

def main():
    """
    主函数
    """
    print("分区管理集成测试")
    print("=" * 60)
    
    # 测试分区管理集成
    success = test_partition_management_integration()
    
    if success:
        # 测试手动维护
        test_manual_maintenance()
        
        print("\n" + "=" * 60)
        print("✓ 所有测试通过！")
        print("\n下一步建议:")
        print("1. 开始使用应用程序插入数据")
        print("2. 观察分区自动创建")
        print("3. 监控维护任务的执行")
    else:
        print("\n" + "=" * 60)
        print("❌ 测试发现问题，请检查配置")
        print("\n建议:")
        print("1. 确保 pg_partman 扩展已安装")
        print("2. 重新运行 create_forecast_tables.py")
        print("3. 检查数据库日志")

if __name__ == '__main__':
    main()
