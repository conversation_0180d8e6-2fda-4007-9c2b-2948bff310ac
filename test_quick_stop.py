#!/usr/bin/env python3
# coding: utf-8
"""
测试快速停止功能
"""

import sys
import time
import subprocess
import psutil
import logging
from pathlib import Path

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


def test_windows_service():
    """测试Windows服务的快速停止"""
    logger.info("=== 测试Windows服务快速停止 ===")
    
    try:
        # 启动服务
        logger.info("启动服务...")
        start_time = time.time()
        result = subprocess.run(['weather-scheduler.exe', 'start'], 
                              capture_output=True, text=True, timeout=30)
        if result.returncode != 0:
            logger.error(f"启动服务失败: {result.stderr}")
            return False
        
        start_duration = time.time() - start_time
        logger.info(f"服务启动耗时: {start_duration:.2f}秒")
        
        # 等待服务完全启动
        time.sleep(5)
        
        # 停止服务并计时
        logger.info("停止服务...")
        stop_time = time.time()
        result = subprocess.run(['weather-scheduler.exe', 'stop'], 
                              capture_output=True, text=True, timeout=15)
        if result.returncode != 0:
            logger.error(f"停止服务失败: {result.stderr}")
            return False
        
        stop_duration = time.time() - stop_time
        logger.info(f"服务停止耗时: {stop_duration:.2f}秒")
        
        # 检查是否有残留进程
        time.sleep(1)
        remaining_processes = []
        for proc in psutil.process_iter(['pid', 'name', 'cmdline']):
            try:
                cmdline = proc.info['cmdline']
                if cmdline and any('run_weather_scheduler.py' in arg for arg in cmdline):
                    remaining_processes.append(proc.info['pid'])
            except (psutil.NoSuchProcess, psutil.AccessDenied):
                continue
        
        if remaining_processes:
            logger.warning(f"发现残留进程: {remaining_processes}")
            return False
        else:
            logger.info("✅ 没有残留进程")
        
        # 评估停止速度
        if stop_duration <= 5:
            logger.info("✅ 停止速度优秀 (≤5秒)")
        elif stop_duration <= 10:
            logger.info("⚠️ 停止速度良好 (≤10秒)")
        else:
            logger.warning("❌ 停止速度较慢 (>10秒)")
        
        return True
        
    except Exception as e:
        logger.error(f"测试Windows服务时出错: {e}")
        return False


def test_linux_service():
    """测试Linux服务的快速停止"""
    logger.info("=== 测试Linux服务快速停止 ===")
    
    try:
        # 启动服务
        logger.info("启动服务...")
        start_time = time.time()
        result = subprocess.run(['sudo', 'systemctl', 'start', 'weather-scheduler'], 
                              capture_output=True, text=True, timeout=30)
        if result.returncode != 0:
            logger.error(f"启动服务失败: {result.stderr}")
            return False
        
        start_duration = time.time() - start_time
        logger.info(f"服务启动耗时: {start_duration:.2f}秒")
        
        # 等待服务完全启动
        time.sleep(5)
        
        # 停止服务并计时
        logger.info("停止服务...")
        stop_time = time.time()
        result = subprocess.run(['sudo', 'systemctl', 'stop', 'weather-scheduler'], 
                              capture_output=True, text=True, timeout=15)
        if result.returncode != 0:
            logger.error(f"停止服务失败: {result.stderr}")
            return False
        
        stop_duration = time.time() - stop_time
        logger.info(f"服务停止耗时: {stop_duration:.2f}秒")
        
        # 检查是否有残留进程
        time.sleep(1)
        remaining_processes = []
        for proc in psutil.process_iter(['pid', 'name', 'cmdline']):
            try:
                cmdline = proc.info['cmdline']
                if cmdline and any('run_weather_scheduler.py' in arg for arg in cmdline):
                    remaining_processes.append(proc.info['pid'])
            except (psutil.NoSuchProcess, psutil.AccessDenied):
                continue
        
        if remaining_processes:
            logger.warning(f"发现残留进程: {remaining_processes}")
            return False
        else:
            logger.info("✅ 没有残留进程")
        
        # 评估停止速度
        if stop_duration <= 5:
            logger.info("✅ 停止速度优秀 (≤5秒)")
        elif stop_duration <= 10:
            logger.info("⚠️ 停止速度良好 (≤10秒)")
        else:
            logger.warning("❌ 停止速度较慢 (>10秒)")
        
        return True
        
    except Exception as e:
        logger.error(f"测试Linux服务时出错: {e}")
        return False


def test_stop_script():
    """测试停止脚本"""
    logger.info("=== 测试停止脚本 ===")
    
    try:
        # 直接启动调度器
        logger.info("直接启动调度器...")
        proc = subprocess.Popen([sys.executable, 'run_weather_scheduler.py'])
        
        # 等待启动
        time.sleep(3)
        
        # 使用停止脚本
        logger.info("使用停止脚本...")
        stop_time = time.time()
        
        if sys.platform == 'win32':
            result = subprocess.run([sys.executable, 'stop_weather_scheduler.py'], 
                                  capture_output=True, text=True, timeout=10)
        else:
            result = subprocess.run([sys.executable, 'stop_weather_scheduler_linux.py'], 
                                  capture_output=True, text=True, timeout=10)
        
        stop_duration = time.time() - stop_time
        logger.info(f"停止脚本执行耗时: {stop_duration:.2f}秒")
        
        if result.returncode == 0:
            logger.info("✅ 停止脚本执行成功")
            logger.info(f"输出: {result.stdout}")
        else:
            logger.error(f"❌ 停止脚本执行失败: {result.stderr}")
            return False
        
        # 检查进程是否真的停止了
        time.sleep(1)
        try:
            proc.wait(timeout=1)
            logger.info("✅ 调度器进程已停止")
        except subprocess.TimeoutExpired:
            logger.warning("⚠️ 调度器进程仍在运行，强制杀死")
            proc.kill()
            return False
        
        return True
        
    except Exception as e:
        logger.error(f"测试停止脚本时出错: {e}")
        return False


def main():
    """主函数"""
    logger.info("开始测试快速停止功能...")
    
    results = []
    
    # 测试停止脚本
    results.append(("停止脚本", test_stop_script()))
    
    # 根据平台测试服务
    if sys.platform == 'win32':
        if Path('weather-scheduler.exe').exists():
            results.append(("Windows服务", test_windows_service()))
        else:
            logger.warning("未找到weather-scheduler.exe，跳过Windows服务测试")
    else:
        results.append(("Linux服务", test_linux_service()))
    
    # 输出测试结果
    logger.info("\n" + "="*50)
    logger.info("测试结果汇总:")
    logger.info("="*50)
    
    all_passed = True
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        logger.info(f"{test_name}: {status}")
        if not result:
            all_passed = False
    
    if all_passed:
        logger.info("\n🎉 所有测试通过！快速停止功能正常工作。")
        return 0
    else:
        logger.error("\n❌ 部分测试失败，请检查配置和代码。")
        return 1


if __name__ == "__main__":
    sys.exit(main())
