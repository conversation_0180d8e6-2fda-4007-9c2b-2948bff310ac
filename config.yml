# WeatherApi 项目配置文件
# 修改此文件中的配置项，然后重新运行程序即可生效

# 数据库配置
database:
  # PostgreSQL / PostGIS 配置
  # postgres:
  #   host: "**********"
  #   port: 5432
  #   database: "mpkj-middle-data"
  #   username: "root"
  #   password: "Ylzx@2025"
  #   schema: "public"
  
  # # MySQL 配置（用于查询映射关系）
  # mysql:
  #   host: "**********"
  #   port: 3306
  #   username: "root"
  #   password: "Ylzx@2025"
  #   database: "ylzx-system"
  # PostgreSQL / PostGIS 配置
  postgres:
    host: "***************"
    port: 5432
    database: "middle-data-dev"
    username: "root"
    password: "Ylzx@9008*12-3*"
    schema: "public"
  
  # MySQL 配置（用于查询映射关系）
  mysql:
    host: "***************"
    port: 13306
    username: "root"
    password: "Ylzx@2000+!#-2"
    database: "ylzx-system-dev"

# 文件路径配置
# 注意: 所有路径都会基于脚本目录(/data/script/weather_script)进行解析
# 相对路径会自动转换为绝对路径，避免systemd启动时路径问题
paths:
  # 数据目录 - 将数据存储在独立的/data/weather目录
  weather_dir: "/data/weather"
  weather_mpf_dir: "/data/weather/MPF"
  weather_backup_dir: "/data/weather/backup"
  gis_route_shape: "./data/gis_route_shape/gis_route_shape.shp"

  # NetCDF 文件配置
  netcdf:
    default_file: "/data/weather/MPF/MPF_20250627172400.nc"
    var_name: "PRE"
    phase_var: "phase"

  # 行政区geojson文件配置
  administrative_regions:
    city_geojson: "./data/geojson/云南省_市.geojson"
    county_geojson: "./data/geojson/云南省_县.geojson"

# 格网参数配置
grid:
  # 格网范围（云南地区）
  x_min: 97.50
  y_min: 21.10
  x_max: 106.25
  y_max: 29.30
  size: 0.01  # 格网大小（度）
  buffer: 0.02  # 生成LUT时的缓冲距离（度）
  
  # 标准格网编码参数
  encoding:
    step: 0.01
    lon_offset: 180.0
    lat_offset: 90.0

# 数据库表名配置
tables:
  # 天气相关表名
  weather:
    weather_cell_6m: "weather_cell_6m"
    weather_routes: "weather_routes"
    weather_grid: "weather_grid"
    weather_cell_route_lut: "weather_cell_route_lut"
    weather_cell_route_lut_line: "weather_cell_route_lut_line"
  
  # 预报表名
  forecast:
    # 6分钟预报表
    forecast_6min_line: "forecast_precipitation_6min_line"
    forecast_6min_polygon: "forecast_precipitation_6min_polygon"
    forecast_6min_relation: "forecast_precipitation_6min_relation"
    
    # 1小时预报表
    forecast_hourly_line: "forecast_precipitation_hourly_line"
    forecast_hourly_polygon: "forecast_precipitation_hourly_polygon"
    forecast_hourly_relation: "forecast_precipitation_hourly_relation"
    
    # 日预报表
    forecast_daily_line: "precipitation_daily_line"
    forecast_daily_polygon: "precipitation_daily_polygon"
    forecast_daily_relation: "precipitation_daily_relation"

# Shapefile 字段映射配置
shapefile:
  fields:
    pile_start: "qdzh"  # 起点桩号（公里）
    pile_end: "zdzh"    # 终点桩号（公里）
    owner_unit: "owner_unit"  # 所有者单位
    route_code: "lxbh"  # 路线编号
    route_name: "lxmc"  # 路线名称
    maintenance_section: "maintenanc"  # 养护路段
    management_office: "tbdw"  # 管理单位
    xzqh: "xzqh"  # 行政区划代码字段

  # 行政区划代码处理配置
  xzqh_processing:
    target_digits: 6  # 标准化后的位数（6位表示县级）

# 处理参数配置
processing:
  # 多进程配置
  max_processes: null  # 正式环境增加进程数
  chunk_size: 1  # 每个进程一次处理的时间步数，提高批处理效率
  
  # 几何处理参数
  snap_tolerance: 0.00001  # 几何对齐容差

  # 线段合并参数
  merge_segments:
    # 最大间隙距离（度），如果为null则无距离限制
    # 示例值：0.001 约等于111米（在地理坐标系下）
    max_gap_distance: null  # 设置为null表示无距离限制，仅基于属性和桩号连续性合并
    # max_gap_distance: 0.001  # 取消注释并设置值来启用距离限制

# 存储过程配置
stored_procedures:
  default_calc_func: "get_rainfall_type"
  default_arg_fields: ["rainfall", "phase"]
  default_source_table: "weather_cell_6m"
  default_value_field: "rainfall"

  # 正式环境并发控制
  max_concurrent_procedures: 100     # 最大并发存储过程数，正式环境增加
  procedure_timeout_seconds: 1800   # 存储过程超时时间，正式环境设置为30分钟
  max_concurrent_db_operations: 100  # 最大并发数据库操作数，正式环境增加

# 天气数据处理配置
weather_processing:
  # 6分钟天气数据处理配置
  weather_6m:
    # 数据源配置
    data_source:
      data_type: "gz_mpfv3"
      variable_name: "WEATHER"
      description: "6分钟天气现象数据"

    # 处理参数
    processing:
      batch_size: 2000          # 批处理大小，正式环境增加
      max_concurrent_db: 100     # 最大并发数据库连接，正式环境增加
      timeout_seconds: 600      # 处理超时时间，正式环境延长
      enable_backup: true       # 是否备份处理后的文件

    # 数据库表配置
    database:
      target_table: "weather_cell_6m"
      conflict_columns: ["cell_id", "timestamp"]
      stored_procedure: "process_weather_6m_data"

    # 数据验证
    validation:
      required_variables: ["WEATHER"]
      min_valid_cells: 1000     # 最少有效格网数量
      value_range: [0, 100]     # 数据值范围

  # 1小时天气数据处理配置
  weather_1h:
    # 数据源配置
    data_source:
      data_types:
        PRE: "gz_didiforecast1hPRE"      # 降水
        TEM: "gz_didiforecast1hTEM"      # 温度
        WEATHER: "gz_didiforecast1hWEATHER"  # 天气现象
        VIS: "gz_didiforecast1hVIS"      # 能见度
      description: "1小时综合天气数据"

    # 处理参数
    processing:
      batch_size: 1500          # 批处理大小，正式环境增加
      max_concurrent_db: 100     # 最大并发数据库连接，正式环境增加
      timeout_seconds: 900      # 处理超时时间，正式环境延长
      enable_backup: true       # 是否备份处理后的文件
      priority_order: ["PRE", "TEM", "WEATHER", "VIS"]  # 处理优先级

    # 数据库表配置
    database:
      target_table: "weather_cell_1h"
      conflict_columns: ["cell_id", "timestamp"]
      stored_procedure: "process_weather_1h_data"

    # 数据验证
    validation:
      required_variables: ["PRE", "TEM"]  # 必需的变量
      optional_variables: ["WEATHER", "VIS"]  # 可选的变量
      min_valid_cells: 1000     # 最少有效格网数量
      value_ranges:             # 各变量的值范围
        PRE: [0, 200]          # 降水量 mm
        TEM: [-50, 60]         # 温度 °C
        WEATHER: [0, 100]      # 天气现象代码
        VIS: [0, 50000]        # 能见度 m

# 天气数据下载配置
weather_download:
  # API配置
  api_base_url: "http://way.weatherdt.com/apimall/basic/ncfile.htm"
  api_key: "d23bf68181a1a038a0dfb0deaa04232f"

# 预警信息API配置
weather_alarm:
  # API配置
  api_base_url: "http://api.weatherdt.com/common/"
  api_key: "a49adb3feddbfe93dee18b5a64dde1f1"
  type: "alarm"

  # 请求参数
  max_areas_per_request: 20  # 每次请求最多20个区域
  request_timeout: 30        # 请求超时时间（秒）
  max_retries: 3            # 最大重试次数
  retry_interval: 5         # 重试间隔（秒）

  # 调度配置
  schedule_interval_minutes: 10  # 每10分钟执行一次

  # 数据类型配置
  data_types:
    gz_mpfv3:
      name: "6分钟降水"
      folder: "precipitation_6min"
      description: "6分钟级降水预报数据"
    gz_didiforecast1hWEATHER:
      name: "1小时天气现象"
      folder: "weather_1h"
      description: "1小时天气现象预报数据"
    gz_didiforecast1hTEM:
      name: "1小时温度"
      folder: "temperature_1h"
      description: "1小时温度预报数据"
    gz_didiforecast1hPRE:
      name: "1小时降水"
      folder: "precipitation_1h"
      description: "1小时降水预报数据"
    gz_didiforecast1hVIS:
      name: "1小时能见度"
      folder: "visibility_1h"
      description: "1小时能见度预报数据"

  # 下载参数
  download_timeout: 600  # 下载超时时间（秒），正式环境延长
  max_retries: 5         # 最大重试次数，正式环境增加
  chunk_size: 16384      # 下载块大小（字节），正式环境增加
  retry_intervals: [20, 40, 80, 160]  # 重试间隔时间（秒），总计约5分钟
  api_timeout: 60        # API调用超时时间（秒），正式环境延长
  max_concurrent_downloads: 8  # 最大并发下载数，正式环境新增

  # 备份配置
  backup_keep_days: 30   # 备份文件保留天数
  auto_cleanup_backups: true  # 是否自动清理旧备份

  # 功能开关
  enable_download: true  # 是否启用下载功能
  enable_backup: true    # 是否启用备份功能

  # 默认数据类型
  default_data_type: "gz_mpfv3"  # 默认使用6分钟降水数据

# 定时任务调度器配置
scheduler:
  # 数据新鲜度检查配置
  uptime_check:
    retry_interval_seconds: 30  # 重试间隔（秒）
    max_retry_duration_minutes: 5  # 最大重试时长（分钟）
    retry_until_fresh: true  # 如果uptime不是新的，是否每隔30秒重新获取API直到获取到新数据

  # 任务配置
  tasks:
    # 1小时任务配置
    hourly:
      data_types: ["PRE", "TEM", "WEATHER", "VIS"]
      timeout_minutes: 30
      # schedule_pattern: "4 * * * *"  # 每小时的第4分钟执行
      schedule_pattern: "30 * * * *"  # 每小时的第30分钟执行

    # 6分钟任务配置
    six_minute:
      data_type: "gz_mpfv3"
      timeout_minutes: 30
      # schedule_pattern: "4,10,16,22,28,34,40,46,52,58 * * * *"  # 每6分钟+4分钟执行
      schedule_pattern: "2,8,14,20,26,32,38,44,50,56 * * * *"  # 每6分钟+4分钟执行

    # 实况数据任务配置
    observation:
      data_type: "gz_obsnc"
      timeout_minutes: 30
      # schedule_pattern: "8,18,28,38,48,58 * * * *"  # 每10分钟+8分钟执行，与其他任务错开
      schedule_pattern: "10,20,30,40,50 * * * *"  # 每10分钟+8分钟执行，与其他任务错开

  # 资源池配置
  resources:
    database:
      max_connections: 200  # 正式环境大幅增加数据库连接数
      min_connections: 50   # 正式环境增加最小连接数

    thread_pool:
      max_workers: 64       # 正式环境增加线程池大小

    process_pool:
      max_workers: null  # 自动检测CPU核心数

  # 监控配置
  monitoring:
    log_level: "INFO"
    log_file: "logs/weather_scheduler.log"  # 按天轮转，实际文件名会是 weather_scheduler_YYYY-MM-DD.log
    health_check_interval: 30  # 秒

  # Web服务配置
  web:
    host: "0.0.0.0"
    port: 8001
    reload: false
