-- PostgreSQL 分区管理设置 SQL 脚本
-- 使用 pg_partman 为所有分区表配置自动分区管理

-- 检查 pg_partman 扩展是否已安装
DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM pg_extension WHERE extname = 'pg_partman') THEN
        RAISE NOTICE '请先安装 pg_partman 扩展: CREATE EXTENSION pg_partman;';
        RAISE EXCEPTION 'pg_partman 扩展未安装';
    ELSE
        RAISE NOTICE 'pg_partman 扩展已安装';
    END IF;
END $$;

-- =============================================================================
-- 要求 A: 核心数据和派生结果表 (共11个)
-- 策略: 每日分区, 保留90天, 预创建7天
-- =============================================================================

-- 1. weather_cell_6m 表
SELECT partman.create_parent(
    p_parent_table := 'public.weather_cell_6m',
    p_control := 'pre_time',
    p_type := 'native',
    p_interval := '1 day',
    p_premake := 7,
    p_retention := '90 days',
    p_retention_keep_table := false
);

-- 2. weather_cell_1h 表
SELECT partman.create_parent(
    p_parent_table := 'public.weather_cell_1h',
    p_control := 'pre_time',
    p_type := 'native',
    p_interval := '1 day',
    p_premake := 7,
    p_retention := '90 days',
    p_retention_keep_table := false
);

-- 3. forecast_precipitation_6min_line 表
SELECT partman.create_parent(
    p_parent_table := 'public.forecast_precipitation_6min_line',
    p_control := 'pre_time',
    p_type := 'native',
    p_interval := '1 day',
    p_premake := 7,
    p_retention := '90 days',
    p_retention_keep_table := false
);

-- 4. forecast_precipitation_6min_polygon 表
SELECT partman.create_parent(
    p_parent_table := 'public.forecast_precipitation_6min_polygon',
    p_control := 'pre_time',
    p_type := 'native',
    p_interval := '1 day',
    p_premake := 7,
    p_retention := '90 days',
    p_retention_keep_table := false
);

-- 5. forecast_precipitation_6min_relation 表
SELECT partman.create_parent(
    p_parent_table := 'public.forecast_precipitation_6min_relation',
    p_control := 'pre_time',
    p_type := 'native',
    p_interval := '1 day',
    p_premake := 7,
    p_retention := '90 days',
    p_retention_keep_table := false
);

-- 6. forecast_precipitation_hourly_line 表
SELECT partman.create_parent(
    p_parent_table := 'public.forecast_precipitation_hourly_line',
    p_control := 'pre_time',
    p_type := 'native',
    p_interval := '1 day',
    p_premake := 7,
    p_retention := '90 days',
    p_retention_keep_table := false
);

-- 7. forecast_precipitation_hourly_polygon 表
SELECT partman.create_parent(
    p_parent_table := 'public.forecast_precipitation_hourly_polygon',
    p_control := 'pre_time',
    p_type := 'native',
    p_interval := '1 day',
    p_premake := 7,
    p_retention := '90 days',
    p_retention_keep_table := false
);

-- 8. forecast_precipitation_hourly_relation 表
SELECT partman.create_parent(
    p_parent_table := 'public.forecast_precipitation_hourly_relation',
    p_control := 'pre_time',
    p_type := 'native',
    p_interval := '1 day',
    p_premake := 7,
    p_retention := '90 days',
    p_retention_keep_table := false
);

-- 9. forecast_precipitation_summary_line 表
SELECT partman.create_parent(
    p_parent_table := 'public.forecast_precipitation_summary_line',
    p_control := 'pre_time',
    p_type := 'native',
    p_interval := '1 day',
    p_premake := 7,
    p_retention := '90 days',
    p_retention_keep_table := false
);

-- 10. forecast_precipitation_summary_polygon 表
SELECT partman.create_parent(
    p_parent_table := 'public.forecast_precipitation_summary_polygon',
    p_control := 'pre_time',
    p_type := 'native',
    p_interval := '1 day',
    p_premake := 7,
    p_retention := '90 days',
    p_retention_keep_table := false
);

-- 11. forecast_precipitation_summary_relation 表
SELECT partman.create_parent(
    p_parent_table := 'public.forecast_precipitation_summary_relation',
    p_control := 'pre_time',
    p_type := 'native',
    p_interval := '1 day',
    p_premake := 7,
    p_retention := '90 days',
    p_retention_keep_table := false
);

-- =============================================================================
-- 要求 B: weather_alarm 表
-- 策略: 每月分区, 保留3年, 预创建4个月
-- =============================================================================

-- 12. weather_alarm 表
SELECT partman.create_parent(
    p_parent_table := 'public.weather_alarm',
    p_control := 'publish_time',
    p_type := 'native',
    p_interval := '1 month',
    p_premake := 4,
    p_retention := '3 years',
    p_retention_keep_table := false
);

-- =============================================================================
-- 设置自动维护任务
-- =============================================================================

-- 检查并设置 pg_cron 定时任务
DO $$
BEGIN
    -- 检查 pg_cron 扩展是否存在
    IF EXISTS (SELECT 1 FROM pg_extension WHERE extname = 'pg_cron') THEN
        RAISE NOTICE 'pg_cron 扩展已安装，设置定时维护任务...';
        
        -- 删除可能存在的旧任务
        BEGIN
            PERFORM cron.unschedule('partman-maintenance');
            RAISE NOTICE '已删除旧的维护任务';
        EXCEPTION WHEN OTHERS THEN
            -- 任务可能不存在，忽略错误
        END;
        
        -- 创建新的维护任务 - 每天凌晨2点运行
        PERFORM cron.schedule(
            'partman-maintenance', 
            '0 2 * * *', 
            'CALL partman.run_maintenance_proc()'
        );
        
        RAISE NOTICE '定时维护任务已创建，将在每天凌晨2点自动运行';
        
    ELSE
        RAISE NOTICE 'pg_cron 扩展未安装';
        RAISE NOTICE '建议安装 pg_cron 扩展以启用自动维护: CREATE EXTENSION pg_cron;';
        RAISE NOTICE '或者手动设置系统 cron 任务:';
        RAISE NOTICE '  0 2 * * * psql -d your_database -c "CALL partman.run_maintenance_proc()"';
    END IF;
END $$;

-- =============================================================================
-- 验证分区设置
-- =============================================================================

-- 显示所有分区配置
SELECT 
    parent_table,
    control,
    partition_type,
    partition_interval,
    premake,
    retention,
    retention_keep_table
FROM partman.part_config
ORDER BY parent_table;

-- 显示当前已创建的分区
SELECT 
    schemaname,
    tablename,
    pg_size_pretty(pg_total_relation_size(schemaname||'.'||tablename)) as size
FROM pg_tables 
WHERE tablename ~ '_p\d{8}$'
   OR tablename ~ '_p\d{6}$'
ORDER BY schemaname, tablename;

-- 显示 pg_cron 任务（如果存在）
DO $$
BEGIN
    IF EXISTS (SELECT 1 FROM pg_extension WHERE extname = 'pg_cron') THEN
        RAISE NOTICE '当前 pg_cron 任务:';
        -- 注意：这里不能直接 SELECT，因为在 DO 块中
        -- 用户需要手动运行: SELECT * FROM cron.job;
    END IF;
END $$;

-- 完成提示
DO $$
BEGIN
    RAISE NOTICE '=================================================';
    RAISE NOTICE '分区管理设置完成！';
    RAISE NOTICE '=================================================';
    RAISE NOTICE '重要提醒:';
    RAISE NOTICE '1. 分区将在首次数据插入时自动创建';
    RAISE NOTICE '2. 维护任务将每天自动运行，管理分区生命周期';
    RAISE NOTICE '3. 可以手动运行 CALL partman.run_maintenance_proc() 立即执行维护';
    RAISE NOTICE '4. 监控日志以确保分区管理正常工作';
    RAISE NOTICE '5. 查看 pg_cron 任务: SELECT * FROM cron.job;';
    RAISE NOTICE '6. 查看分区配置: SELECT * FROM partman.part_config;';
END $$;
