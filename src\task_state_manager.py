#!/usr/bin/env python3
# coding: utf-8
"""
任务状态管理器
管理每个任务的完成状态，记录最后处理的uptime时间
支持5个任务：1h任务4个参数（PRE、TEM、WEATHER、VIS），6m任务1个（gz_mpfv3）
"""

import logging
import threading
from datetime import datetime
from typing import Dict, Optional, Set
from dataclasses import dataclass, field
import json

logger = logging.getLogger(__name__)


@dataclass
class TaskState:
    """任务状态信息"""
    task_type: str  # "1h" 或 "6m"
    data_type: str  # 数据类型：PRE/TEM/WEATHER/VIS/gz_mpfv3
    last_uptime: Optional[str] = None  # 最后处理的uptime，格式：YYYYMMDDHHMM
    last_update: Optional[datetime] = None  # 最后更新时间
    total_executions: int = 0  # 总执行次数
    
    def __post_init__(self):
        if self.last_update is None:
            self.last_update = datetime.now()
    
    @property
    def task_key(self) -> str:
        """获取任务唯一标识"""
        return f"{self.task_type}_{self.data_type}"
    
    def is_first_run(self) -> bool:
        """是否是首次运行"""
        return self.last_uptime is None
    
    def should_process(self, current_uptime: str) -> bool:
        """判断是否应该处理当前uptime的数据"""
        if self.is_first_run():
            return True
        
        # 比较uptime时间
        try:
            if len(current_uptime) == 12:  # YYYYMMDDHHMM
                current_time = datetime.strptime(current_uptime, "%Y%m%d%H%M")
                last_time = datetime.strptime(self.last_uptime, "%Y%m%d%H%M")
                return current_time > last_time
            else:
                logger.warning(f"无效的uptime格式: {current_uptime}")
                return False
        except ValueError as e:
            logger.error(f"解析uptime时间失败: {e}")
            return False
    
    def update_completion(self, uptime: str):
        """更新任务完成状态（已弃用，使用mark_task_completed代替）"""
        self.last_uptime = uptime
        self.last_update = datetime.now()
        self.total_executions += 1
        logger.info(f"任务 {self.task_key} 完成，uptime: {uptime}, 总执行次数: {self.total_executions}")


class TaskStateManager:
    """任务状态管理器"""
    
    def __init__(self):
        self._states: Dict[str, TaskState] = {}
        self._lock = threading.RLock()
        self._initialized = False
        
        # 定义5个任务
        self._task_definitions = [
            ("1h", "PRE"),
            ("1h", "TEM"), 
            ("1h", "WEATHER"),
            ("1h", "VIS"),
            ("6m", "gz_mpfv3")
        ]
        
        self._initialize_tasks()
    
    def _initialize_tasks(self):
        """初始化所有任务状态"""
        with self._lock:
            for task_type, data_type in self._task_definitions:
                task_key = f"{task_type}_{data_type}"
                if task_key not in self._states:
                    self._states[task_key] = TaskState(
                        task_type=task_type,
                        data_type=data_type
                    )
            
            self._initialized = True
            logger.info(f"任务状态管理器初始化完成，管理 {len(self._states)} 个任务")
    
    def get_task_state(self, task_type: str, data_type: str) -> Optional[TaskState]:
        """获取任务状态"""
        task_key = f"{task_type}_{data_type}"
        with self._lock:
            return self._states.get(task_key)
    
    def is_first_run(self, task_type: str, data_type: str) -> bool:
        """检查是否是首次运行"""
        state = self.get_task_state(task_type, data_type)
        return state.is_first_run() if state else True
    
    def should_process_uptime(self, task_type: str, data_type: str, current_uptime: str) -> bool:
        """判断是否应该处理当前uptime的数据"""
        state = self.get_task_state(task_type, data_type)
        if not state:
            logger.warning(f"未找到任务状态: {task_type}_{data_type}")
            return True
        
        should_process = state.should_process(current_uptime)
        
        if state.is_first_run():
            logger.info(f"任务 {state.task_key} 首次运行，处理uptime: {current_uptime}")
        elif should_process:
            logger.info(f"任务 {state.task_key} 发现新数据，当前uptime: {current_uptime}, 上次: {state.last_uptime}")
        else:
            logger.info(f"任务 {state.task_key} 数据已处理，跳过uptime: {current_uptime}, 上次: {state.last_uptime}")
        
        return should_process
    
    def mark_task_started(self, task_type: str, data_type: str, uptime: str):
        """标记任务开始处理（立即更新uptime状态）"""
        state = self.get_task_state(task_type, data_type)
        if state:
            with self._lock:
                old_uptime = state.last_uptime
                state.last_uptime = uptime
                state.last_update = datetime.now()
                logger.info(f"任务 {state.task_key} 开始处理，uptime: {uptime} (上次: {old_uptime})")
        else:
            logger.error(f"无法标记任务开始，未找到任务状态: {task_type}_{data_type}")

    def mark_task_completed(self, task_type: str, data_type: str, uptime: str):
        """标记任务完成"""
        state = self.get_task_state(task_type, data_type)
        if state:
            with self._lock:
                # 确保uptime是最新的（防止处理过程中uptime被其他地方修改）
                state.last_uptime = uptime
                state.last_update = datetime.now()
                state.total_executions += 1
                logger.info(f"任务 {state.task_key} 完成，uptime: {uptime}, 总执行次数: {state.total_executions}")
        else:
            logger.error(f"无法标记任务完成，未找到任务状态: {task_type}_{data_type}")
    
    def get_all_states(self) -> Dict[str, TaskState]:
        """获取所有任务状态"""
        with self._lock:
            return self._states.copy()
    
    def get_states_summary(self) -> Dict[str, dict]:
        """获取任务状态摘要"""
        with self._lock:
            summary = {}
            for task_key, state in self._states.items():
                summary[task_key] = {
                    "task_type": state.task_type,
                    "data_type": state.data_type,
                    "last_uptime": state.last_uptime,
                    "last_update": state.last_update.isoformat() if state.last_update else None,
                    "total_executions": state.total_executions,
                    "is_first_run": state.is_first_run()
                }
            return summary
    
    def reset_task_state(self, task_type: str, data_type: str):
        """重置任务状态（用于测试或重新开始）"""
        state = self.get_task_state(task_type, data_type)
        if state:
            with self._lock:
                state.last_uptime = None
                state.last_update = datetime.now()
                state.total_executions = 0
            logger.info(f"任务 {task_type}_{data_type} 状态已重置")
        else:
            logger.warning(f"无法重置任务状态，未找到: {task_type}_{data_type}")
    
    def reset_all_states(self):
        """重置所有任务状态"""
        with self._lock:
            for state in self._states.values():
                state.last_uptime = None
                state.last_update = datetime.now()
                state.total_executions = 0
        logger.info("所有任务状态已重置")
    
    def export_states_to_json(self) -> str:
        """导出任务状态为JSON格式"""
        summary = self.get_states_summary()
        return json.dumps(summary, indent=2, ensure_ascii=False)
    
    def get_pending_tasks(self) -> Set[str]:
        """获取待执行的任务（首次运行的任务）"""
        pending = set()
        with self._lock:
            for task_key, state in self._states.items():
                if state.is_first_run():
                    pending.add(task_key)
        return pending
    
    def get_statistics(self) -> Dict[str, int]:
        """获取统计信息"""
        with self._lock:
            stats = {
                "total_tasks": len(self._states),
                "first_run_tasks": len([s for s in self._states.values() if s.is_first_run()]),
                "completed_tasks": len([s for s in self._states.values() if not s.is_first_run()]),
                "total_executions": sum(s.total_executions for s in self._states.values())
            }
            
            # 按任务类型统计
            stats["1h_tasks"] = len([s for s in self._states.values() if s.task_type == "1h"])
            stats["6m_tasks"] = len([s for s in self._states.values() if s.task_type == "6m"])
            
            return stats


# 全局任务状态管理器实例
task_state_manager = TaskStateManager()


def should_process_task(task_type: str, data_type: str, current_uptime: str) -> bool:
    """
    便捷函数：判断是否应该处理任务
    
    Args:
        task_type: 任务类型 ("1h" 或 "6m")
        data_type: 数据类型 (PRE/TEM/WEATHER/VIS/gz_mpfv3)
        current_uptime: 当前uptime，格式：YYYYMMDDHHMM
    
    Returns:
        是否应该处理
    """
    return task_state_manager.should_process_uptime(task_type, data_type, current_uptime)


def mark_task_started(task_type: str, data_type: str, uptime: str):
    """
    便捷函数：标记任务开始处理

    Args:
        task_type: 任务类型 ("1h" 或 "6m")
        data_type: 数据类型 (PRE/TEM/WEATHER/VIS/gz_mpfv3)
        uptime: 开始处理的uptime，格式：YYYYMMDDHHMM
    """
    task_state_manager.mark_task_started(task_type, data_type, uptime)


def mark_task_completed(task_type: str, data_type: str, uptime: str):
    """
    便捷函数：标记任务完成

    Args:
        task_type: 任务类型 ("1h" 或 "6m")
        data_type: 数据类型 (PRE/TEM/WEATHER/VIS/gz_mpfv3)
        uptime: 完成的uptime，格式：YYYYMMDDHHMM
    """
    task_state_manager.mark_task_completed(task_type, data_type, uptime)


def is_first_run(task_type: str, data_type: str) -> bool:
    """
    便捷函数：检查是否是首次运行
    
    Args:
        task_type: 任务类型 ("1h" 或 "6m")
        data_type: 数据类型 (PRE/TEM/WEATHER/VIS/gz_mpfv3)
    
    Returns:
        是否是首次运行
    """
    return task_state_manager.is_first_run(task_type, data_type)


def get_task_states_summary() -> Dict[str, dict]:
    """
    便捷函数：获取所有任务状态摘要
    
    Returns:
        任务状态摘要字典
    """
    return task_state_manager.get_states_summary()


def reset_all_task_states():
    """
    便捷函数：重置所有任务状态
    """
    task_state_manager.reset_all_states()


if __name__ == "__main__":
    # 测试代码
    logging.basicConfig(level=logging.INFO)
    
    print("=== 任务状态管理器测试 ===")
    
    # 测试首次运行检查
    print(f"1h_PRE 首次运行: {is_first_run('1h', 'PRE')}")
    print(f"6m_gz_mpfv3 首次运行: {is_first_run('6m', 'gz_mpfv3')}")
    
    # 测试处理判断
    print(f"应该处理 1h_PRE 202507131304: {should_process_task('1h', 'PRE', '202507131304')}")
    
    # 标记完成
    mark_task_completed('1h', 'PRE', '202507131304')
    
    # 再次检查
    print(f"1h_PRE 首次运行: {is_first_run('1h', 'PRE')}")
    print(f"应该处理 1h_PRE 202507131304: {should_process_task('1h', 'PRE', '202507131304')}")
    print(f"应该处理 1h_PRE 202507131305: {should_process_task('1h', 'PRE', '202507131305')}")
    
    # 显示状态摘要
    print("\n=== 任务状态摘要 ===")
    summary = get_task_states_summary()
    for task_key, state in summary.items():
        print(f"{task_key}: {state}")
    
    # 显示统计信息
    print(f"\n=== 统计信息 ===")
    stats = task_state_manager.get_statistics()
    for key, value in stats.items():
        print(f"{key}: {value}")
