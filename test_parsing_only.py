#!/usr/bin/env python3
# coding: utf-8
"""
仅测试预警内容解析功能（不连接数据库）
"""

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

import re

def extract_admin_region_name(alarm_content: str) -> str:
    """提取行政区名称（气象台之前的内容）"""
    match = re.search(r'^(.+?)气象台', alarm_content)
    if match:
        return match.group(1).strip()
    return ""

def extract_content_after_colon(alarm_content: str) -> str:
    """提取第一个冒号后的内容"""
    colon_index = alarm_content.find('：')
    if colon_index == -1:
        colon_index = alarm_content.find(':')
    if colon_index != -1:
        return alarm_content[colon_index + 1:].strip()
    return ""

def extract_time_and_location(content: str) -> str:
    """提取时间信息后的地点内容"""
    # 简化的匹配策略
    # 1. 先找到"天"或"小时"的位置
    time_patterns = [r'未来\d+(?:天|小时)', r'\d+(?:天|小时)', r'(?:天|小时)']

    for time_pattern in time_patterns:
        match = re.search(time_pattern, content)
        if match:
            # 获取时间词后的内容
            after_time = content[match.end():].strip()

            # 去除开头的标点符号和空格
            after_time = re.sub(r'^[，,。！？\s]+', '', after_time)

            # 查找第一个包含地名的部分（包含县、市、镇、乡、区等）
            # 或者查找第一个逗号前的内容
            location_patterns = [
                r'([^，,。！？]*?(?:县|市|镇|乡|区|街道|村|等)[^，,。！？]*?)(?:[，,。！？]|$)',  # 包含地名标识
                r'([^，,。！？]+?)(?:[，,。！？]|$)'  # 第一个逗号前的内容
            ]

            for loc_pattern in location_patterns:
                loc_match = re.search(loc_pattern, after_time)
                if loc_match:
                    location_text = loc_match.group(1).strip()
                    if location_text:
                        return location_text

            break

    return ""

def clean_location_prefix(location: str, admin_region: str) -> str:
    """清理地点前缀"""
    # 去除常见前缀
    prefixes = ['我县', '我区', '我州', '我市']
    for prefix in prefixes:
        if location.startswith(prefix):
            location = location[len(prefix):].strip()
            break
    
    # 去除行政区名称前缀
    if admin_region and location.startswith(admin_region):
        location = location[len(admin_region):].strip()
    
    return location

def split_locations(location_text: str) -> list:
    """使用顿号分割地点"""
    # 使用顿号分割
    locations = re.split(r'[、]', location_text)
    return [loc.strip() for loc in locations if loc.strip()]

def test_parsing():
    """测试解析功能"""
    
    test_cases = [
        {
            "content": "墨江县气象台2025年7月18日07时05分发布大雾黄色预警信号：未来12小时墨江县大部乡镇将出现浓雾，请做好防护。（预警信息来源：国家预警信息发布中心）",
            "expected_admin": "墨江县"
        },
        {
            "content": "会泽县气象台2025年7月17日16时25分发布高温黄色预警信号：未来3天娜姑镇、纸厂乡的低矮河谷地区最高气温将在35℃以上，请注意防范。（预警信息来源：国家预警信息发布中心）",
            "expected_admin": "会泽县"
        },
        {
            "content": "彝良县气象台2025年7月18日9时00分继续发布高温橙色预警信号：未来24小时，彝良县洛旺、柳溪、牛街、角奎、发界、海子、洛泽河、两河等乡镇（街道）海拔1200米以下低矮河谷区域日最高气温将升至37℃以上，午后请减少户外活动。（预警信息来源：国家预警信息发布中心）",
            "expected_admin": "彝良县"
        },
        {
            "content": "绥江县气象台2025年7月18日08时34分发布高温橙色预警信号：未来24小时，我县南岸镇、板栗镇、中城镇、新滩镇、会仪镇最高气温将升至37℃以上，午后请减少户外活动。（预警信息来源：国家预警信息发布中心）",
            "expected_admin": "绥江县"
        }
    ]
    
    print("=" * 80)
    print("预警内容解析功能测试（仅解析，不连接数据库）")
    print("=" * 80)
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"\n测试用例 {i}:")
        print("-" * 60)
        
        content = test_case["content"]
        expected_admin = test_case["expected_admin"]
        
        print(f"预警内容: {content[:100]}...")
        print(f"预期行政区: {expected_admin}")
        
        # 1. 测试行政区名称提取
        admin_region = extract_admin_region_name(content)
        print(f"提取的行政区: {admin_region}")
        print(f"行政区提取{'✓' if admin_region == expected_admin else '✗'}")
        
        # 2. 测试冒号后内容提取
        content_after_colon = extract_content_after_colon(content)
        print(f"冒号后内容: {content_after_colon[:100]}...")
        
        # 3. 测试时间和地点提取
        location_text = extract_time_and_location(content_after_colon)
        print(f"提取的地点文本: '{location_text}'")
        
        # 4. 测试前缀清理
        cleaned_location = clean_location_prefix(location_text, admin_region)
        print(f"清理后的地点: '{cleaned_location}'")
        
        # 5. 测试地点分割
        locations = split_locations(cleaned_location)
        print(f"分割后的地点: {locations}")
    
    print("\n" + "=" * 80)
    print("测试完成")
    print("=" * 80)

if __name__ == "__main__":
    test_parsing()
