# 依赖安装指南

本项目提供了两个自动化脚本来安装所有必要的依赖，分别适用于Windows和Ubuntu系统。

## 📋 系统要求

### 通用要求
- Python 3.12+ (推荐)
- 网络连接（用于下载依赖包）

### Windows要求
- Windows 10/11
- PowerShell或命令提示符
- 管理员权限（可选，用于安装uv）

### Ubuntu要求
- Ubuntu 18.04+ 或兼容的Debian系统
- sudo权限
- 基本的构建工具

## 🚀 快速开始

### Windows系统

1. **运行安装脚本**
   ```cmd
   install_dependencies_windows.bat
   ```

2. **脚本会自动执行以下操作：**
   - 检查uv包管理器是否已安装
   - 验证Python版本
   - 检查本地wheel文件（特别是GDAL）
   - 创建虚拟环境
   - 同步所有项目依赖
   - 可选安装开发依赖
   - 验证关键依赖的导入

3. **如果uv未安装，请先安装：**
   ```powershell
   # 使用PowerShell安装uv
   powershell -c "irm https://astral.sh/uv/install.ps1 | iex"
   ```

### Ubuntu系统

1. **运行安装脚本**
   ```bash
   ./install_dependencies_ubuntu.sh
   ```

2. **脚本会自动执行以下操作：**
   - 更新系统包列表
   - 安装GDAL和地理空间系统库
   - 安装Python开发工具
   - 安装数据库客户端库
   - 安装uv包管理器
   - 设置GDAL环境变量
   - 创建虚拟环境并同步依赖
   - 验证安装结果

3. **首次运行可能需要输入sudo密码**

## 📦 主要依赖说明

### 地理空间处理
- **GDAL**: 地理空间数据抽象库
  - Windows: 使用本地wheel文件 (`wheels/gdal-3.10.2-cp312-cp312-win_amd64.whl`)
  - Ubuntu: 通过apt安装系统版本
- **GeoPandas**: 地理空间数据分析
- **Shapely**: 几何对象处理
- **Fiona**: 矢量数据I/O
- **Rasterio**: 栅格数据处理
- **PyProj**: 投影转换

### 数据处理
- **Pandas**: 数据分析库
- **NumPy**: 数值计算库
- **XArray**: 多维数组处理（NetCDF文件）

### 数据库连接
- **SQLAlchemy**: SQL工具包和ORM
- **GeoAlchemy2**: SQLAlchemy地理空间扩展
- **AsyncPG**: PostgreSQL异步驱动
- **PyMySQL**: MySQL驱动
- **psycopg2-binary**: PostgreSQL同步驱动

### Web框架
- **FastAPI**: 现代Web框架
- **Uvicorn**: ASGI服务器

## 🔧 手动安装（备选方案）

如果自动脚本遇到问题，可以手动安装：

### Windows手动安装
```cmd
# 1. 安装uv（如果未安装）
powershell -c "irm https://astral.sh/uv/install.ps1 | iex"

# 2. 创建虚拟环境
uv venv

# 3. 同步依赖
uv sync

# 4. 安装开发依赖（可选）
uv sync --extra dev
```

### Ubuntu手动安装
```bash
# 1. 安装系统依赖
sudo apt update
sudo apt install -y gdal-bin libgdal-dev libgeos-dev libproj-dev \
    python3-dev build-essential libpq-dev libnetcdf-dev

# 2. 安装uv
curl -LsSf https://astral.sh/uv/install.sh | sh
export PATH="$HOME/.cargo/bin:$PATH"

# 3. 设置GDAL环境变量
export GDAL_CONFIG=/usr/bin/gdal-config
export CPLUS_INCLUDE_PATH=/usr/include/gdal
export C_INCLUDE_PATH=/usr/include/gdal

# 4. 创建虚拟环境并同步依赖
uv venv
uv sync
```

## ✅ 验证安装

安装完成后，可以运行以下命令验证：

```bash
# 检查虚拟环境
uv run python --version

# 测试关键依赖
uv run python -c "import gdal; print(f'GDAL: {gdal.__version__}')"
uv run python -c "import geopandas; print(f'GeoPandas: {geopandas.__version__}')"
uv run python -c "import pandas; print(f'Pandas: {pandas.__version__}')"

# 运行依赖测试（如果存在）
uv run python tests/test_gdal_dependencies.py
```

## 🐛 故障排除

### 常见问题

1. **GDAL导入失败 (Windows)**
   - 确保使用Python 3.12
   - 检查wheels目录是否包含正确的GDAL wheel文件
   - 尝试重新运行安装脚本

2. **GDAL导入失败 (Ubuntu)**
   - 确保安装了libgdal-dev
   - 检查环境变量设置
   - 尝试重新安装：`sudo apt install --reinstall gdal-bin libgdal-dev`

3. **uv命令未找到**
   - Windows: 重启命令提示符或PowerShell
   - Ubuntu: 添加到PATH：`export PATH="$HOME/.cargo/bin:$PATH"`

4. **权限错误**
   - Windows: 以管理员身份运行命令提示符
   - Ubuntu: 确保有sudo权限

5. **网络连接问题**
   - 检查防火墙设置
   - 尝试使用代理：`uv sync --proxy http://proxy:port`

### 获取帮助

如果遇到其他问题：
1. 查看脚本输出的详细错误信息
2. 检查uv日志：`uv --verbose sync`
3. 验证系统环境和依赖版本
4. 参考项目文档或提交issue

## 📝 环境变量配置

### Ubuntu用户建议添加到 ~/.bashrc

```bash
# GDAL配置
export GDAL_CONFIG=/usr/bin/gdal-config
export CPLUS_INCLUDE_PATH=/usr/include/gdal
export C_INCLUDE_PATH=/usr/include/gdal

# uv路径
export PATH="$HOME/.cargo/bin:$PATH"
```

添加后运行：`source ~/.bashrc`
