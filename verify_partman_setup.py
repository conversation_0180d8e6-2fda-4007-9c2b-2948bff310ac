#!/usr/bin/env python3
# coding: utf-8
"""
验证 pg_partman 分区管理设置脚本
检查所有分区表的 partman 配置是否正确
"""

from sqlalchemy import create_engine, text
from src.config import PG_URL

def verify_partman_extension():
    """
    验证 pg_partman 扩展状态
    """
    engine = create_engine(PG_URL)
    
    try:
        with engine.begin() as conn:
            print("检查 pg_partman 扩展...")
            
            # 检查扩展
            ext_result = conn.execute(text("""
                SELECT extname, extversion 
                FROM pg_extension 
                WHERE extname IN ('pg_partman', 'pg_cron')
                ORDER BY extname
            """)).fetchall()
            
            if ext_result:
                print("✓ 已安装的扩展:")
                for row in ext_result:
                    print(f"  {row[0]} v{row[1]}")
            else:
                print("❌ 未找到相关扩展")
                return False
            
            # 检查 partman schema
            schema_result = conn.execute(text("""
                SELECT schema_name 
                FROM information_schema.schemata 
                WHERE schema_name = 'partman'
            """)).fetchall()
            
            if schema_result:
                print("✓ partman schema 存在")
            else:
                print("❌ partman schema 不存在")
                return False
                
            return True
            
    except Exception as e:
        print(f"检查扩展时发生错误: {e}")
        return False
    finally:
        engine.dispose()

def verify_partition_configs():
    """
    验证分区配置
    """
    engine = create_engine(PG_URL)
    
    # 期望的配置
    expected_configs = {
        # 核心数据表 - 每日分区
        'public.weather_cell_6m': {
            'control': 'pre_time',
            'interval': '1 day',
            'premake': 7,
            'retention': '90 days'
        },
        'public.weather_cell_1h': {
            'control': 'pre_time',
            'interval': '1 day',
            'premake': 7,
            'retention': '90 days'
        },
        'public.forecast_precipitation_6min_line': {
            'control': 'pre_time',
            'interval': '1 day',
            'premake': 7,
            'retention': '90 days'
        },
        'public.forecast_precipitation_6min_polygon': {
            'control': 'pre_time',
            'interval': '1 day',
            'premake': 7,
            'retention': '90 days'
        },
        'public.forecast_precipitation_6min_relation': {
            'control': 'pre_time',
            'interval': '1 day',
            'premake': 7,
            'retention': '90 days'
        },
        'public.forecast_precipitation_hourly_line': {
            'control': 'pre_time',
            'interval': '1 day',
            'premake': 7,
            'retention': '90 days'
        },
        'public.forecast_precipitation_hourly_polygon': {
            'control': 'pre_time',
            'interval': '1 day',
            'premake': 7,
            'retention': '90 days'
        },
        'public.forecast_precipitation_hourly_relation': {
            'control': 'pre_time',
            'interval': '1 day',
            'premake': 7,
            'retention': '90 days'
        },
        'public.forecast_precipitation_summary_line': {
            'control': 'pre_time',
            'interval': '1 day',
            'premake': 7,
            'retention': '90 days'
        },
        'public.forecast_precipitation_summary_polygon': {
            'control': 'pre_time',
            'interval': '1 day',
            'premake': 7,
            'retention': '90 days'
        },
        'public.forecast_precipitation_summary_relation': {
            'control': 'pre_time',
            'interval': '1 day',
            'premake': 7,
            'retention': '90 days'
        },
        # 预警表 - 每月分区
        'public.weather_alarm': {
            'control': 'publish_time',
            'interval': '1 mon',
            'premake': 4,
            'retention': '3 years'
        }
    }
    
    try:
        with engine.begin() as conn:
            print("\n检查分区配置...")
            
            # 获取实际配置
            actual_configs = conn.execute(text("""
                SELECT 
                    parent_table,
                    control,
                    partition_type,
                    partition_interval,
                    premake,
                    retention,
                    retention_keep_table
                FROM partman.part_config
                ORDER BY parent_table
            """)).fetchall()
            
            if not actual_configs:
                print("❌ 没有找到任何分区配置")
                return False
            
            print(f"✓ 找到 {len(actual_configs)} 个分区配置")
            
            # 验证每个配置
            configured_tables = set()
            for row in actual_configs:
                parent_table = row[0]
                control = row[1]
                partition_type = row[2]
                partition_interval = row[3]
                premake = row[4]
                retention = row[5]
                retention_keep_table = row[6]
                
                configured_tables.add(parent_table)
                
                print(f"\n  表: {parent_table}")
                print(f"    分区键: {control}")
                print(f"    分区类型: {partition_type}")
                print(f"    分区间隔: {partition_interval}")
                print(f"    预创建: {premake}")
                print(f"    保留策略: {retention}")
                print(f"    保留表: {retention_keep_table}")
                
                # 检查是否符合期望
                if parent_table in expected_configs:
                    expected = expected_configs[parent_table]
                    
                    checks = []
                    checks.append(('分区键', control == expected['control']))
                    checks.append(('分区间隔', partition_interval == expected['interval']))
                    checks.append(('预创建数量', premake == expected['premake']))
                    checks.append(('保留策略', retention == expected['retention']))
                    checks.append(('分区类型', partition_type == 'native'))
                    checks.append(('保留表设置', retention_keep_table == False))
                    
                    all_correct = True
                    for check_name, is_correct in checks:
                        if is_correct:
                            print(f"      ✓ {check_name}")
                        else:
                            print(f"      ❌ {check_name}")
                            all_correct = False
                    
                    if all_correct:
                        print(f"      ✓ {parent_table} 配置正确")
                    else:
                        print(f"      ⚠️ {parent_table} 配置有问题")
                else:
                    print(f"      ⚠️ {parent_table} 不在期望配置列表中")
            
            # 检查是否有遗漏的表
            expected_tables = set(expected_configs.keys())
            missing_tables = expected_tables - configured_tables
            
            if missing_tables:
                print(f"\n❌ 以下表缺少分区配置:")
                for table in missing_tables:
                    print(f"  {table}")
            else:
                print(f"\n✓ 所有期望的表都已配置分区管理")
            
            return len(missing_tables) == 0
            
    except Exception as e:
        print(f"检查分区配置时发生错误: {e}")
        return False
    finally:
        engine.dispose()

def verify_maintenance_job():
    """
    验证维护任务
    """
    engine = create_engine(PG_URL)
    
    try:
        with engine.begin() as conn:
            print("\n检查维护任务...")
            
            # 检查 pg_cron 是否存在
            cron_exists = conn.execute(text("""
                SELECT EXISTS (
                    SELECT 1 FROM pg_extension WHERE extname = 'pg_cron'
                )
            """)).scalar()
            
            if cron_exists:
                print("✓ pg_cron 扩展已安装")
                
                # 检查维护任务
                jobs = conn.execute(text("""
                    SELECT jobid, jobname, schedule, command, active
                    FROM cron.job
                    WHERE jobname = 'partman-maintenance'
                """)).fetchall()
                
                if jobs:
                    for job in jobs:
                        print(f"✓ 找到维护任务:")
                        print(f"  任务ID: {job[0]}")
                        print(f"  任务名: {job[1]}")
                        print(f"  调度: {job[2]}")
                        print(f"  命令: {job[3]}")
                        print(f"  状态: {'活跃' if job[4] else '非活跃'}")
                        
                        if job[2] == '0 2 * * *' and 'partman.run_maintenance_proc' in job[3]:
                            print("  ✓ 维护任务配置正确")
                        else:
                            print("  ⚠️ 维护任务配置可能有问题")
                else:
                    print("❌ 没有找到 partman-maintenance 任务")
                    print("建议手动创建:")
                    print("  SELECT cron.schedule('partman-maintenance', '0 2 * * *', 'CALL partman.run_maintenance_proc()');")
            else:
                print("⚠️ pg_cron 扩展未安装")
                print("建议安装 pg_cron 或设置系统 cron 任务")
                
    except Exception as e:
        print(f"检查维护任务时发生错误: {e}")
    finally:
        engine.dispose()

def verify_partitions():
    """
    验证已创建的分区
    """
    engine = create_engine(PG_URL)
    
    try:
        with engine.begin() as conn:
            print("\n检查已创建的分区...")
            
            # 查找子分区
            partitions = conn.execute(text("""
                SELECT 
                    schemaname,
                    tablename,
                    pg_size_pretty(pg_total_relation_size(schemaname||'.'||tablename)) as size
                FROM pg_tables 
                WHERE tablename ~ '_p\d{8}$'
                   OR tablename ~ '_p\d{6}$'
                ORDER BY schemaname, tablename
            """)).fetchall()
            
            if partitions:
                print(f"✓ 找到 {len(partitions)} 个子分区:")
                for partition in partitions:
                    print(f"  {partition[0]}.{partition[1]} ({partition[2]})")
            else:
                print("ℹ️ 暂无子分区")
                print("  分区将在首次数据插入时自动创建")
                print("  或运行 'CALL partman.run_maintenance_proc()' 立即创建")
                
    except Exception as e:
        print(f"检查分区时发生错误: {e}")
    finally:
        engine.dispose()

def main():
    """
    主函数
    """
    print("pg_partman 分区管理验证脚本")
    print("=" * 50)
    
    try:
        # 1. 验证扩展
        if not verify_partman_extension():
            print("\n❌ pg_partman 扩展验证失败")
            return
        
        # 2. 验证分区配置
        config_ok = verify_partition_configs()
        
        # 3. 验证维护任务
        verify_maintenance_job()
        
        # 4. 验证分区
        verify_partitions()
        
        print("\n" + "=" * 50)
        if config_ok:
            print("✓ 分区管理验证完成！所有配置正确")
        else:
            print("⚠️ 分区管理验证完成，但发现一些问题")
        
        print("\n建议的下一步操作:")
        print("1. 运行 'CALL partman.run_maintenance_proc()' 创建初始分区")
        print("2. 测试数据插入以验证分区自动创建")
        print("3. 监控日志确保维护任务正常运行")
        
    except Exception as e:
        print(f"\n❌ 验证过程中发生错误: {e}")
        raise

if __name__ == '__main__':
    main()
