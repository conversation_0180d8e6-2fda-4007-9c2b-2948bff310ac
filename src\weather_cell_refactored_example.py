#!/usr/bin/env python3
# coding: utf-8
"""
重构示例：展示如何使用公共模块简化天气数据处理脚本

这是一个示例文件，展示了如何重构原始的weather_cell_*_async_hybrid.py文件
使用weather_common.py和weather_download.py中的公共函数
"""

import asyncio
import logging
import numpy as np
import pandas as pd
from typing import List, Dict, Any
from datetime import datetime

# 导入公共模块
from weather_common import (
    # 格网编码/解码函数
    encode_cell, decode_cell, get_netcdf_indices_from_coords,
    
    # 文件处理函数
    get_nc_file_path_single, get_nc_files_path_multiple, load_valid_cells,
    
    # 数据库管理
    AsyncDatabaseManager,
    
    # 常量
    NC_NROWS, NC_NCOLS, MAX_CONCURRENT_DB_OPERATIONS,
    
    # 日志设置
    setup_logging
)

# 导入NetCDF处理函数
from weather_download import (
    read_netcdf_data, extract_netcdf_coordinates, 
    get_netcdf_value_at_coords, validate_netcdf_file
)

# 导入配置
from config import PG_URL, PROCESSING_CONFIG

# 设置日志
setup_logging()
logger = logging.getLogger(__name__)


class WeatherDataProcessor:
    """天气数据处理器基类"""
    
    def __init__(self, data_interval: str = "1h"):
        """
        初始化处理器
        
        Args:
            data_interval: 数据间隔，"1h" 或 "6m"
        """
        self.data_interval = data_interval
        self.valid_cells = None
        self.db_manager = None
        
    async def initialize(self):
        """初始化处理器"""
        logger.info(f"初始化 {self.data_interval} 天气数据处理器")
        
        # 加载有效格网
        self.valid_cells = load_valid_cells()
        logger.info(f"加载了 {len(self.valid_cells)} 个有效格网单元")
        
        # 初始化数据库管理器
        self.db_manager = AsyncDatabaseManager(PG_URL, MAX_CONCURRENT_DB_OPERATIONS)
        await self.db_manager.connect()
        
    async def cleanup(self):
        """清理资源"""
        if self.db_manager:
            await self.db_manager.close()
    
    async def process_single_file(self, file_path: str, var_name: str) -> bool:
        """
        处理单个NetCDF文件（6分钟数据）
        
        Args:
            file_path: NetCDF文件路径
            var_name: 变量名
            
        Returns:
            bool: 处理是否成功
        """
        try:
            logger.info(f"开始处理文件: {file_path}")
            
            # 验证文件
            if not validate_netcdf_file(file_path, [var_name]):
                logger.error(f"文件验证失败: {file_path}")
                return False
            
            # 读取NetCDF数据
            ds, metadata = read_netcdf_data(file_path, var_name)
            logger.info(f"文件元数据: {metadata}")
            
            # 提取坐标信息
            lon_array, lat_array, lon_min, lat_min = extract_netcdf_coordinates(ds)
            
            # 处理数据
            processed_data = []
            
            for cell_id in self.valid_cells:
                # 解码格网坐标
                lon, lat = decode_cell(cell_id)
                
                # 获取数据值
                value = get_netcdf_value_at_coords(
                    ds, var_name, lon, lat, lon_array, lat_array, lon_min, lat_min
                )
                
                if value is not None:
                    processed_data.append({
                        'cell_id': cell_id,
                        'value': value,
                        'timestamp': datetime.now(),
                        'data_type': var_name
                    })
            
            # 批量写入数据库
            if processed_data:
                table_name = f"weather_data_{self.data_interval}"
                success = await self.db_manager.execute_batch_upsert(
                    table_name, processed_data, ['cell_id', 'timestamp']
                )
                
                if success:
                    logger.info(f"成功处理 {len(processed_data)} 条数据")
                    return True
                else:
                    logger.error("数据库写入失败")
                    return False
            else:
                logger.warning("没有有效数据需要处理")
                return True
                
        except Exception as e:
            logger.error(f"处理文件失败 {file_path}: {e}")
            return False
        finally:
            # 关闭数据集
            if 'ds' in locals():
                ds.close()
    
    async def process_multiple_files(self, file_dict: Dict[str, str]) -> bool:
        """
        处理多个NetCDF文件（1小时数据）
        
        Args:
            file_dict: 文件字典，格式为 {var_name: file_path}
            
        Returns:
            bool: 处理是否成功
        """
        try:
            logger.info(f"开始处理多个文件: {list(file_dict.keys())}")
            
            # 验证所有文件
            for var_name, file_path in file_dict.items():
                if not validate_netcdf_file(file_path, [var_name]):
                    logger.error(f"文件验证失败: {file_path}")
                    return False
            
            # 读取所有数据集
            datasets = {}
            coordinates = {}
            
            for var_name, file_path in file_dict.items():
                ds, metadata = read_netcdf_data(file_path, var_name)
                datasets[var_name] = ds
                
                # 提取坐标（假设所有文件使用相同的坐标系统）
                if var_name not in coordinates:
                    lon_array, lat_array, lon_min, lat_min = extract_netcdf_coordinates(ds)
                    coordinates[var_name] = (lon_array, lat_array, lon_min, lat_min)
            
            # 处理数据
            processed_data = []
            
            for cell_id in self.valid_cells:
                # 解码格网坐标
                lon, lat = decode_cell(cell_id)
                
                # 为每个变量获取数据值
                cell_data = {
                    'cell_id': cell_id,
                    'timestamp': datetime.now()
                }
                
                has_valid_data = False
                
                for var_name, ds in datasets.items():
                    lon_array, lat_array, lon_min, lat_min = coordinates[var_name]
                    value = get_netcdf_value_at_coords(
                        ds, var_name, lon, lat, lon_array, lat_array, lon_min, lat_min
                    )
                    
                    if value is not None:
                        cell_data[var_name.lower()] = value
                        has_valid_data = True
                
                if has_valid_data:
                    processed_data.append(cell_data)
            
            # 批量写入数据库
            if processed_data:
                table_name = f"weather_data_{self.data_interval}"
                success = await self.db_manager.execute_batch_upsert(
                    table_name, processed_data, ['cell_id', 'timestamp']
                )
                
                if success:
                    logger.info(f"成功处理 {len(processed_data)} 条数据")
                    return True
                else:
                    logger.error("数据库写入失败")
                    return False
            else:
                logger.warning("没有有效数据需要处理")
                return True
                
        except Exception as e:
            logger.error(f"处理多个文件失败: {e}")
            return False
        finally:
            # 关闭所有数据集
            for ds in datasets.values():
                ds.close()


async def process_6m_weather_data():
    """处理6分钟天气数据的示例函数"""
    processor = WeatherDataProcessor("6m")
    
    try:
        await processor.initialize()
        
        # 获取NC文件路径
        nc_file_path, is_downloaded, data_type = get_nc_file_path_single("gz_mpfv3")
        
        # 处理文件
        success = await processor.process_single_file(nc_file_path, "WEATHER")
        
        if success:
            logger.info("6分钟天气数据处理完成")
        else:
            logger.error("6分钟天气数据处理失败")
            
    finally:
        await processor.cleanup()


async def process_1h_weather_data():
    """处理1小时天气数据的示例函数"""
    processor = WeatherDataProcessor("1h")
    
    try:
        await processor.initialize()
        
        # 获取多个NC文件路径
        data_types = ['PRE', 'TEM', 'WEATHER', 'VIS']
        nc_files_dict, downloaded_info, _ = get_nc_files_path_multiple(data_types)
        
        if nc_files_dict:
            # 处理文件
            success = await processor.process_multiple_files(nc_files_dict)
            
            if success:
                logger.info("1小时天气数据处理完成")
            else:
                logger.error("1小时天气数据处理失败")
        else:
            logger.error("未找到有效的NC文件")
            
    finally:
        await processor.cleanup()


async def main():
    """主函数"""
    logger.info("开始天气数据处理")
    
    # 可以选择处理6分钟或1小时数据
    choice = input("选择处理类型 (1: 6分钟数据, 2: 1小时数据): ").strip()
    
    if choice == "1":
        await process_6m_weather_data()
    elif choice == "2":
        await process_1h_weather_data()
    else:
        logger.error("无效选择")


if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        logger.info("处理被用户中断")
    except Exception as e:
        logger.error(f"处理过程中发生错误: {e}")
        import traceback
        traceback.print_exc()
