#!/usr/bin/env python3
# coding: utf-8
"""
设置 pg_partman 分区管理脚本
为所有分区表配置自动分区管理和维护策略
"""

from sqlalchemy import create_engine, text
from src.config import PG_URL

def check_partman_extension():
    """
    检查 pg_partman 扩展是否已安装
    """
    engine = create_engine(PG_URL)
    
    try:
        with engine.begin() as conn:
            # 检查扩展是否存在
            result = conn.execute(text("""
                SELECT EXISTS (
                    SELECT 1 FROM pg_extension WHERE extname = 'pg_partman'
                )
            """)).scalar()
            
            if result:
                print("✓ pg_partman 扩展已安装")
                
                # 检查 partman schema 是否存在
                schema_result = conn.execute(text("""
                    SELECT EXISTS (
                        SELECT 1 FROM information_schema.schemata WHERE schema_name = 'partman'
                    )
                """)).scalar()
                
                if schema_result:
                    print("✓ partman schema 已存在")
                    return True
                else:
                    print("⚠️ partman schema 不存在")
                    return False
            else:
                print("❌ pg_partman 扩展未安装")
                print("请先安装 pg_partman 扩展:")
                print("  CREATE EXTENSION pg_partman;")
                return False
                
    except Exception as e:
        print(f"检查 pg_partman 扩展时发生错误: {e}")
        return False
    finally:
        engine.dispose()

def setup_partition_management():
    """
    为所有分区表设置 partman 管理
    """
    engine = create_engine(PG_URL)
    
    # 要求 A: 核心数据和派生结果表 (共11个)
    # 策略: 每日分区, 保留90天, 预创建7天
    core_tables = [
        'public.weather_cell_6m',
        'public.weather_cell_1h',
        'public.forecast_precipitation_6min_line',
        'public.forecast_precipitation_6min_polygon',
        'public.forecast_precipitation_6min_relation',
        'public.forecast_precipitation_hourly_line',
        'public.forecast_precipitation_hourly_polygon',
        'public.forecast_precipitation_hourly_relation',
        'public.forecast_precipitation_summary_line',
        'public.forecast_precipitation_summary_polygon',
        'public.forecast_precipitation_summary_relation'
    ]
    
    # 要求 B: weather_alarm 表
    # 策略: 每月分区, 保留3年, 预创建4个月
    alarm_table = 'public.weather_alarm'
    
    try:
        with engine.begin() as conn:
            print("开始配置分区管理...")
            
            # 配置核心数据表 (要求A)
            print("\n配置核心数据表 (每日分区, 保留90天):")
            for table_name in core_tables:
                print(f"  配置表: {table_name}")
                try:
                    sql = """
                    SELECT partman.create_parent(
                        p_parent_table := :table_name,
                        p_control := 'pre_time',
                        p_type := 'native',
                        p_interval := '1 day',
                        p_premake := 7,
                        p_retention := '90 days',
                        p_retention_keep_table := false
                    )
                    """
                    result = conn.execute(text(sql), {"table_name": table_name})
                    success = result.scalar()
                    if success:
                        print(f"    ✓ {table_name} 配置成功")
                    else:
                        print(f"    ⚠️ {table_name} 配置返回 false")
                except Exception as e:
                    print(f"    ❌ {table_name} 配置失败: {e}")
                    # 继续配置其他表
                    continue
            
            # 配置预警表 (要求B)
            print(f"\n配置预警表 (每月分区, 保留3年):")
            print(f"  配置表: {alarm_table}")
            try:
                sql = """
                SELECT partman.create_parent(
                    p_parent_table := :table_name,
                    p_control := 'publish_time',
                    p_type := 'native',
                    p_interval := '1 month',
                    p_premake := 4,
                    p_retention := '3 years',
                    p_retention_keep_table := false
                )
                """
                result = conn.execute(text(sql), {"table_name": alarm_table})
                success = result.scalar()
                if success:
                    print(f"    ✓ {alarm_table} 配置成功")
                else:
                    print(f"    ⚠️ {alarm_table} 配置返回 false")
            except Exception as e:
                print(f"    ❌ {alarm_table} 配置失败: {e}")
            
            print("\n✓ 分区管理配置完成")
            
    except Exception as e:
        print(f"配置分区管理时发生错误: {e}")
        raise
    finally:
        engine.dispose()

def setup_maintenance_job():
    """
    设置自动维护任务
    """
    engine = create_engine(PG_URL)
    
    try:
        with engine.begin() as conn:
            print("\n设置自动维护任务...")
            
            # 检查 pg_cron 扩展是否存在
            cron_exists = conn.execute(text("""
                SELECT EXISTS (
                    SELECT 1 FROM pg_extension WHERE extname = 'pg_cron'
                )
            """)).scalar()
            
            if cron_exists:
                print("✓ pg_cron 扩展已安装，设置定时任务...")
                
                # 删除可能存在的旧任务
                try:
                    conn.execute(text("SELECT cron.unschedule('partman-maintenance')"))
                    print("  已删除旧的维护任务")
                except:
                    pass  # 任务可能不存在
                
                # 创建新的维护任务 - 每天凌晨2点运行
                result = conn.execute(text("""
                    SELECT cron.schedule(
                        'partman-maintenance', 
                        '0 2 * * *', 
                        'CALL partman.run_maintenance_proc()'
                    )
                """)).scalar()
                
                if result:
                    print(f"  ✓ 定时维护任务已创建 (任务ID: {result})")
                    print("    任务将在每天凌晨2点自动运行")
                else:
                    print("  ⚠️ 定时任务创建失败")
                    
            else:
                print("⚠️ pg_cron 扩展未安装")
                print("建议安装 pg_cron 扩展以启用自动维护:")
                print("  CREATE EXTENSION pg_cron;")
                print("\n或者手动设置系统 cron 任务:")
                print("  0 2 * * * psql -d your_database -c 'CALL partman.run_maintenance_proc()'")
                
    except Exception as e:
        print(f"设置维护任务时发生错误: {e}")
        # 不抛出异常，因为这不是致命错误
    finally:
        engine.dispose()

def verify_partition_setup():
    """
    验证分区设置
    """
    engine = create_engine(PG_URL)
    
    try:
        with engine.begin() as conn:
            print("\n验证分区设置...")
            
            # 检查 partman 配置
            result = conn.execute(text("""
                SELECT 
                    parent_table,
                    control,
                    partition_type,
                    partition_interval,
                    premake,
                    retention,
                    retention_keep_table
                FROM partman.part_config
                ORDER BY parent_table
            """)).fetchall()
            
            if result:
                print("✓ 发现以下分区配置:")
                for row in result:
                    print(f"  {row[0]}:")
                    print(f"    分区键: {row[1]}")
                    print(f"    分区类型: {row[2]}")
                    print(f"    分区间隔: {row[3]}")
                    print(f"    预创建: {row[4]} 个分区")
                    print(f"    保留策略: {row[5]}")
                    print(f"    保留表: {row[6]}")
                    print()
            else:
                print("⚠️ 没有发现分区配置")
            
            # 检查已创建的分区
            partition_result = conn.execute(text("""
                SELECT 
                    schemaname,
                    tablename,
                    pg_size_pretty(pg_total_relation_size(schemaname||'.'||tablename)) as size
                FROM pg_tables 
                WHERE tablename ~ '_p\d{8}$'
                   OR tablename ~ '_p\d{6}$'
                ORDER BY schemaname, tablename
            """)).fetchall()
            
            if partition_result:
                print("✓ 发现以下子分区:")
                for row in partition_result:
                    print(f"  {row[0]}.{row[1]} ({row[2]})")
            else:
                print("ℹ️ 暂无子分区（将在首次数据插入或维护运行时创建）")
                
    except Exception as e:
        print(f"验证过程中发生错误: {e}")
    finally:
        engine.dispose()

def main():
    """
    主函数
    """
    print("PostgreSQL 分区管理设置脚本")
    print("=" * 50)
    
    try:
        # 1. 检查 pg_partman 扩展
        if not check_partman_extension():
            return
        
        # 2. 设置分区管理
        setup_partition_management()
        
        # 3. 设置自动维护任务
        setup_maintenance_job()
        
        # 4. 验证设置
        verify_partition_setup()
        
        print("\n" + "=" * 50)
        print("✓ 分区管理设置完成！")
        print("\n重要提醒:")
        print("1. 分区将在首次数据插入时自动创建")
        print("2. 维护任务将每天自动运行，管理分区生命周期")
        print("3. 可以手动运行 'CALL partman.run_maintenance_proc()' 立即执行维护")
        print("4. 监控日志以确保分区管理正常工作")
        
    except Exception as e:
        print(f"\n❌ 设置过程中发生错误: {e}")
        raise

if __name__ == '__main__':
    main()
