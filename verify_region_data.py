#!/usr/bin/env python3
"""
验证数据库中的行政区划数据
"""

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

from sqlalchemy import create_engine, text
from config import get_postgres_url, ROUTE_TB, LUT_TB, LUT_LINE_TB

def verify_region_data():
    """验证数据库中的行政区划数据"""
    print("=== 验证数据库中的行政区划数据 ===")
    
    # 创建数据库连接
    engine = create_engine(get_postgres_url().replace("postgresql://", "postgresql+psycopg2://"))
    
    with engine.begin() as conn:
        # 1. 检查路线表中的行政区划字段
        print("\n1. 路线表 (weather_routes) 行政区划字段检查:")
        
        # 检查字段是否存在
        result = conn.execute(text(f"""
            SELECT column_name, data_type 
            FROM information_schema.columns 
            WHERE table_name = '{ROUTE_TB}' 
            AND column_name IN ('region_code', 'region_name')
            ORDER BY column_name
        """))
        
        columns = result.fetchall()
        if columns:
            print("   ✓ 行政区划字段存在:")
            for col in columns:
                print(f"     - {col.column_name}: {col.data_type}")
        else:
            print("   ✗ 未找到行政区划字段")
            return
        
        # 统计数据
        result = conn.execute(text(f"""
            SELECT 
                COUNT(*) as total_count,
                COUNT(region_code) as region_code_count,
                COUNT(region_name) as region_name_count,
                COUNT(CASE WHEN region_code IS NOT NULL AND region_name IS NOT NULL THEN 1 END) as both_count
            FROM {ROUTE_TB}
        """))
        
        stats = result.fetchone()
        print(f"   - 总记录数: {stats.total_count}")
        print(f"   - 有region_code的记录数: {stats.region_code_count}")
        print(f"   - 有region_name的记录数: {stats.region_name_count}")
        print(f"   - 两个字段都有的记录数: {stats.both_count}")
        
        # 显示一些示例数据
        result = conn.execute(text(f"""
            SELECT route_id, route_code, route_name, region_code, region_name
            FROM {ROUTE_TB}
            WHERE region_code IS NOT NULL AND region_name IS NOT NULL
            LIMIT 5
        """))
        
        examples = result.fetchall()
        if examples:
            print("   - 示例数据:")
            for row in examples:
                print(f"     路线{row.route_id}: {row.route_code} ({row.route_name}) - {row.region_code} ({row.region_name})")
        
        # 2. 检查LUT表中的行政区划字段
        print(f"\n2. LUT表 ({LUT_TB}) 行政区划字段检查:")
        
        result = conn.execute(text(f"""
            SELECT column_name, data_type 
            FROM information_schema.columns 
            WHERE table_name = '{LUT_TB}' 
            AND column_name IN ('region_code', 'region_name')
            ORDER BY column_name
        """))
        
        columns = result.fetchall()
        if columns:
            print("   ✓ 行政区划字段存在:")
            for col in columns:
                print(f"     - {col.column_name}: {col.data_type}")
        else:
            print("   ✗ 未找到行政区划字段")
        
        # 统计LUT表数据
        result = conn.execute(text(f"""
            SELECT 
                COUNT(*) as total_count,
                COUNT(region_code) as region_code_count,
                COUNT(region_name) as region_name_count
            FROM {LUT_TB}
        """))
        
        stats = result.fetchone()
        print(f"   - 总记录数: {stats.total_count}")
        print(f"   - 有region_code的记录数: {stats.region_code_count}")
        print(f"   - 有region_name的记录数: {stats.region_name_count}")
        
        # 3. 检查LUT_LINE表中的行政区划字段
        print(f"\n3. LUT_LINE表 ({LUT_LINE_TB}) 行政区划字段检查:")
        
        result = conn.execute(text(f"""
            SELECT column_name, data_type 
            FROM information_schema.columns 
            WHERE table_name = '{LUT_LINE_TB}' 
            AND column_name IN ('region_code', 'region_name')
            ORDER BY column_name
        """))
        
        columns = result.fetchall()
        if columns:
            print("   ✓ 行政区划字段存在:")
            for col in columns:
                print(f"     - {col.column_name}: {col.data_type}")
        else:
            print("   ✗ 未找到行政区划字段")
        
        # 统计LUT_LINE表数据
        result = conn.execute(text(f"""
            SELECT 
                COUNT(*) as total_count,
                COUNT(region_code) as region_code_count,
                COUNT(region_name) as region_name_count
            FROM {LUT_LINE_TB}
        """))
        
        stats = result.fetchone()
        print(f"   - 总记录数: {stats.total_count}")
        print(f"   - 有region_code的记录数: {stats.region_code_count}")
        print(f"   - 有region_name的记录数: {stats.region_name_count}")
        
        # 4. 验证行政区划代码的格式
        print(f"\n4. 行政区划代码格式验证:")
        
        result = conn.execute(text(f"""
            SELECT 
                LENGTH(region_code) as code_length,
                COUNT(*) as count
            FROM {ROUTE_TB}
            WHERE region_code IS NOT NULL
            GROUP BY LENGTH(region_code)
            ORDER BY code_length
        """))
        
        length_stats = result.fetchall()
        if length_stats:
            print("   - 行政区划代码长度分布:")
            for row in length_stats:
                print(f"     {row.code_length}位: {row.count}条记录")
        
        # 5. 显示一些具体的查询示例
        print(f"\n5. 查询示例验证:")
        
        # 按行政区划分组统计
        result = conn.execute(text(f"""
            SELECT region_code, region_name, COUNT(*) as route_count
            FROM {ROUTE_TB}
            WHERE region_code IS NOT NULL AND region_name IS NOT NULL
            GROUP BY region_code, region_name
            ORDER BY route_count DESC
            LIMIT 5
        """))
        
        region_stats = result.fetchall()
        if region_stats:
            print("   - 按行政区划分组的路线数量 (前5名):")
            for row in region_stats:
                print(f"     {row.region_code} ({row.region_name}): {row.route_count}条路线")

if __name__ == "__main__":
    verify_region_data()
    print("\n验证完成！")
