#!/usr/bin/env python3
# coding: utf-8
"""
测试下载失败场景下的调度器行为
模拟下载失败，验证最终步骤是否仍然执行
"""

import asyncio
import logging
import sys
from pathlib import Path
from unittest.mock import AsyncMock, patch, MagicMock

# 添加src目录到Python路径
src_path = Path(__file__).parent / "src"
sys.path.insert(0, str(src_path))

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

async def test_hourly_task_download_failure():
    """测试1小时任务下载失败的情况"""
    logger.info("=== 测试1小时任务下载失败场景 ===")
    
    try:
        from scheduler import WeatherTaskScheduler
        
        scheduler = WeatherTaskScheduler()
        
        # 模拟下载失败的情况
        with patch('scheduler.check_and_get_fresh_data') as mock_check_data, \
             patch('scheduler.download_weather_data_with_api_result') as mock_download, \
             patch('scheduler.mark_task_started') as mock_mark_started, \
             patch('scheduler.mark_task_completed') as mock_mark_completed:
            
            # 设置模拟返回值 - API检查成功但下载失败
            mock_check_data.return_value = {
                'uptime': '202507211500',
                'download_url': 'http://example.com/test.nc'
            }
            mock_download.return_value = None  # 模拟下载失败
            
            # 执行任务
            result = await scheduler._execute_hourly_task("PRE")
            
            # 验证结果
            assert "处理完成（下载失败）" in result
            logger.info(f"✓ 1小时任务下载失败场景测试通过，结果: {result}")
            
            # 验证mark_task_started被调用了（因为API检查成功）
            mock_mark_started.assert_called_once()
            
            # 验证mark_task_completed被调用了（因为有uptime）
            mock_mark_completed.assert_called_once()
            
    except Exception as e:
        logger.error(f"1小时任务下载失败测试失败: {e}")
        raise

async def test_six_minute_task_download_failure():
    """测试6分钟任务下载失败的情况"""
    logger.info("=== 测试6分钟任务下载失败场景 ===")
    
    try:
        from scheduler import WeatherTaskScheduler
        
        scheduler = WeatherTaskScheduler()
        
        # 模拟下载失败的情况
        with patch('scheduler.check_and_get_fresh_data') as mock_check_data, \
             patch('scheduler.download_weather_data_with_api_result') as mock_download, \
             patch('scheduler.mark_task_started') as mock_mark_started, \
             patch('scheduler.mark_task_completed') as mock_mark_completed:
            
            # 设置模拟返回值 - API检查成功但下载失败
            mock_check_data.return_value = {
                'uptime': '202507211500',
                'download_url': 'http://example.com/test.nc'
            }
            mock_download.return_value = None  # 模拟下载失败
            
            # 执行任务
            result = await scheduler._execute_six_minute_task()
            
            # 验证结果
            assert "处理完成（下载失败）" in result
            logger.info(f"✓ 6分钟任务下载失败场景测试通过，结果: {result}")
            
            # 验证mark_task_started被调用了（因为API检查成功）
            mock_mark_started.assert_called_once()
            
            # 验证mark_task_completed被调用了（因为有uptime）
            mock_mark_completed.assert_called_once()
            
    except Exception as e:
        logger.error(f"6分钟任务下载失败测试失败: {e}")
        raise

async def test_api_failure_scenario():
    """测试API检查失败的情况"""
    logger.info("=== 测试API检查失败场景 ===")
    
    try:
        from scheduler import WeatherTaskScheduler
        
        scheduler = WeatherTaskScheduler()
        
        # 模拟API检查失败的情况
        with patch('scheduler.check_and_get_fresh_data') as mock_check_data, \
             patch('scheduler.mark_task_started') as mock_mark_started, \
             patch('scheduler.mark_task_completed') as mock_mark_completed:
            
            # 设置模拟返回值 - API检查失败
            mock_check_data.return_value = None
            
            # 执行任务应该抛出异常
            try:
                result = await scheduler._execute_hourly_task("PRE")
                # 如果没有抛出异常，说明有问题
                assert False, "应该抛出异常但没有"
            except Exception as e:
                logger.info(f"✓ API失败场景正确抛出异常: {e}")
            
            # 验证mark_task_started没有被调用（因为API检查失败）
            mock_mark_started.assert_not_called()
            
            # 验证mark_task_completed没有被调用（因为没有uptime）
            mock_mark_completed.assert_not_called()
            
    except Exception as e:
        logger.error(f"API失败场景测试失败: {e}")
        raise

async def main():
    """主测试函数"""
    logger.info("开始测试下载失败场景...")
    
    try:
        # 测试6分钟任务下载失败（不需要数据库连接）
        await test_six_minute_task_download_failure()
        
        # 测试1小时任务下载失败（需要数据库连接）
        try:
            await test_hourly_task_download_failure()
        except Exception as e:
            logger.warning(f"1小时任务下载失败测试失败（可能是数据库连接问题）: {e}")
        
        # 测试API失败场景
        await test_api_failure_scenario()
        
        logger.info("✓ 所有下载失败场景测试完成")
        
    except Exception as e:
        logger.error(f"测试过程中发生错误: {e}")
        raise

if __name__ == '__main__':
    asyncio.run(main())
