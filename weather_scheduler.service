[Unit]
Description=Weather Scheduler Service (Production Grade)
# 确保在网络可用并且数据库（如果需要）启动后才启动
After=network-online.target
Wants=network-online.target

[Service]
# --- 执行用户 ---
User=root
Group=root

# --- 核心命令 ---
WorkingDirectory=/data/script/weather_script
# -u 参数让 Python 不使用输出缓存，日志可以立即写入 journal
ExecStart=/data/script/weather_script/.venv/bin/python -u /data/script/weather_script/run_weather_scheduler.py

# --- 配置分离 ---
# 从 .env 文件加载环境变量，而不是硬编码在代码里
EnvironmentFile=/data/script/weather_script/.env

# --- 重启策略 ---
# 只在失败时重启（非0退出码），而不是任何情况都重启
Restart=on-failure
# 重启前等待10秒，防止因程序bug导致的高频率无限重启拖垮系统
RestartSec=10s

# --- 日志 ---
StandardOutput=journal
StandardError=journal
# 在系统日志中为这个服务的日志加上一个固定的标签，方便筛选
SyslogIdentifier=weather_scheduler

[Install]
WantedBy=multi-user.target