# 天气数据备份目录结构升级

## 概述

已成功将天气数据备份系统从简单的平面目录结构升级为按年/月/日划分的层次化目录结构，大大改善了备份文件的组织和管理。

## 主要改进

### 1. 新的目录结构

**升级前（旧格式）：**
```
backup/
├── MPF_20250713145400.nc
├── MPF_20250713150600.nc
├── MPF_20250713151200.nc
└── ... (所有文件混在一起)
```

**升级后（新格式）：**
```
backup/
├── 2025/                    # 年份目录
│   ├── 07/                  # 月份目录
│   │   ├── 13/              # 日期目录
│   │   │   ├── MPF_20250713145400.nc
│   │   │   ├── MPF_20250713150600.nc
│   │   │   └── MPF_20250713151200.nc
│   │   ├── 14/
│   │   │   └── MPF_20250714000000.nc
│   │   └── 15/
│   │       └── MPF_20250715120000.nc
│   └── 08/
│       └── 01/
│           └── MPF_20250801080000.nc
└── 2024/
    └── 12/
        └── 31/
            └── old_backup_file.nc
```

### 2. 核心功能改进

#### 2.1 智能备份路径生成
- 自动按当前日期创建年/月/日目录结构
- 重名文件自动添加时间戳后缀（HHMMSS格式）
- 确保目录权限正确设置

#### 2.2 增强的清理功能
- 支持按日期目录批量清理
- 自动删除空的日期/月份/年份目录
- 兼容处理旧格式和新格式的备份文件
- 精确的日期计算，避免误删当天文件

#### 2.3 向后兼容性
- 新备份使用新格式，旧备份保持原位置
- 清理功能同时处理新旧两种格式
- 不需要强制迁移现有备份文件

## 技术实现

### 修改的文件

1. **src/weather_download.py**
   - `move_to_backup()` 方法：实现按日期创建目录结构
   - `cleanup_old_backups()` 方法：支持层次化目录的清理

### 新增的工具

1. **test_backup_structure.py**
   - 功能测试脚本，验证新备份结构的正确性

2. **backup_demo.py**
   - 演示脚本，展示新功能和使用方法

3. **migrate_backup_structure.py**
   - 可选的迁移工具，将现有备份文件整理到新结构中

## 使用方法

### 基本使用（自动生效）

新的备份结构已经自动集成到现有系统中，无需修改任何代码：

```python
from weather_download import WeatherDownloader

# 创建下载器实例
downloader = WeatherDownloader('gz_mpfv3')

# 备份文件（自动使用新的目录结构）
success = downloader.move_to_backup('/path/to/weather_data.nc')
```

### 批量备份

```python
# 备份NC文件目录中的所有现有文件
success = downloader.backup_existing_files()
```

### 清理旧备份

```python
# 清理30天前的备份文件（支持新旧两种格式）
downloader.cleanup_old_backups(keep_days=30)
```

### 便捷函数

```python
from weather_download import backup_weather_file

# 直接备份文件
success = backup_weather_file('/path/to/file.nc', 'gz_mpfv3')
```

## 迁移现有备份（可选）

如果希望将现有的543个备份文件整理到新的目录结构中：

### 1. 试运行（推荐先执行）
```bash
uv run python migrate_backup_structure.py
```

### 2. 执行实际迁移
```bash
uv run python migrate_backup_structure.py --execute
```

### 3. 查看迁移预览
```bash
uv run python migrate_backup_structure.py --preview
```

## 当前状态

### 备份文件统计
- **gz_mpfv3 (6分钟降水)**: 195个文件
- **gz_didiforecast1hTEM (1小时温度)**: 51个文件  
- **gz_didiforecast1hPRE (1小时降水)**: 129个文件
- **gz_didiforecast1hWEATHER (1小时天气现象)**: 99个文件
- **gz_didiforecast1hVIS (1小时能见度)**: 69个文件
- **总计**: 543个备份文件

所有文件目前使用旧格式存储，新的备份将自动使用新格式。

## 优势总结

1. **更好的组织性**: 按日期自动分类，便于查找特定日期的备份
2. **性能提升**: 避免单个目录文件过多导致的性能问题
3. **智能清理**: 支持按日期批量清理，更精确的管理
4. **自动处理**: 重名文件自动添加时间戳，避免冲突
5. **向后兼容**: 保持与旧格式的完全兼容性
6. **空间优化**: 自动清理空目录，保持目录结构整洁

## 配置选项

在 `config.yml` 中的相关配置保持不变：

```yaml
weather_download:
  backup_keep_days: 30        # 备份文件保留天数
  auto_cleanup_backups: true  # 是否自动清理旧备份
  enable_backup: true         # 是否启用备份功能
```

## 测试验证

所有功能已通过测试验证：
- ✅ 新目录结构创建正确
- ✅ 重名文件处理正确
- ✅ 清理功能工作正常
- ✅ 向后兼容性良好
- ✅ 迁移工具功能完整

## 总结

这次升级显著改善了天气数据备份系统的可维护性和可扩展性，为长期运行的生产环境提供了更好的文件管理方案。新系统在保持完全向后兼容的同时，为未来的备份文件提供了更加合理和高效的组织结构。
