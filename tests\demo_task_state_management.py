#!/usr/bin/env python3
# coding: utf-8
"""
任务状态管理演示脚本
展示任务状态管理器的功能，包括uptime跟踪、防重复处理等
"""

import asyncio
import sys
from pathlib import Path
import logging
from datetime import datetime

# 添加src目录到Python路径
src_path = Path(__file__).parent / "src"
sys.path.insert(0, str(src_path))

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

logger = logging.getLogger(__name__)


def demo_task_state_basics():
    """演示任务状态管理基础功能"""
    logger.info("=== 演示任务状态管理基础功能 ===")
    
    try:
        from task_state_manager import (
            task_state_manager, should_process_task, 
            mark_task_completed, is_first_run, get_task_states_summary
        )
        
        # 重置所有状态
        task_state_manager.reset_all_states()
        
        # 1. 显示初始状态
        logger.info("📊 初始状态:")
        stats = task_state_manager.get_statistics()
        logger.info(f"  总任务数: {stats['total_tasks']}")
        logger.info(f"  首次运行任务: {stats['first_run_tasks']}")
        logger.info(f"  已完成任务: {stats['completed_tasks']}")
        
        # 2. 演示首次运行检查
        logger.info("\n🔍 检查首次运行状态:")
        for task_type, data_type in [("1h", "PRE"), ("1h", "TEM"), ("6m", "gz_mpfv3")]:
            first_run = is_first_run(task_type, data_type)
            logger.info(f"  {task_type}_{data_type}: {'首次运行' if first_run else '已运行过'}")
        
        # 3. 演示处理判断
        logger.info("\n⚡ 演示处理判断:")
        test_uptime = "202507131304"
        
        for task_type, data_type in [("1h", "PRE"), ("6m", "gz_mpfv3")]:
            should_process = should_process_task(task_type, data_type, test_uptime)
            logger.info(f"  {task_type}_{data_type} uptime {test_uptime}: {'需要处理' if should_process else '跳过'}")
        
        # 4. 模拟任务执行
        logger.info("\n🚀 模拟任务执行:")
        mark_task_completed("1h", "PRE", test_uptime)
        mark_task_completed("6m", "gz_mpfv3", test_uptime)
        
        # 5. 再次检查处理判断
        logger.info("\n🔄 再次检查处理判断:")
        for task_type, data_type in [("1h", "PRE"), ("6m", "gz_mpfv3")]:
            should_process = should_process_task(task_type, data_type, test_uptime)
            logger.info(f"  {task_type}_{data_type} uptime {test_uptime}: {'需要处理' if should_process else '跳过'}")
        
        # 6. 测试新的uptime
        logger.info("\n🆕 测试新的uptime:")
        new_uptime = "202507131305"
        for task_type, data_type in [("1h", "PRE"), ("6m", "gz_mpfv3")]:
            should_process = should_process_task(task_type, data_type, new_uptime)
            logger.info(f"  {task_type}_{data_type} uptime {new_uptime}: {'需要处理' if should_process else '跳过'}")
        
        # 7. 显示最终状态
        logger.info("\n📈 最终状态:")
        stats = task_state_manager.get_statistics()
        logger.info(f"  总任务数: {stats['total_tasks']}")
        logger.info(f"  首次运行任务: {stats['first_run_tasks']}")
        logger.info(f"  已完成任务: {stats['completed_tasks']}")
        logger.info(f"  总执行次数: {stats['total_executions']}")
        
        return True
        
    except Exception as e:
        logger.error(f"演示失败: {e}")
        return False


def demo_uptime_formats():
    """演示uptime格式处理"""
    logger.info("=== 演示uptime格式处理 ===")
    
    try:
        from scheduler import WeatherTaskScheduler
        
        scheduler = WeatherTaskScheduler()
        
        # 测试各种uptime格式
        test_cases = [
            ("202507131304", "标准格式 YYYYMMDDHHMM"),
            ("20250713130400", "带秒格式 YYYYMMDDHHMMSS"),
            ("2507131304", "短格式 YYMMDDHHMM"),
            ("2025-07-13 13:04", "带分隔符格式"),
            ("2025/07/13 13:04:00", "带分隔符和秒"),
            ("invalid_format", "无效格式"),
        ]
        
        logger.info("🔧 uptime格式转换:")
        for input_uptime, description in test_cases:
            formatted = scheduler._format_uptime(input_uptime)
            status = "✅" if formatted else "❌"
            logger.info(f"  {status} {description}: {input_uptime} -> {formatted}")
        
        return True
        
    except Exception as e:
        logger.error(f"演示失败: {e}")
        return False


def demo_task_execution_flow():
    """演示完整的任务执行流程"""
    logger.info("=== 演示完整的任务执行流程 ===")
    
    try:
        from task_state_manager import task_state_manager, should_process_task, mark_task_completed
        from scheduler import WeatherTaskScheduler
        
        # 重置状态
        task_state_manager.reset_all_states()
        scheduler = WeatherTaskScheduler()
        
        # 模拟API响应
        mock_api_responses = [
            {"time": "202507131304", "url": "http://example.com/data1.tar.gz"},
            {"time": "202507131304", "url": "http://example.com/data1.tar.gz"},  # 重复
            {"time": "202507131310", "url": "http://example.com/data2.tar.gz"},  # 新数据
        ]
        
        task_type = "1h"
        data_type = "PRE"
        
        logger.info(f"🎯 模拟 {task_type}_{data_type} 任务执行流程:")
        
        for i, api_response in enumerate(mock_api_responses, 1):
            logger.info(f"\n--- 第 {i} 次API调用 ---")
            
            # 1. 格式化uptime
            raw_uptime = api_response["time"]
            formatted_uptime = scheduler._format_uptime(raw_uptime)
            logger.info(f"📅 API返回uptime: {raw_uptime} -> {formatted_uptime}")
            
            # 2. 检查是否需要处理
            should_process = should_process_task(task_type, data_type, formatted_uptime)
            logger.info(f"🤔 是否需要处理: {'是' if should_process else '否'}")
            
            if should_process:
                # 3. 模拟下载和处理
                logger.info(f"⬇️  开始下载: {api_response['url']}")
                logger.info("🔄 处理数据...")
                
                # 4. 标记完成
                mark_task_completed(task_type, data_type, formatted_uptime)
                logger.info(f"✅ 任务完成，uptime: {formatted_uptime}")
            else:
                logger.info("⏭️  跳过重复数据")
        
        # 显示最终状态
        logger.info(f"\n📊 {task_type}_{data_type} 最终状态:")
        state = task_state_manager.get_task_state(task_type, data_type)
        if state:
            logger.info(f"  最后处理uptime: {state.last_uptime}")
            logger.info(f"  总执行次数: {state.total_executions}")
            logger.info(f"  最后更新时间: {state.last_update}")
        
        return True
        
    except Exception as e:
        logger.error(f"演示失败: {e}")
        return False


def demo_multiple_tasks():
    """演示多任务并行状态管理"""
    logger.info("=== 演示多任务并行状态管理 ===")
    
    try:
        from task_state_manager import task_state_manager, should_process_task, mark_task_completed
        
        # 重置状态
        task_state_manager.reset_all_states()
        
        # 定义测试任务
        tasks = [
            ("1h", "PRE"),
            ("1h", "TEM"),
            ("1h", "WEATHER"),
            ("1h", "VIS"),
            ("6m", "gz_mpfv3")
        ]
        
        # 模拟不同时间的数据
        uptimes = ["202507131300", "202507131306", "202507131312"]
        
        logger.info("🔄 模拟多任务处理不同时间的数据:")
        
        for uptime in uptimes:
            logger.info(f"\n--- 处理uptime: {uptime} ---")
            
            for task_type, data_type in tasks:
                should_process = should_process_task(task_type, data_type, uptime)
                
                if should_process:
                    logger.info(f"✅ {task_type}_{data_type}: 处理")
                    mark_task_completed(task_type, data_type, uptime)
                else:
                    logger.info(f"⏭️  {task_type}_{data_type}: 跳过")
        
        # 显示所有任务状态
        logger.info("\n📊 所有任务最终状态:")
        summary = task_state_manager.get_states_summary()
        for task_key, state in summary.items():
            logger.info(f"  {task_key}:")
            logger.info(f"    最后uptime: {state['last_uptime']}")
            logger.info(f"    执行次数: {state['total_executions']}")
        
        # 显示统计信息
        stats = task_state_manager.get_statistics()
        logger.info(f"\n📈 统计信息:")
        logger.info(f"  总任务数: {stats['total_tasks']}")
        logger.info(f"  已完成任务: {stats['completed_tasks']}")
        logger.info(f"  总执行次数: {stats['total_executions']}")
        logger.info(f"  1小时任务: {stats['1h_tasks']}")
        logger.info(f"  6分钟任务: {stats['6m_tasks']}")
        
        return True
        
    except Exception as e:
        logger.error(f"演示失败: {e}")
        return False


async def main():
    """主演示函数"""
    logger.info("🎭 开始任务状态管理演示")
    logger.info("=" * 60)
    
    demos = [
        ("任务状态管理基础功能", demo_task_state_basics),
        ("uptime格式处理", demo_uptime_formats),
        ("完整任务执行流程", demo_task_execution_flow),
        ("多任务并行状态管理", demo_multiple_tasks),
    ]
    
    results = []
    for demo_name, demo_func in demos:
        try:
            logger.info(f"\n🎬 开始演示: {demo_name}")
            result = demo_func()
            results.append((demo_name, result))
            
            if result:
                logger.info(f"✅ {demo_name} 演示成功")
            else:
                logger.error(f"❌ {demo_name} 演示失败")
                
        except Exception as e:
            logger.error(f"❌ {demo_name} 演示异常: {e}")
            results.append((demo_name, False))
        
        logger.info("-" * 40)
    
    # 汇总结果
    logger.info("=" * 60)
    logger.info("📊 演示结果汇总:")
    
    all_passed = True
    for demo_name, passed in results:
        status = "✅ 成功" if passed else "❌ 失败"
        logger.info(f"  {demo_name}: {status}")
        if not passed:
            all_passed = False
    
    logger.info("=" * 60)
    if all_passed:
        logger.info("🎉 所有演示成功！任务状态管理系统运行正常。")
        logger.info("")
        logger.info("💡 关键特性:")
        logger.info("   ✅ 内存中跟踪每个任务的最后处理uptime")
        logger.info("   ✅ 防止重复处理相同uptime的数据")
        logger.info("   ✅ 支持多种uptime格式自动转换")
        logger.info("   ✅ 5个独立任务状态管理")
        logger.info("   ✅ 首次运行自动检测")
        logger.info("")
        logger.info("🚀 启动完整系统:")
        logger.info("   uv run python run_weather_scheduler.py")
        logger.info("")
        logger.info("🌐 查看任务状态:")
        logger.info("   http://localhost:8000/task-states")
    else:
        logger.error("💥 部分演示失败！请检查相关模块。")


if __name__ == "__main__":
    asyncio.run(main())
