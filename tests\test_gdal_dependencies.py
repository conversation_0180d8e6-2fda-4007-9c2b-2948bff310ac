#!/usr/bin/env python3
# coding: utf-8
"""
测试GDAL及相关地理空间依赖包的安装和导入
"""

import sys
import traceback


def test_gdal_import():
    """测试GDAL导入"""
    try:
        from osgeo import gdal, ogr, osr
        print("✅ GDAL导入成功")
        print(f"   GDAL版本: {gdal.VersionInfo()}")
        return True
    except ImportError as e:
        print(f"❌ GDAL导入失败: {e}")
        return False


def test_fiona_import():
    """测试Fiona导入"""
    try:
        import fiona
        print("✅ Fiona导入成功")
        print(f"   Fiona版本: {fiona.__version__}")
        return True
    except ImportError as e:
        print(f"❌ Fiona导入失败: {e}")
        return False


def test_pyproj_import():
    """测试pyproj导入"""
    try:
        import pyproj
        print("✅ pyproj导入成功")
        print(f"   pyproj版本: {pyproj.__version__}")
        return True
    except ImportError as e:
        print(f"❌ pyproj导入失败: {e}")
        return False


def test_rasterio_import():
    """测试rasterio导入"""
    try:
        import rasterio
        print("✅ rasterio导入成功")
        print(f"   rasterio版本: {rasterio.__version__}")
        return True
    except ImportError as e:
        print(f"❌ rasterio导入失败: {e}")
        return False


def test_geopandas_import():
    """测试geopandas导入"""
    try:
        import geopandas as gpd
        print("✅ geopandas导入成功")
        print(f"   geopandas版本: {gpd.__version__}")
        return True
    except ImportError as e:
        print(f"❌ geopandas导入失败: {e}")
        return False


def test_shapely_import():
    """测试shapely导入"""
    try:
        import shapely
        from shapely.geometry import Point
        print("✅ shapely导入成功")
        print(f"   shapely版本: {shapely.__version__}")
        
        # 测试基本功能
        point = Point(0, 0)
        print(f"   测试Point创建: {point}")
        return True
    except ImportError as e:
        print(f"❌ shapely导入失败: {e}")
        return False
    except Exception as e:
        print(f"❌ shapely功能测试失败: {e}")
        return False


def test_basic_functionality():
    """测试基本地理空间功能"""
    try:
        import geopandas as gpd
        from shapely.geometry import Point
        import pandas as pd
        
        # 创建简单的GeoDataFrame
        data = {
            'name': ['Point1', 'Point2'],
            'geometry': [Point(0, 0), Point(1, 1)]
        }
        gdf = gpd.GeoDataFrame(data, crs='EPSG:4326')
        
        print("✅ 基本地理空间功能测试成功")
        print(f"   创建了包含{len(gdf)}个点的GeoDataFrame")
        print(f"   CRS: {gdf.crs}")
        return True
    except Exception as e:
        print(f"❌ 基本地理空间功能测试失败: {e}")
        traceback.print_exc()
        return False


def main():
    """主测试函数"""
    print("🔍 开始测试地理空间依赖包...")
    print("=" * 50)
    
    tests = [
        test_gdal_import,
        test_fiona_import,
        test_pyproj_import,
        test_rasterio_import,
        test_geopandas_import,
        test_shapely_import,
        test_basic_functionality,
    ]
    
    results = []
    for test in tests:
        print()
        result = test()
        results.append(result)
    
    print("\n" + "=" * 50)
    print("📊 测试结果汇总:")
    
    passed = sum(results)
    total = len(results)
    
    if passed == total:
        print(f"✅ 所有测试通过 ({passed}/{total})")
        print("🎉 地理空间依赖包安装完成且功能正常！")
        return 0
    else:
        print(f"❌ 部分测试失败 ({passed}/{total})")
        print("⚠️  请检查失败的依赖包安装")
        return 1


if __name__ == "__main__":
    sys.exit(main())
